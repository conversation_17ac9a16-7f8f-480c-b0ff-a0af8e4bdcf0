{"project_id": "550e8400-e29b-41d4-a716-446655440000", "name": "Minimal Project Example", "version": "1.0.0", "phases": [{"id": "phase-001", "name": "Setup Phase", "order_index": 0, "steps": [{"id": "step-001", "name": "Initial Setup", "order_index": 0, "tasks": [{"id": "task-001", "name": "Create Repository", "order_index": 0, "assigned_agent": "shell"}, {"id": "task-002", "name": "Setup Backend", "order_index": 1, "assigned_agent": "backend"}, {"id": "task-003", "name": "Create Frontend", "order_index": 2, "assigned_agent": "frontend"}]}]}]}