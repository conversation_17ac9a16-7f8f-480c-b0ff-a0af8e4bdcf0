/**
 * IDE Types and Interfaces
 */

import { AgentRole } from './agents';

export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  size?: number;
  lastModified?: Date;
  children?: FileNode[];
  isExpanded?: boolean;
}

export interface EditorTab {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  active: boolean;
  modified: boolean;
  saved?: boolean;
}

export interface AgentAction {
  id: string;
  type: 'suggestion' | 'completion' | 'refactor' | 'fix' | 'generate';
  title: string;
  description: string;
  code?: string;
  position?: {
    line: number;
    column: number;
  };
  range?: {
    startLine: number;
    startColumn: number;
    endLine: number;
    endColumn: number;
  };
  confidence: number;
  agentRole: AgentRole;
  timestamp: Date;
}

export interface AIAssistantMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  agentRole?: AgentRole;
  isError?: boolean;
  metadata?: {
    taskId?: string;
    executionTime?: number;
    tokenCount?: number;
    codeGenerated?: boolean;
    filesModified?: string[];
  };
}

export interface AIChat {
  messages: AIAssistantMessage[];
  isLoading: boolean;
  context?: {
    currentFile?: string;
    selectedText?: string;
    projectType?: string;
  };
}

export interface ProjectPreview {
  type: 'web' | 'mobile' | 'desktop' | 'api';
  url?: string;
  status: 'loading' | 'ready' | 'error';
  lastUpdated: Date;
  error?: string;
}

export interface IDEState {
  openTabs: EditorTab[];
  activeTabId: string | null;
  sidebarVisible: boolean;
  rightPanelVisible: boolean;
  agentCollaborationVisible: boolean;
  theme: 'light' | 'dark';
  files: FileNode[];
  aiChat: AIChat;
  projectPreview: ProjectPreview | null;
  recentFiles?: string[];
  bookmarks?: string[];
  searchResults?: FileNode[];
}

export interface AICodeAssistanceRequest {
  type: 'completion' | 'explanation' | 'refactor' | 'debug' | 'generate';
  context: {
    filePath: string;
    language: string;
    selectedText?: string;
    cursorPosition?: {
      line: number;
      column: number;
    };
    surroundingCode?: string;
  };
  prompt: string;
  agentRole?: AgentRole;
}

export interface AICodeAssistanceResponse {
  id: string;
  type: AICodeAssistanceRequest['type'];
  suggestions: AgentAction[];
  explanation?: string;
  confidence: number;
  agentRole: AgentRole;
  executionTime: number;
}
