{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@monaco-editor/react": "^4.7.0", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/axios": "^0.9.36", "@types/d3": "^7.4.3", "@types/dompurify": "^3.0.5", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-router-dom": "^5.3.3", "@xyflow/react": "^12.8.3", "axios": "^1.11.0", "clsx": "^2.1.1", "d3": "^7.9.0", "dompurify": "^3.2.6", "lucide-react": "^0.539.0", "monaco-editor": "^0.52.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-router-dom": "^7.8.0", "react-scripts": "5.0.1", "tailwind-merge": "^3.3.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.4.31", "tailwindcss": "^3.4.17", "webpack-bundle-analyzer": "^4.10.2"}, "overrides": {"nth-check": "^2.0.1", "webpack-dev-server": "^4.0.0", "svgo": "^2.0.0"}}