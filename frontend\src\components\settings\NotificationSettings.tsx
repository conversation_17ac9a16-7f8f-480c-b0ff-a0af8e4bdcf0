/**
 * Notification Settings Component
 * Handles email, push, and in-app notification preferences
 */

import React from 'react';
import { Bell, Mail, Smartphone, MessageSquare, Shield, Megaphone } from 'lucide-react';
import { usePreferences } from '../../contexts/PreferencesContext';

const notificationOptions = [
  {
    key: 'email' as const,
    icon: Mail,
    title: 'Email Notifications',
    description: 'Receive notifications via email',
  },
  {
    key: 'push' as const,
    icon: Smartphone,
    title: 'Push Notifications',
    description: 'Receive browser push notifications',
  },
  {
    key: 'inApp' as const,
    icon: MessageSquare,
    title: 'In-App Notifications',
    description: 'Show notifications within the application',
  },
  {
    key: 'marketing' as const,
    icon: Megaphone,
    title: 'Marketing Communications',
    description: 'Receive updates about new features and products',
  },
  {
    key: 'security' as const,
    icon: Shield,
    title: 'Security Alerts',
    description: 'Important security-related notifications',
  },
];

export const NotificationSettings: React.FC = () => {
  const { preferences, updateSection } = usePreferences();
  const { notifications } = preferences;

  const handleToggle = (key: keyof typeof notifications) => {
    updateSection('notifications', {
      [key]: !notifications[key],
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Settings
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Control how and when you receive notifications
        </p>
      </div>

      <div className="space-y-6">
        {notificationOptions.map((option) => {
          const Icon = option.icon;
          const isEnabled = notifications[option.key];

          return (
            <div
              key={option.key}
              className="flex items-start justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div className="flex items-start gap-3">
                <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400 mt-0.5" />
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white">
                    {option.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {option.description}
                  </p>
                </div>
              </div>

              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isEnabled}
                  onChange={() => handleToggle(option.key)}
                  className="sr-only peer"
                />
                <div className={`
                  relative w-11 h-6 rounded-full peer transition-colors duration-200 ease-in-out
                  ${isEnabled
                    ? 'bg-indigo-600'
                    : 'bg-gray-200 dark:bg-gray-700'
                  }
                  peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800
                `}>
                  <div className={`
                    absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 transition-transform duration-200 ease-in-out
                    ${isEnabled ? 'translate-x-full border-white' : 'translate-x-0'}
                  `} />
                </div>
              </label>
            </div>
          );
        })}
      </div>

      {/* Additional Settings */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Notification Timing
        </h4>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Quiet Hours
            </label>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Pause non-urgent notifications during these hours
            </p>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                  Start Time
                </label>
                <input
                  type="time"
                  defaultValue="22:00"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                  End Time
                </label>
                <input
                  type="time"
                  defaultValue="08:00"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notification Frequency
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
              <option value="immediate">Immediate</option>
              <option value="digest-hourly">Hourly Digest</option>
              <option value="digest-daily">Daily Digest</option>
              <option value="digest-weekly">Weekly Digest</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};
