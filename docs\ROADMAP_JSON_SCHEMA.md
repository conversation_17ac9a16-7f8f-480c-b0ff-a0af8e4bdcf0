# Roadmap JSON Schema Implementation

## Overview

The `roadmap.json` schema defines the structure for project roadmaps in the AI Coding Agent system. This implementation provides comprehensive validation, type safety, and standardization for roadmap data across the entire platform.

## 🏗️ Schema Structure

### Core Schema File
- **Location**: `schemas/roadmap.json`
- **Standard**: JSON Schema Draft 7
- **Purpose**: Validate roadmap data structure and enforce consistency

### Key Components

#### 1. **Root Roadmap Object**
```json
{
  "project_id": "uuid",
  "name": "string",
  "version": "semantic version (x.y.z)",
  "description": "optional string",
  "status": "pending|in_progress|completed|blocked|cancelled",
  "phases": [...]
}
```

#### 2. **Phase Definition**
```json
{
  "id": "uuid",
  "name": "string",
  "description": "optional string",
  "order_index": "integer >= 0",
  "status": "pending|in_progress|completed|blocked|cancelled",
  "dependencies": ["array of phase UUIDs"],
  "estimated_duration": "human-readable string",
  "steps": [...]
}
```

#### 3. **Step Definition**
```json
{
  "id": "uuid",
  "name": "string",
  "description": "optional string",
  "order_index": "integer >= 0",
  "status": "pending|in_progress|completed|blocked|cancelled",
  "dependencies": ["array of step UUIDs"],
  "estimated_duration": "human-readable string",
  "tasks": [...]
}
```

#### 4. **Task Definition**
```json
{
  "id": "uuid",
  "name": "string",
  "description": "optional string",
  "order_index": "integer >= 0",
  "status": "pending|in_progress|completed|failed|blocked|cancelled",
  "assigned_agent": "architect|frontend|backend|shell|issue_fix",
  "dependencies": ["array of task UUIDs"],
  "estimated_duration": "human-readable string",
  "artifacts": [...]
}
```

#### 5. **Artifact Definition**
```json
{
  "type": "code|documentation|configuration|test|deployment|design",
  "name": "string",
  "path": "optional file path",
  "content": "optional content string",
  "description": "optional description",
  "size": "optional size in bytes",
  "checksum": "optional SHA-256 checksum"
}
```

## 🔧 Implementation Components

### 1. Schema Validation Service
**File**: `src/ai_coding_agent/services/schema_validation.py`

**Key Features**:
- JSON Schema validation using `jsonschema` library
- Fallback validation when `jsonschema` is not available
- Comprehensive error reporting
- Schema caching for performance

**Methods**:
```python
# Validate roadmap data
is_valid, errors = schema_validator.validate_roadmap(roadmap_data)

# Strict validation (raises HTTPException on failure)
schema_validator.validate_roadmap_strict(roadmap_data)

# Get schema information
info = schema_validator.get_schema_info("roadmap")

# List available schemas
schemas = schema_validator.list_available_schemas()
```

### 2. API Endpoints
**File**: `src/ai_coding_agent/routers/roadmap.py`

**New Endpoints**:
```
POST /api/v1/validate/roadmap - Validate roadmap data
GET /api/v1/schema/roadmap/info - Get schema information
GET /api/v1/schema/list - List available schemas
```

### 3. Integration with Roadmap Service
**File**: `src/ai_coding_agent/services/roadmap.py`

**Enhanced Methods**:
- `_validate_roadmap_schema()` - Internal validation during creation
- `validate_roadmap_json()` - Public validation method

## 📋 Validation Rules

### Required Fields
- **Roadmap**: `project_id`, `name`, `version`, `phases`
- **Phase**: `id`, `name`, `order_index`, `steps`
- **Step**: `id`, `name`, `order_index`, `tasks`
- **Task**: `id`, `name`, `order_index`, `assigned_agent`
- **Artifact**: `type`, `name`

### Format Validation
- **UUIDs**: Standard UUID format (36 characters)
- **Version**: Semantic versioning (major.minor.patch)
- **Status**: Predefined enum values
- **Agent Types**: Valid agent assignments
- **Order Index**: Non-negative integers

### Structural Validation
- **Non-empty Arrays**: Phases, steps, and tasks must contain at least one item
- **Dependency References**: All dependency UUIDs must be valid
- **Hierarchical Consistency**: Proper parent-child relationships

## 🧪 Testing

### Test File
**Location**: `test_roadmap_schema.py`

**Test Coverage**:
1. **Schema File Structure** - Validates schema file exists and is well-formed
2. **Sample Roadmap Validation** - Tests against provided sample
3. **Invalid Data Rejection** - Ensures invalid data is properly rejected
4. **Minimal Valid Data** - Tests with minimal required fields
5. **Format Validation** - Tests version, UUID, and enum formats
6. **Complex Structure** - Tests with full feature set

**Run Tests**:
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Run schema tests
python test_roadmap_schema.py
```

## 📖 Usage Examples

### 1. Basic Roadmap Creation
```json
{
  "project_id": "550e8400-e29b-41d4-a716-************",
  "name": "Simple Project",
  "version": "1.0.0",
  "phases": [
    {
      "id": "phase-001",
      "name": "Setup",
      "order_index": 0,
      "steps": [
        {
          "id": "step-001",
          "name": "Initialize",
          "order_index": 0,
          "tasks": [
            {
              "id": "task-001",
              "name": "Create Repository",
              "order_index": 0,
              "assigned_agent": "shell"
            }
          ]
        }
      ]
    }
  ]
}
```

### 2. Validation in Code
```python
from src.ai_coding_agent.services.schema_validation import schema_validator

# Validate roadmap data
roadmap_data = {...}  # Your roadmap JSON
is_valid, errors = schema_validator.validate_roadmap(roadmap_data)

if not is_valid:
    print("Validation errors:")
    for error in errors:
        print(f"  - {error}")
```

### 3. API Validation
```bash
# Validate roadmap via API
curl -X POST "http://localhost:8000/api/v1/validate/roadmap" \
  -H "Content-Type: application/json" \
  -d @my_roadmap.json
```

## 🔍 Schema Features

### Extensibility
- **Project Metadata**: Flexible metadata object for project-specific data
- **Custom Properties**: Additional properties allowed in metadata sections
- **Future Compatibility**: Schema designed for backward compatibility

### Validation Levels
- **Strict Mode**: Full JSON Schema validation with detailed error messages
- **Basic Mode**: Fallback validation for essential structure
- **Custom Validation**: Additional business logic validation in service layer

### Performance Optimization
- **Schema Caching**: Loaded schemas are cached in memory
- **Lazy Loading**: Schemas loaded only when needed
- **Efficient Validation**: Optimized validation paths for common cases

## 🚀 Integration Points

### 1. Project Creation
- Automatic validation during project creation with roadmap
- Schema validation integrated into `RoadmapService.create_project()`

### 2. Roadmap Updates
- Validation on roadmap modifications
- Ensures data integrity throughout lifecycle

### 3. Import/Export
- Validates imported roadmap files
- Ensures exported data meets schema requirements

### 4. API Consistency
- All roadmap endpoints use schema-validated data
- Consistent error reporting across API

## 📊 Benefits

### Data Integrity
- **Consistent Structure**: All roadmaps follow the same format
- **Type Safety**: Proper data types enforced
- **Required Fields**: Essential data always present

### Developer Experience
- **Clear Documentation**: Schema serves as API documentation
- **IDE Support**: JSON Schema enables autocomplete and validation
- **Error Messages**: Detailed validation feedback

### System Reliability
- **Input Validation**: Prevents invalid data from entering system
- **Predictable Structure**: Code can rely on consistent data format
- **Future-Proof**: Schema evolution supports system growth

## 🔧 Configuration

### Dependencies
```bash
# Optional but recommended for full validation
pip install jsonschema
```

### Environment Variables
```env
# Schema validation settings (optional)
SCHEMA_VALIDATION_STRICT=true
SCHEMA_CACHE_SIZE=100
```

### File Structure
```
schemas/
├── roadmap.json          # Main roadmap schema
└── (future schemas)      # Additional schemas as needed

examples/
├── sample_roadmap.json   # Complete example
└── minimal_roadmap.json  # Minimal valid example
```

This implementation provides a robust foundation for roadmap data management, ensuring consistency, reliability, and extensibility across the AI Coding Agent platform.
