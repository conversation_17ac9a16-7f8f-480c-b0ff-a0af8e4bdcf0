"""
Simple Integration Test for Dependency Engine
Test that all components can be imported and work together.
"""

def test_imports():
    """Test that all critical components can be imported."""
    try:
        # Test model imports
        from ai_coding_agent.models import (
            DependencyCheckResult, BlockingDependency, DependencyType,
            DependencyCheckStatus, OverrideLevel, PhaseProgressionResult
        )
        print("✅ Dependency models imported successfully")

        # Test service imports
        from ai_coding_agent.services.dependency_engine import DependencyEngine
        print("✅ DependencyEngine service imported successfully")

        from ai_coding_agent.services.roadmap import RoadmapService
        print("✅ RoadmapService imported successfully")

        # Test that we can create instances
        result = DependencyCheckResult(
            entity_id="test",
            entity_type=DependencyType.TASK,
            entity_name="Test Task",
            status=DependencyCheckStatus.CAN_START,
            can_start=True,
            message="Test message"
        )
        print("✅ DependencyCheckResult instance created successfully")

        blocking_dep = BlockingDependency(
            dependency_id="dep1",
            dependency_type=DependencyType.TASK,
            dependency_name="Dependency Task",
            current_status="pending",
            blocking_reason="Test blocking reason"
        )
        print("✅ BlockingDependency instance created successfully")

        print("\n🎉 All imports and basic functionality working!")
        return True

    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False


def test_dependency_logic():
    """Test basic dependency logic without database."""
    try:
        from ai_coding_agent.models import (
            DependencyCheckResult, DependencyType, DependencyCheckStatus, OverrideLevel
        )

        # Test basic dependency check result
        result = DependencyCheckResult(
            entity_id="task-123",
            entity_type=DependencyType.TASK,
            entity_name="Sample Task",
            status=DependencyCheckStatus.BLOCKED,
            can_start=False,
            blocking_dependencies=[],
            warnings=["Sample warning"],
            override_level=OverrideLevel.DEVELOPER,
            message="Task is blocked by dependencies"
        )

        # Verify the result
        assert result.entity_id == "task-123"
        assert result.entity_type == DependencyType.TASK
        assert result.can_start is False
        assert result.status == DependencyCheckStatus.BLOCKED
        assert result.override_level == OverrideLevel.DEVELOPER
        assert len(result.warnings) == 1

        print("✅ Dependency logic test passed")
        return True

    except Exception as e:
        print(f"❌ Dependency logic test failed: {e}")
        return False


def test_enum_values():
    """Test that all enum values are working correctly."""
    try:
        from ai_coding_agent.models import (
            DependencyType, DependencyCheckStatus, OverrideLevel
        )

        # Test DependencyType enum
        assert DependencyType.TASK == "task"
        assert DependencyType.STEP == "step"
        assert DependencyType.PHASE == "phase"

        # Test DependencyCheckStatus enum
        assert DependencyCheckStatus.CAN_START == "can_start"
        assert DependencyCheckStatus.BLOCKED == "blocked"
        assert DependencyCheckStatus.WARNING == "warning"

        # Test OverrideLevel enum
        assert OverrideLevel.NONE == "none"
        assert OverrideLevel.WARNING == "warning"
        assert OverrideLevel.DEVELOPER == "developer"
        assert OverrideLevel.ADMIN == "admin"

        print("✅ Enum values test passed")
        return True

    except Exception as e:
        print(f"❌ Enum values test failed: {e}")
        return False


def test_pydantic_validation():
    """Test Pydantic model validation."""
    try:
        from ai_coding_agent.models import (
            DependencyCheckResult, DependencyType, DependencyCheckStatus
        )

        # Test valid model creation
        result = DependencyCheckResult(
            entity_id="test-id",
            entity_type=DependencyType.TASK,
            entity_name="Test Task",
            status=DependencyCheckStatus.CAN_START,
            can_start=True,
            message="Test message"
        )

        # Test model serialization
        result_dict = result.model_dump()
        assert result_dict["entity_id"] == "test-id"
        assert result_dict["entity_type"] == "task"
        assert result_dict["can_start"] is True

        # Test model deserialization
        new_result = DependencyCheckResult(**result_dict)
        assert new_result.entity_id == result.entity_id
        assert new_result.can_start == result.can_start

        print("✅ Pydantic validation test passed")
        return True

    except Exception as e:
        print(f"❌ Pydantic validation test failed: {e}")
        return False


def main():
    """Run all integration tests."""
    print("🚀 Starting Phase B2 Dependency Engine Integration Tests\n")

    tests = [
        test_imports,
        test_dependency_logic,
        test_enum_values,
        test_pydantic_validation
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        print(f"\n📋 Running {test.__name__}...")
        if test():
            passed += 1
        print()

    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED! Phase B2 Dependency Engine is working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
