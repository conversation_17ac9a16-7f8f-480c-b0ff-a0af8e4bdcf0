"""
LTKB Document Management API Endpoints.

This module provides FastAPI endpoints for managing LTKB (Long-Term Knowledge Base)
documents, including upload, search, retrieval, and organization.
"""

import os
import uuid
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from enum import Enum

from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Query
from pydantic import BaseModel, Field, ConfigDict

from ..config import settings
from ..services.vector_backup import VectorDBBackup, create_vector_db_backup, restore_vector_db_backup

router = APIRouter(prefix="/api/v1/ltkb", tags=["LTKB"])


class DocumentType(str, Enum):
    """Types of documents in LTKB."""
    TEMPLATE = "template"
    PATTERN = "pattern"
    COMPONENT = "component"
    WORKFLOW = "workflow"
    DOCUMENTATION = "documentation"
    EXAMPLE = "example"
    CONFIGURATION = "configuration"


class DocumentStatus(str, Enum):
    """Status of documents in LTKB."""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DEPRECATED = "deprecated"
    DRAFT = "draft"


class DocumentMetadata(BaseModel):
    """Metadata for LTKB documents."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(description="Document title")
    description: str = Field(description="Document description")
    document_type: DocumentType = Field(description="Type of document")
    status: DocumentStatus = Field(default=DocumentStatus.ACTIVE)

    # Categorization
    tags: List[str] = Field(default_factory=list, description="Document tags")
    technology_stack: List[str] = Field(default_factory=list, description="Related technologies")
    use_cases: List[str] = Field(default_factory=list, description="Applicable use cases")
    complexity_level: str = Field(default="intermediate", description="beginner|intermediate|advanced|expert")

    # File information
    file_path: str = Field(description="Relative path to the document file")
    file_size: int = Field(description="File size in bytes")
    file_format: str = Field(description="File format/extension")

    # Versioning
    version: str = Field(default="1.0.0", description="Document version")
    author: str = Field(description="Document author")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Usage tracking
    usage_count: int = Field(default=0, description="Number of times used")
    last_used: Optional[datetime] = Field(default=None, description="Last usage timestamp")

    # Relationships
    dependencies: List[str] = Field(default_factory=list, description="Document IDs this depends on")
    related_documents: List[str] = Field(default_factory=list, description="Related document IDs")

    model_config = ConfigDict(use_enum_values=True)


class DocumentSearchQuery(BaseModel):
    """Search query for LTKB documents."""

    query: str = Field(description="Search query text")
    document_types: Optional[List[DocumentType]] = Field(default=None, description="Filter by document types")
    tags: Optional[List[str]] = Field(default=None, description="Filter by tags")
    technology_stack: Optional[List[str]] = Field(default=None, description="Filter by technologies")
    complexity_level: Optional[str] = Field(default=None, description="Filter by complexity")
    limit: int = Field(default=20, ge=1, le=100, description="Maximum results to return")
    include_content: bool = Field(default=False, description="Include document content in results")


class DocumentResponse(BaseModel):
    """Response model for document operations."""

    metadata: DocumentMetadata
    content: Optional[str] = Field(default=None, description="Document content if requested")
    relevance_score: Optional[float] = Field(default=None, description="Search relevance score")


class LTKBStats(BaseModel):
    """Statistics about the LTKB."""

    total_documents: int
    documents_by_type: Dict[str, int]
    total_size: int
    most_used_documents: List[DocumentMetadata]
    recent_additions: List[DocumentMetadata]
    technology_distribution: Dict[str, int]


class LTKBService:
    """Service class for LTKB operations."""

    def __init__(self):
        self.base_path = Path("ltkb")
        self.systems_path = self.base_path / "systems"
        self.projects_path = self.base_path / "projects"
        self.metadata_file = self.base_path / "metadata.json"

        # Ensure directories exist
        self.base_path.mkdir(exist_ok=True)
        self.systems_path.mkdir(exist_ok=True)
        self.projects_path.mkdir(exist_ok=True)

        # Load or initialize metadata
        self.metadata_index: Dict[str, DocumentMetadata] = {}
        self._load_metadata_index()

    def _load_metadata_index(self):
        """Load document metadata index from file."""
        if self.metadata_file.exists():
            import json
            try:
                with open(self.metadata_file, 'r') as f:
                    data = json.load(f)
                    for doc_id, metadata_dict in data.items():
                        self.metadata_index[doc_id] = DocumentMetadata(**metadata_dict)
            except Exception as e:
                print(f"Error loading metadata index: {e}")

    def _save_metadata_index(self):
        """Save document metadata index to file."""
        import json
        try:
            data = {
                doc_id: metadata.model_dump()
                for doc_id, metadata in self.metadata_index.items()
            }
            with open(self.metadata_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving metadata index: {e}")

    async def store_document(
        self,
        file: UploadFile,
        metadata: DocumentMetadata,
        collection: str = "systems"
    ) -> DocumentMetadata:
        """Store a document in LTKB with metadata."""

        # Determine storage path
        if collection == "systems":
            storage_dir = self.systems_path / metadata.document_type.value
        else:
            storage_dir = self.projects_path / collection

        storage_dir.mkdir(parents=True, exist_ok=True)

        # Generate file path
        file_extension = Path(file.filename or "document.txt").suffix
        file_name = f"{metadata.id}{file_extension}"
        file_path = storage_dir / file_name

        # Save file
        with open(file_path, 'wb') as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Update metadata
        metadata.file_path = str(file_path.relative_to(self.base_path))
        metadata.file_size = file_path.stat().st_size
        metadata.file_format = file_extension
        metadata.updated_at = datetime.now()

        # Store in index
        self.metadata_index[metadata.id] = metadata
        self._save_metadata_index()

        return metadata

    async def get_document(self, document_id: str, include_content: bool = False) -> Optional[DocumentResponse]:
        """Retrieve a document by ID."""

        if document_id not in self.metadata_index:
            return None

        metadata = self.metadata_index[document_id]
        content = None

        if include_content:
            file_path = self.base_path / metadata.file_path
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                except UnicodeDecodeError:
                    # Try binary read for non-text files
                    with open(file_path, 'rb') as f:
                        content = f"<Binary file: {metadata.file_format}>"

        # Update usage tracking
        metadata.usage_count += 1
        metadata.last_used = datetime.now()
        self._save_metadata_index()

        return DocumentResponse(
            metadata=metadata,
            content=content
        )

    async def search_documents(self, query: DocumentSearchQuery) -> List[DocumentResponse]:
        """Search documents in LTKB."""

        results = []
        query_lower = query.query.lower()

        for doc_id, metadata in self.metadata_index.items():
            # Basic text matching
            relevance_score = 0.0

            # Title and description matching
            if query_lower in metadata.title.lower():
                relevance_score += 0.5
            if query_lower in metadata.description.lower():
                relevance_score += 0.3

            # Tag matching
            for tag in metadata.tags:
                if query_lower in tag.lower():
                    relevance_score += 0.2

            # Technology stack matching
            for tech in metadata.technology_stack:
                if query_lower in tech.lower():
                    relevance_score += 0.2

            # Apply filters
            if query.document_types and metadata.document_type not in query.document_types:
                continue

            if query.tags and not any(tag in metadata.tags for tag in query.tags):
                continue

            if query.technology_stack and not any(
                tech in metadata.technology_stack for tech in query.technology_stack
            ):
                continue

            if query.complexity_level and metadata.complexity_level != query.complexity_level:
                continue

            # Only include results with some relevance
            if relevance_score > 0:
                content = None
                if query.include_content:
                    doc_response = await self.get_document(doc_id, include_content=True)
                    if doc_response:
                        content = doc_response.content

                results.append(DocumentResponse(
                    metadata=metadata,
                    content=content,
                    relevance_score=relevance_score
                ))

        # Sort by relevance and limit results
        results.sort(key=lambda x: x.relevance_score or 0, reverse=True)
        return results[:query.limit]

    async def update_document_metadata(self, document_id: str, updates: Dict[str, Any]) -> Optional[DocumentMetadata]:
        """Update document metadata."""

        if document_id not in self.metadata_index:
            return None

        metadata = self.metadata_index[document_id]

        # Update allowed fields
        for field, value in updates.items():
            if hasattr(metadata, field) and field not in ['id', 'created_at', 'file_path']:
                setattr(metadata, field, value)

        metadata.updated_at = datetime.now()
        self._save_metadata_index()

        return metadata

    async def delete_document(self, document_id: str) -> bool:
        """Delete a document from LTKB."""

        if document_id not in self.metadata_index:
            return False

        metadata = self.metadata_index[document_id]
        file_path = self.base_path / metadata.file_path

        # Delete file if it exists
        if file_path.exists():
            file_path.unlink()

        # Remove from index
        del self.metadata_index[document_id]
        self._save_metadata_index()

        return True

    async def get_stats(self) -> LTKBStats:
        """Get LTKB statistics."""

        documents_by_type = {}
        technology_distribution = {}
        total_size = 0

        for metadata in self.metadata_index.values():
            # Count by type
            doc_type = metadata.document_type.value
            documents_by_type[doc_type] = documents_by_type.get(doc_type, 0) + 1

            # Technology distribution
            for tech in metadata.technology_stack:
                technology_distribution[tech] = technology_distribution.get(tech, 0) + 1

            # Total size
            total_size += metadata.file_size

        # Most used documents
        most_used = sorted(
            self.metadata_index.values(),
            key=lambda x: x.usage_count,
            reverse=True
        )[:10]

        # Recent additions
        recent = sorted(
            self.metadata_index.values(),
            key=lambda x: x.created_at,
            reverse=True
        )[:10]

        return LTKBStats(
            total_documents=len(self.metadata_index),
            documents_by_type=documents_by_type,
            total_size=total_size,
            most_used_documents=most_used,
            recent_additions=recent,
            technology_distribution=technology_distribution
        )

    async def backup_documents(self):
        """Create a backup of all documents and metadata."""

        # Create backup directory
        backup_dir = self.base_path / "backup"
        backup_dir.mkdir(exist_ok=True)

        # Backup metadata
        metadata_backup_file = backup_dir / "metadata_backup.json"
        import json
        try:
            with open(metadata_backup_file, 'w') as f:
                json.dump(self.metadata_index, f, indent=2, default=str)
        except Exception as e:
            print(f"Error backing up metadata: {e}")

        # Backup files
        for metadata in self.metadata_index.values():
            file_path = self.base_path / metadata.file_path
            if file_path.exists():
                try:
                    # Copy file to backup location
                    shutil.copy2(file_path, backup_dir / file_path.name)
                except Exception as e:
                    print(f"Error backing up file {file_path.name}: {e}")

        # Create vector database backup
        create_vector_db_backup()


# Global service instance
ltkb_service = LTKBService()


# API Endpoints

@router.post("/documents", response_model=DocumentMetadata)
async def upload_document(
    file: UploadFile = File(...),
    title: str = Query(..., description="Document title"),
    description: str = Query(..., description="Document description"),
    document_type: DocumentType = Query(..., description="Document type"),
    author: str = Query(..., description="Document author"),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    technology_stack: Optional[str] = Query(None, description="Comma-separated technologies"),
    complexity_level: str = Query("intermediate", description="Complexity level"),
    collection: str = Query("systems", description="Collection to store in")
):
    """Upload a new document to LTKB."""

    metadata = DocumentMetadata(
        title=title,
        description=description,
        document_type=document_type,
        author=author,
        tags=tags.split(",") if tags else [],
        technology_stack=technology_stack.split(",") if technology_stack else [],
        complexity_level=complexity_level,
        file_path="",  # Will be set by service
        file_size=0,   # Will be set by service
        file_format="" # Will be set by service
    )

    return await ltkb_service.store_document(file, metadata, collection)


@router.get("/documents/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    include_content: bool = Query(False, description="Include document content")
):
    """Retrieve a document by ID."""

    document = await ltkb_service.get_document(document_id, include_content)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    return document


@router.post("/search", response_model=List[DocumentResponse])
async def search_documents(query: DocumentSearchQuery):
    """Search documents in LTKB."""

    return await ltkb_service.search_documents(query)


@router.put("/documents/{document_id}", response_model=DocumentMetadata)
async def update_document_metadata(
    document_id: str,
    updates: Dict[str, Any]
):
    """Update document metadata."""

    metadata = await ltkb_service.update_document_metadata(document_id, updates)
    if not metadata:
        raise HTTPException(status_code=404, detail="Document not found")

    return metadata


@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """Delete a document from LTKB."""

    success = await ltkb_service.delete_document(document_id)
    if not success:
        raise HTTPException(status_code=404, detail="Document not found")

    return {"message": "Document deleted successfully"}


@router.get("/stats", response_model=LTKBStats)
async def get_ltkb_stats():
    """Get LTKB statistics."""

    return await ltkb_service.get_stats()


@router.get("/documents", response_model=List[DocumentMetadata])
async def list_documents(
    document_type: Optional[DocumentType] = Query(None, description="Filter by document type"),
    status: Optional[DocumentStatus] = Query(None, description="Filter by status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum documents to return"),
    offset: int = Query(0, ge=0, description="Number of documents to skip")
):
    """List documents with optional filtering."""

    documents = list(ltkb_service.metadata_index.values())

    # Apply filters
    if document_type:
        documents = [d for d in documents if d.document_type == document_type]

    if status:
        documents = [d for d in documents if d.status == status]

    # Sort by updated_at descending
    documents.sort(key=lambda x: x.updated_at, reverse=True)

    # Apply pagination
    return documents[offset:offset + limit]


# Add backup endpoints

@router.post("/backup")
async def create_backup(
    backup_name: Optional[str] = Query(None, description="Custom backup name")
):
    """Create a backup of the vector database and LTKB data."""
    try:
        backup_path = create_vector_db_backup(backup_name)
        return {"message": "Backup created successfully", "backup_path": backup_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backup failed: {str(e)}")


@router.get("/backups")
async def list_backups():
    """List available backups."""
    try:
        from ..services.vector_db import get_vector_db
        from ..services.vector_backup import VectorDBBackup

        vector_db = get_vector_db()
        backup_system = VectorDBBackup(vector_db)
        backups = backup_system.list_backups()

        return {"backups": backups}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list backups: {str(e)}")


@router.post("/restore")
async def restore_backup(
    backup_path: str = Query(..., description="Path to backup file"),
    confirm: bool = Query(False, description="Confirm restore operation")
):
    """Restore from a backup file."""
    try:
        success = restore_vector_db_backup(backup_path, confirm)
        if success:
            return {"message": "Backup restored successfully"}
        else:
            raise HTTPException(status_code=400, detail="Restore operation failed or cancelled")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Restore failed: {str(e)}")
