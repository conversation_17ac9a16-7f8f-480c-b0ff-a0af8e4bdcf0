/**
 * Task Node Component for ReactFlow
 *
 * Displays individual tasks in the collaboration graph
 */

import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from '@xyflow/react';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { TaskInfo, TaskStatus } from '../../types/agents';

interface TaskNodeData {
  task: TaskInfo;
  progress: number;
}

const getStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case TaskStatus.COMPLETED:
      return CheckCircle;
    case TaskStatus.IN_PROGRESS:
      return Play;
    case TaskStatus.PENDING:
      return Clock;
    case TaskStatus.FAILED:
      return AlertCircle;
    case TaskStatus.VERIFYING:
      return RotateCcw;
    case TaskStatus.FIXING:
      return Pause;
    default:
      return Clock;
  }
};

const getStatusColor = (status: TaskStatus) => {
  switch (status) {
    case TaskStatus.COMPLETED:
      return 'text-green-600 bg-green-100';
    case TaskStatus.IN_PROGRESS:
      return 'text-blue-600 bg-blue-100';
    case TaskStatus.PENDING:
      return 'text-gray-600 bg-gray-100';
    case TaskStatus.FAILED:
      return 'text-red-600 bg-red-100';
    case TaskStatus.VERIFYING:
      return 'text-yellow-600 bg-yellow-100';
    case TaskStatus.FIXING:
      return 'text-orange-600 bg-orange-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

const getProgressColor = (status: TaskStatus) => {
  switch (status) {
    case TaskStatus.COMPLETED:
      return 'bg-green-500';
    case TaskStatus.IN_PROGRESS:
      return 'bg-blue-500';
    case TaskStatus.FAILED:
      return 'bg-red-500';
    case TaskStatus.VERIFYING:
      return 'bg-yellow-500';
    case TaskStatus.FIXING:
      return 'bg-orange-500';
    default:
      return 'bg-gray-500';
  }
};

const TaskNodeComponent: React.FC<NodeProps> = ({ data }) => {
  const nodeData = data as unknown as TaskNodeData;
  const { task, progress } = nodeData;
  const StatusIcon = getStatusIcon(task.status);
  const statusColor = getStatusColor(task.status);
  const progressColor = getProgressColor(task.status);

  const formatDuration = (startTime: Date, endTime?: Date) => {
    const end = endTime || new Date();
    const diffMs = end.getTime() - startTime.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);

    if (diffMins > 0) {
      return `${diffMins}m ${diffSecs % 60}s`;
    }
    return `${diffSecs}s`;
  };

  return (
    <div className="relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-md min-w-64 max-w-80 p-4">
      {/* Input/Output Handles */}
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />

      {/* Task Header */}
      <div className="flex items-start space-x-3 mb-3">
        <div className={`p-2 rounded-lg ${statusColor} flex-shrink-0`}>
          <StatusIcon size={16} />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 dark:text-white text-sm leading-tight">
            {task.description}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Task ID: {task.id}
          </p>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
            Progress
          </span>
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            {progress}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${progressColor}`}
            style={{ width: `${progress}%` }}
          >
            {task.status === TaskStatus.IN_PROGRESS && (
              <div className="h-full w-full rounded-full animate-pulse bg-white bg-opacity-30"></div>
            )}
          </div>
        </div>
      </div>

      {/* Task Details */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600 dark:text-gray-400">Assigned to:</span>
          <span className="font-medium text-gray-900 dark:text-white capitalize">
            {task.assignedAgent}
          </span>
        </div>

        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600 dark:text-gray-400">Duration:</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {formatDuration(task.startTime, task.endTime)}
          </span>
        </div>

        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600 dark:text-gray-400">Status:</span>
          <span className={`font-medium capitalize ${
            task.status === TaskStatus.COMPLETED ? 'text-green-600' :
            task.status === TaskStatus.IN_PROGRESS ? 'text-blue-600' :
            task.status === TaskStatus.FAILED ? 'text-red-600' :
            'text-gray-600'
          }`}>
            {task.status.replace('_', ' ')}
          </span>
        </div>
      </div>

      {/* Dependencies */}
      {task.dependencies && task.dependencies.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
          <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Dependencies:</p>
          <div className="flex flex-wrap gap-1">
            {task.dependencies.slice(0, 2).map((dep: string, index: number) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded text-gray-600 dark:text-gray-300"
              >
                {dep}
              </span>
            ))}
            {task.dependencies.length > 2 && (
              <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded text-gray-500">
                +{task.dependencies.length - 2}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {task.error && (
        <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
          <p className="text-xs text-red-600 dark:text-red-400 font-medium">Error:</p>
          <p className="text-xs text-red-500 dark:text-red-300 mt-1">
            {task.error}
          </p>
        </div>
      )}

      {/* Activity Indicator for In-Progress Tasks */}
      {task.status === TaskStatus.IN_PROGRESS && (
        <div className="absolute -top-1 -right-1">
          <div className="w-3 h-3 bg-blue-500 rounded-full animate-ping"></div>
          <div className="absolute top-0 right-0 w-3 h-3 bg-blue-500 rounded-full"></div>
        </div>
      )}
    </div>
  );
};

export default TaskNodeComponent;
