import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  supabaseAuthService,
  AuthUser,
  LoginCredentials,
  RegisterCredentials,
  UpdateProfileData,
  ChangePasswordData
} from '../services/supabaseAuth';
import { queryKeys } from '../lib/queryClient';

// Query for current user session
export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.user,
    queryFn: async (): Promise<AuthUser | null> => {
      const session = await supabaseAuthService.getCurrentSession();
      if (!session?.user) return null;

      // Get user profile data
      const profile = await supabaseAuthService.getUserProfile(session.user.id);
      if (!profile) return null;

      return {
        id: session.user.id,
        email: session.user.email || '',
        username: profile.username,
        created_at: session.user.created_at,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on auth errors
      if (error?.status === 401) return false;
      return failureCount < 2;
    },
  });
};

// Mutation for user login
export const useLogin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (credentials: LoginCredentials): Promise<AuthUser> => {
      const { user, error } = await supabaseAuthService.signIn(credentials);
      if (error || !user) {
        throw new Error(error || 'Login failed');
      }
      return user;
    },
    onSuccess: (user: AuthUser) => {
      // Update the user cache
      queryClient.setQueryData(queryKeys.user, user);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard });
    },
    onError: (error) => {
      console.error('Login failed:', error);
      // Clear any cached user data on login failure
      queryClient.setQueryData(queryKeys.user, null);
    },
  });
};

// Mutation for user registration
export const useRegister = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (credentials: RegisterCredentials): Promise<AuthUser> => {
      const { user, error } = await supabaseAuthService.signUp(credentials);
      if (error || !user) {
        throw new Error(error || 'Registration failed');
      }
      return user;
    },
    onSuccess: (user: AuthUser) => {
      // Update the user cache
      queryClient.setQueryData(queryKeys.user, user);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard });
    },
    onError: (error) => {
      console.error('Registration failed:', error);
    },
  });
};

// Mutation for user logout
export const useLogout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (): Promise<void> => {
      await supabaseAuthService.signOut();
    },
    onSuccess: () => {
      // Clear all user-related cache
      queryClient.setQueryData(queryKeys.user, null);
      queryClient.removeQueries({ queryKey: queryKeys.dashboard });
      queryClient.removeQueries({ queryKey: queryKeys.userProfile('') });

      // Clear all cached data for security
      queryClient.clear();
    },
    onError: (error) => {
      console.error('Logout failed:', error);
      // Even if logout fails, clear local cache for security
      queryClient.clear();
    },
  });
};

// Mutation for updating user profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { userId: string; updates: UpdateProfileData }): Promise<AuthUser> => {
      const { profile, error } = await supabaseAuthService.updateProfile(data.userId, data.updates);
      if (error || !profile) {
        throw new Error(error || 'Profile update failed');
      }

      // Convert profile to AuthUser format
      return {
        id: profile.id,
        email: profile.email,
        username: profile.username,
        created_at: profile.created_at,
      };
    },
    onSuccess: (user: AuthUser) => {
      // Update the user cache
      queryClient.setQueryData(queryKeys.user, user);

      // Invalidate profile-specific queries
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile(user.id) });
    },
    onError: (error) => {
      console.error('Profile update failed:', error);
    },
  });
};

// Mutation for changing password
export const useChangePassword = () => {
  return useMutation({
    mutationFn: async (data: ChangePasswordData): Promise<void> => {
      const { error } = await supabaseAuthService.changePassword(data.password);
      if (error) {
        throw new Error(error);
      }
    },
    onError: (error) => {
      console.error('Password change failed:', error);
    },
  });
};

// Hook for checking authentication status
export const useAuthStatus = () => {
  const { data: user, isLoading, error } = useCurrentUser();

  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
  };
};
