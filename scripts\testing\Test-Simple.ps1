# Simple PowerShell test script
Write-Host "Testing PowerShell script execution..." -ForegroundColor Green

# Test basic file checks
if (Test-Path "backend\Dockerfile") {
    Write-Host "✅ Backend Dockerfile found" -ForegroundColor Green
} else {
    Write-Host "❌ Backend Dockerfile missing" -ForegroundColor Red
}

if (Test-Path "frontend\Dockerfile") {
    Write-Host "✅ Frontend Dockerfile found" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend Dockerfile missing" -ForegroundColor Red
}

if (Test-Path "docker-compose.yml") {
    Write-Host "✅ Docker Compose file found" -ForegroundColor Green
} else {
    Write-Host "❌ Docker Compose file missing" -ForegroundColor Red
}

Write-Host "Test complete!" -ForegroundColor Cyan
