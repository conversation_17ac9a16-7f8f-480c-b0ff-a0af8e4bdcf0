# Frontend Development Dockerfile for AI Coding Agent React Application
# Optimized for hot reloading and development workflow

FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install development dependencies
RUN apk add --no-cache git

# Create non-root user for development (handle existing group)
RUN addgroup -g 1001 appuser 2>/dev/null || true && \
    adduser -D -u 1001 -G appuser appuser 2>/dev/null || true

# Copy package files for dependency installation
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Change ownership to appuser
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose development server port
EXPOSE 3000

# Start development server with hot reloading
CMD ["npm", "start"]
