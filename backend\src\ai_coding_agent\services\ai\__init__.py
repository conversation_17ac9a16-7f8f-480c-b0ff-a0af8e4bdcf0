"""
AI Services Package

This package provides AI integration services including:
- Abstract AI provider interfaces
- Specialized AI providers (Ollama, OpenAI, etc.)
- Agent orchestration and coordination
- Conversation management and context handling
- Model health monitoring and routing
"""

from .base import AIProvider, ChatRequest, ChatResponse, HealthStatus
from .orchestrator import AgentOrchestrator
from .conversation import ConversationManager

__all__ = [
    "AIProvider",
    "ChatRequest",
    "ChatResponse",
    "HealthStatus",
    "AgentOrchestrator",
    "ConversationManager",
]
