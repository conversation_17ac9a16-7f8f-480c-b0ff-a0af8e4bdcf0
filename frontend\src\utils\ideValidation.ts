/**
 * IDE Security and Validation Utilities
 * Comprehensive security measures for file operations and user inputs
 */

import DOMPurify from 'dompurify';

// File path security validation
export const validateFilePath = (path: string): boolean => {
  // Prevent directory traversal attacks
  const dangerousPatterns = [
    '../',
    '..\\',
    '//',
    '\\\\',
    '<',
    '>',
    '|',
    ':',
    '*',
    '?',
    '"'
  ];

  return !dangerousPatterns.some(pattern => path.includes(pattern)) &&
         path.length <= 260 && // Windows path limit
         path.length > 0 &&
         !/^\s|\s$/.test(path); // No leading/trailing whitespace
};

// Sanitize file content for safe display
export const sanitizeContent = (content: string): string => {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['pre', 'code', 'span'],
    ALLOWED_ATTR: ['class']
  });
};

// Validate file size
export const validateFileSize = (size: number): boolean => {
  const maxSize = parseInt(process.env.REACT_APP_MAX_FILE_SIZE || '10485760'); // 10MB default
  return size <= maxSize && size >= 0;
};

// Validate file name
export const validateFileName = (name: string): boolean => {
  const invalidChars = /[<>:"/\\|?*]/;
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];

  return !invalidChars.test(name) &&
         !reservedNames.includes(name.toUpperCase()) &&
         name.length <= 255 &&
         name.length > 0 &&
         !name.startsWith('.') &&
         !name.endsWith(' ') &&
         !name.endsWith('.');
};

// Sanitize agent messages
export const sanitizeAgentMessage = (message: string): string => {
  return DOMPurify.sanitize(message, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'code', 'pre'],
    ALLOWED_ATTR: []
  });
};

// Validate WebSocket message structure
export const validateWebSocketMessage = (message: any): boolean => {
  return typeof message === 'object' &&
         message !== null &&
         typeof message.type === 'string' &&
         message.type.length > 0 &&
         message.type.length <= 100;
};

// Rate limiting for API calls
class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(identifier: string, maxRequests: number = 100, windowMs: number = 60000): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;

    if (!this.requests.has(identifier)) {
      this.requests.set(identifier, []);
    }

    const userRequests = this.requests.get(identifier)!;

    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => time > windowStart);

    if (validRequests.length >= maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(identifier, validRequests);

    return true;
  }
}

export const rateLimiter = new RateLimiter();

// CSRF token management
export const getCsrfToken = (): string | null => {
  const meta = document.querySelector('meta[name="csrf-token"]');
  return meta ? meta.getAttribute('content') : null;
};

// IDE-specific secure storage
export const ideSecureStorage = {
  set: (key: string, value: any): void => {
    try {
      const encrypted = btoa(JSON.stringify(value));
      sessionStorage.setItem(`ide_${key}`, encrypted);
    } catch (error) {
      console.error('Failed to store IDE data:', error);
    }
  },

  get: (key: string): any => {
    try {
      const encrypted = sessionStorage.getItem(`ide_${key}`);
      if (!encrypted) return null;
      return JSON.parse(atob(encrypted));
    } catch (error) {
      console.error('Failed to retrieve IDE data:', error);
      return null;
    }
  },

  remove: (key: string): void => {
    sessionStorage.removeItem(`ide_${key}`);
  }
};

// Input validation for search queries
export const validateSearchQuery = (query: string): boolean => {
  return query.length <= 500 &&
         query.length > 0 &&
         !/[<>'"&]/g.test(query); // Prevent XSS
};

// Secure URL validation
export const validateUrl = (url: string): boolean => {
  try {
    const parsed = new URL(url);
    return ['http:', 'https:'].includes(parsed.protocol) &&
           (parsed.hostname !== 'localhost' || process.env.NODE_ENV === 'development');
  } catch {
    return false;
  }
};

// Code content security validation
export const validateCodeContent = (content: string): boolean => {
  // Check for potential malicious patterns
  const suspiciousPatterns = [
    /eval\s*\(/i,
    /document\.write/i,
    /innerHTML\s*=/i,
    /script\s*>/i,
    /javascript:/i
  ];

  return !suspiciousPatterns.some(pattern => pattern.test(content)) &&
         content.length <= 1000000; // 1MB limit for code files
};

// Agent request validation
export const validateAgentRequest = (request: any): boolean => {
  return typeof request === 'object' &&
         request !== null &&
         typeof request.message === 'string' &&
         request.message.length > 0 &&
         request.message.length <= 10000;
};

// Tab validation
export const validateTabData = (tab: any): boolean => {
  return typeof tab === 'object' &&
         tab !== null &&
         typeof tab.id === 'string' &&
         typeof tab.name === 'string' &&
         typeof tab.path === 'string' &&
         validateFilePath(tab.path) &&
         validateFileName(tab.name);
};

// File operation security wrapper
export const secureFileOperation = async <T>(
  operation: () => Promise<T>,
  operationType: string
): Promise<T> => {
  try {
    // Add rate limiting
    if (!rateLimiter.isAllowed(`file_${operationType}`, 50, 60000)) {
      throw new Error('Rate limit exceeded for file operations');
    }

    return await operation();
  } catch (error) {
    console.error(`Secure file operation failed (${operationType}):`, error);
    throw error;
  }
};
