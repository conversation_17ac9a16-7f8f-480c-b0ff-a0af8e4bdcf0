#!/bin/bash

# Docker Quick Setup Script for AI Coding Agent
# This script sets up the environment and starts the containers

set -e  # Exit on any error

echo "🚀 AI Coding Agent - Docker Quick Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}📋 Step $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Check prerequisites
print_step 1 "Checking prerequisites"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_success "Docker and Docker Compose are installed"

# Step 2: Environment setup
print_step 2 "Setting up environment"

if [ ! -f ".env" ]; then
    if [ -f ".env.template" ]; then
        print_warning ".env file not found. Copying from .env.template"
        cp .env.template .env
        print_warning "Please edit .env file and set required values:"
        print_warning "- SECRET_KEY (32+ characters)"
        print_warning "- CONFIG_ENCRYPTION_KEY (32+ characters)" 
        print_warning "- DB_PASSWORD (secure password)"
        echo ""
        read -p "Press Enter after editing .env file to continue..."
    else
        print_error ".env.template file not found. Cannot create .env file."
        exit 1
    fi
else
    print_success ".env file exists"
fi

# Step 3: Build frontend assets (if needed)
print_step 3 "Preparing frontend assets"

if [ ! -d "frontend/build" ] || [ -z "$(ls -A frontend/build 2>/dev/null)" ]; then
    print_warning "Frontend build directory is empty. Building frontend..."
    cd frontend
    if [ ! -d "node_modules" ]; then
        echo "Installing frontend dependencies..."
        npm install
    fi
    echo "Building frontend..."
    npm run build
    cd ..
    print_success "Frontend built successfully"
else
    print_success "Frontend build directory exists with content"
fi

# Step 4: Stop any existing containers
print_step 4 "Stopping existing containers"

docker-compose down 2>/dev/null || true
print_success "Existing containers stopped"

# Step 5: Build containers
print_step 5 "Building containers"

echo "Building backend container..."
docker-compose build backend

echo "Building frontend container..."
docker-compose build frontend

print_success "Containers built successfully"

# Step 6: Start services
print_step 6 "Starting services"

echo "Starting database and cache services..."
docker-compose up -d postgres redis

echo "Waiting for database to be ready..."
sleep 10

echo "Starting backend service..."
docker-compose up -d backend

echo "Waiting for backend to be ready..."
sleep 15

echo "Starting frontend service..."
docker-compose up -d frontend

print_success "All services started"

# Step 7: Verify services
print_step 7 "Verifying services"

echo "Checking service status..."
docker-compose ps

echo ""
echo "Testing backend health..."
if curl -f http://localhost:8000/api/v1/health > /dev/null 2>&1; then
    print_success "Backend is healthy"
else
    print_warning "Backend health check failed. Check logs with: docker-compose logs backend"
fi

echo "Testing frontend..."
if curl -f http://localhost:3000/ > /dev/null 2>&1; then
    print_success "Frontend is accessible"
else
    print_warning "Frontend check failed. Check logs with: docker-compose logs frontend"
fi

# Step 8: Show useful information
echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Services are running at:"
echo "  🌐 Frontend: http://localhost:3000"
echo "  🔧 Backend API: http://localhost:8000"
echo "  📊 API Health: http://localhost:8000/api/v1/health"
echo "  🗄️  Database: localhost:5432"
echo "  🔄 Redis: localhost:6379"
echo ""
echo "Useful commands:"
echo "  📋 View logs: docker-compose logs [service]"
echo "  🔄 Restart: docker-compose restart [service]"
echo "  🛑 Stop all: docker-compose down"
echo "  🔧 Rebuild: docker-compose build --no-cache [service]"
echo ""
echo "Development mode:"
echo "  🚀 Start dev: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d"
echo ""

# Optional: Open browser
read -p "Open frontend in browser? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:3000
    elif command -v open &> /dev/null; then
        open http://localhost:3000
    else
        echo "Please open http://localhost:3000 in your browser"
    fi
fi

print_success "Setup completed successfully! 🎉"
