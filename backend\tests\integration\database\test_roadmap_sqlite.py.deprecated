#!/usr/bin/env python3
"""
Test Phase B1: Roadmap System with SQLite database.
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import our models and services
from ai_coding_agent.models import Base
from ai_coding_agent.models.roadmap import (
    Project, Roadmap, Phase, Step, Task,
    TaskStatus, AgentType,
    ProjectCreate, RoadmapCreate, PhaseCreate, StepCreate, TaskCreate
)
from src.ai_coding_agent.services.roadmap import RoadmapService


def setup_test_database():
    """Set up SQLite database for testing."""
    db_path = "test_roadmap.db"
    if os.path.exists(db_path):
        os.remove(db_path)

    engine = create_engine(f"sqlite:///{db_path}", echo=False)
    Base.metadata.create_all(engine)

    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return engine, SessionLocal


def test_roadmap_service():
    """Test the roadmap service with SQLite."""
    print("🧪 Testing Phase B1: Roadmap System with SQLite")

    engine, SessionLocal = setup_test_database()

    db = SessionLocal()
    service = RoadmapService(db)

    try:
        # Test 1: Create a project with roadmap
        print("\n1. 📋 Creating project with roadmap...")

        project_data = ProjectCreate(
            name="Bakery Website",
            description="A modern bakery website with online ordering",
            tech_stack={
                "frontend": "React + TypeScript",
                "backend": "FastAPI + Python",
                "database": "PostgreSQL",
                "styling": "Tailwind CSS"
            },
            project_rules={
                "code_style": "PEP 8 for Python, ESLint for TypeScript",
                "testing": "pytest for backend, Jest for frontend",
                "git_workflow": "feature branches + PR reviews"
            },
            roadmap=RoadmapCreate(
                name="Bakery Website Development Roadmap",
                version="1.0.0",
                phases=[
                    PhaseCreate(
                        name="Phase 1: Project Setup",
                        description="Initialize project structure and basic configuration",
                        order_index=0,
                        steps=[
                            StepCreate(
                                name="Setup Development Environment",
                                description="Configure Python venv, install dependencies",
                                order_index=0,
                                tasks=[
                                    TaskCreate(
                                        name="Create Python virtual environment",
                                        description="Set up isolated Python environment",
                                        assigned_agent=AgentType.SHELL,
                                        order_index=0
                                    ),
                                    TaskCreate(
                                        name="Install backend dependencies",
                                        description="Install FastAPI, SQLAlchemy, etc.",
                                        assigned_agent=AgentType.BACKEND,
                                        order_index=1
                                    )
                                ]
                            ),
                            StepCreate(
                                name="Initialize Frontend Project",
                                description="Set up React project with TypeScript",
                                order_index=1,
                                tasks=[
                                    TaskCreate(
                                        name="Create React TypeScript project",
                                        description="Initialize frontend with create-react-app",
                                        assigned_agent=AgentType.FRONTEND,
                                        order_index=0
                                    )
                                ]
                            )
                        ]
                    ),
                    PhaseCreate(
                        name="Phase 2: Core Development",
                        description="Implement main features and functionality",
                        order_index=1,
                        dependencies=["phase_1"],
                        steps=[
                            StepCreate(
                                name="Database Design",
                                description="Design and implement database schema",
                                order_index=0,
                                tasks=[
                                    TaskCreate(
                                        name="Design database schema",
                                        description="Create ERD for bakery data model",
                                        assigned_agent=AgentType.ARCHITECT,
                                        order_index=0
                                    )
                                ]
                            )
                        ]
                    )
                ]
            )
        )

        # Create project first
        project_create = ProjectCreate(
            name=project_data.name,
            description=project_data.description,
            tech_stack=project_data.tech_stack,
            project_rules=project_data.project_rules
        )

        project = service.create_project(project_create)
        print(f"✅ Created project: {project.name}")
        print(f"📊 Project ID: {project.id}")

        # Create roadmap for the project
        if project_data.roadmap is None:
            raise ValueError("Project roadmap data is None")
        roadmap_create = RoadmapCreate(
            name=project_data.roadmap.name,
            phases=project_data.roadmap.phases
        )

        roadmap = service.create_roadmap(project.id, roadmap_create)
        print(f"🗺️ Roadmap: {roadmap.name}")
        print(f"📋 Phases: {len(roadmap.phases)}")

        # Test 2: Get roadmap details
        print("\n2. 🔍 Retrieving roadmap details...")
        if project.roadmap is None:
            raise ValueError("Project roadmap is None")
        roadmap = service.get_roadmap(project.roadmap.id)

        total_tasks = sum(
            len(step.tasks)
            for phase in roadmap.phases
            for step in phase.steps
        )
        print(f"✅ Roadmap retrieved: {roadmap.name}")
        print(f"📊 Total phases: {len(roadmap.phases)}")
        print(f"📊 Total tasks: {total_tasks}")

        # Test 3: Update task status
        print("\n3. 🔄 Testing task status updates...")

        # Find the first task
        first_task = None
        for phase in roadmap.phases:
            for step in phase.steps:
                if step.tasks:
                    first_task = step.tasks[0]
                    break
            if first_task:
                break

        if first_task:
            # Start the task
            updated_task = service.start_task(first_task.id)
            print(f"✅ Started task: {updated_task.name}")
            print(f"📊 Status: {updated_task.status}")
            print(f"⏰ Started at: {updated_task.started_at}")

            # Complete the task
            completed_task = service.complete_task(
                first_task.id,
                artifacts=[{
                    "type": "code",
                    "filename": "requirements.txt",
                    "content": "fastapi==0.104.1\nsqlalchemy==2.0.23",
                    "description": "Backend dependencies file"
                }]
            )
            print(f"✅ Completed task: {completed_task.name}")
            print(f"📊 Status: {completed_task.status}")
            print(f"🎯 Artifacts: {len(completed_task.artifacts)}")

        # Test 4: Get tasks by agent
        print("\n4. 👥 Testing agent task assignment...")

        backend_tasks = service.get_tasks_by_agent(AgentType.BACKEND)
        frontend_tasks = service.get_tasks_by_agent(AgentType.FRONTEND)
        shell_tasks = service.get_tasks_by_agent(AgentType.SHELL)

        print(f"✅ Backend agent tasks: {len(backend_tasks)}")
        print(f"✅ Frontend agent tasks: {len(frontend_tasks)}")
        print(f"✅ Shell agent tasks: {len(shell_tasks)}")

        print(f"\n🎉 Phase B1 Roadmap System Test: SUCCESS!")
        print("✅ All roadmap operations working correctly")
        print("✅ SQLite database integration working")
        print("✅ Task lifecycle management working")
        print("✅ Agent assignment system working")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


if __name__ == "__main__":
    success = test_roadmap_service()
    if success:
        print("\n🚀 Phase B1 is ready for production!")
    else:
        print("\n❌ Phase B1 needs fixes before proceeding")

    sys.exit(0 if success else 1)
