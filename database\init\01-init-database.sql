-- AI Coding Agent Database Initialization Script
-- This script sets up the initial database structure for PostgreSQL

-- Create database if it doesn't exist (this is handled by POSTGRES_DB env var)
-- But we can set up additional configurations here

-- Set timezone
SET timezone = 'UTC';

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create a schema for the application (optional, using public for now)
-- CREATE SCHEMA IF NOT EXISTS ai_coding_agent;

-- Grant permissions to the application user
GRANT ALL PRIVILEGES ON DATABASE ai_coding_agent TO agent;
GRANT ALL PRIVILEGES ON SCHEMA public TO agent;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO agent;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO agent;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO agent;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO agent;

-- Log the initialization
\echo 'AI Coding Agent database initialized successfully'
