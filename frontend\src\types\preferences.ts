/**
 * User Preferences System - Type Definitions
 * Defines the structure for user preferences with proper TypeScript typing
 */

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  inApp: boolean;
  marketing: boolean;
  security: boolean;
}

export interface AccessibilitySettings {
  reduceMotion: boolean;
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  screenReader: boolean;
  keyboardNavigation: boolean;
}

export interface DisplaySettings {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  density: 'compact' | 'comfortable' | 'spacious';
}

export interface PrivacySettings {
  analytics: boolean;
  cookies: boolean;
  dataCollection: boolean;
  thirdPartyIntegrations: boolean;
  profileVisibility: 'public' | 'private' | 'friends';
}

export interface DeveloperSettings {
  debugMode: boolean;
  apiLogging: boolean;
  performanceMetrics: boolean;
  experimentalFeatures: boolean;
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
}

export interface UserPreferences {
  id?: string;
  userId?: string;
  notifications: NotificationSettings;
  accessibility: AccessibilitySettings;
  display: DisplaySettings;
  privacy: PrivacySettings;
  developer: DeveloperSettings;
  createdAt?: string;
  updatedAt?: string;
}

export interface PreferencesExport {
  version: string;
  exportDate: string;
  preferences: Omit<UserPreferences, 'id' | 'userId' | 'createdAt' | 'updatedAt'>;
}

// Default preferences
export const defaultPreferences: Omit<UserPreferences, 'id' | 'userId' | 'createdAt' | 'updatedAt'> = {
  notifications: {
    email: true,
    push: true,
    inApp: true,
    marketing: false,
    security: true,
  },
  accessibility: {
    reduceMotion: false,
    highContrast: false,
    fontSize: 'medium',
    screenReader: false,
    keyboardNavigation: true,
  },
  display: {
    theme: 'system',
    language: 'en',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    density: 'comfortable',
  },
  privacy: {
    analytics: true,
    cookies: true,
    dataCollection: false,
    thirdPartyIntegrations: true,
    profileVisibility: 'private',
  },
  developer: {
    debugMode: false,
    apiLogging: false,
    performanceMetrics: false,
    experimentalFeatures: false,
    autoSave: true,
    autoSaveInterval: 30,
  },
};

// Validation schemas for preferences
export const preferencesValidation = {
  notifications: {
    email: { type: 'boolean', required: true },
    push: { type: 'boolean', required: true },
    inApp: { type: 'boolean', required: true },
    marketing: { type: 'boolean', required: true },
    security: { type: 'boolean', required: true },
  },
  accessibility: {
    reduceMotion: { type: 'boolean', required: true },
    highContrast: { type: 'boolean', required: true },
    fontSize: { type: 'string', required: true, enum: ['small', 'medium', 'large', 'extra-large'] },
    screenReader: { type: 'boolean', required: true },
    keyboardNavigation: { type: 'boolean', required: true },
  },
  display: {
    theme: { type: 'string', required: true, enum: ['light', 'dark', 'system'] },
    language: { type: 'string', required: true },
    timezone: { type: 'string', required: true },
    dateFormat: { type: 'string', required: true, enum: ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'] },
    timeFormat: { type: 'string', required: true, enum: ['12h', '24h'] },
    density: { type: 'string', required: true, enum: ['compact', 'comfortable', 'spacious'] },
  },
  privacy: {
    analytics: { type: 'boolean', required: true },
    cookies: { type: 'boolean', required: true },
    dataCollection: { type: 'boolean', required: true },
    thirdPartyIntegrations: { type: 'boolean', required: true },
    profileVisibility: { type: 'string', required: true, enum: ['public', 'private', 'friends'] },
  },
  developer: {
    debugMode: { type: 'boolean', required: true },
    apiLogging: { type: 'boolean', required: true },
    performanceMetrics: { type: 'boolean', required: true },
    experimentalFeatures: { type: 'boolean', required: true },
    autoSave: { type: 'boolean', required: true },
    autoSaveInterval: { type: 'number', required: true, min: 5, max: 300 },
  },
} as const;
