"""
Container Management API Endpoints.

Provides REST API endpoints for managing user containers in the
container-per-user model. Includes provisioning, status checking,
command execution, and cleanup operations.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel, Field

from ..services.container_manager import (
    UserContainerManager,
    get_container_manager,
    UserContainer,
    ProjectType,
    ContainerStatus
)
from ..services.auth import get_current_user
from ..models.user import User

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Router
router = APIRouter(
    prefix="/api/v1/containers",
    tags=["containers"]
)


# Request/Response Models
class ContainerProvisionRequest(BaseModel):
    """Request model for container provisioning."""
    project_type: ProjectType
    project_name: Optional[str] = Field(None, description="Optional project name")


class ContainerExecuteRequest(BaseModel):
    """Request model for command execution."""
    command: str = Field(..., description="Command to execute")
    working_dir: Optional[str] = Field(None, description="Working directory")


class ContainerResponse(BaseModel):
    """Response model for container information."""
    user_id: str
    container_id: str
    container_name: str
    project_type: ProjectType
    status: ContainerStatus
    port: Optional[int]
    preview_url: Optional[str]
    created_at: datetime
    last_accessed: datetime
    resource_limits: Dict[str, Any]


class ExecutionResponse(BaseModel):
    """Response model for command execution."""
    exit_code: int
    output: str
    success: bool
    executed_at: datetime


class ContainerListResponse(BaseModel):
    """Response model for container listing."""
    containers: List[ContainerResponse]
    total_count: int


@router.post("/provision", response_model=ContainerResponse, dependencies=[Depends(security)])
async def provision_user_container(
    request: ContainerProvisionRequest,
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Provision an isolated container environment for the current user.

    Creates a new container with the specified project type and proper
    security isolation, resource limits, and networking.
    """
    try:
        logger.info(f"Provisioning container for user {current_user.id}, type: {request.project_type}")

        user_container = await container_manager.provision_user_environment(
            user_id=str(current_user.id),
            project_type=request.project_type,
            project_name=request.project_name
        )

        return ContainerResponse(
            user_id=user_container.user_id,
            container_id=user_container.container_id,
            container_name=user_container.container_name,
            project_type=user_container.project_type,
            status=user_container.status,
            port=user_container.port,
            preview_url=user_container.preview_url,
            created_at=user_container.created_at,
            last_accessed=user_container.last_accessed,
            resource_limits=user_container.resource_limits
        )

    except Exception as e:
        logger.error(f"Failed to provision container for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Container provisioning failed: {str(e)}"
        )


@router.get("/status", response_model=Optional[ContainerResponse], dependencies=[Depends(security)])
async def get_container_status(
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Get the current status of the user's container.

    Returns container information if it exists, or None if no container
    is currently provisioned for the user.
    """
    try:
        user_container = await container_manager.get_container_status(str(current_user.id))

        if not user_container:
            return None

        return ContainerResponse(
            user_id=user_container.user_id,
            container_id=user_container.container_id,
            container_name=user_container.container_name,
            project_type=user_container.project_type,
            status=user_container.status,
            port=user_container.port,
            preview_url=user_container.preview_url,
            created_at=user_container.created_at,
            last_accessed=user_container.last_accessed,
            resource_limits=user_container.resource_limits
        )

    except Exception as e:
        logger.error(f"Failed to get container status for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get container status: {str(e)}"
        )


@router.post("/execute", response_model=ExecutionResponse, dependencies=[Depends(security)])
async def execute_command(
    request: ContainerExecuteRequest,
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Execute a command in the user's container.

    Runs the specified command in the user's isolated container environment
    with proper security restrictions and returns the execution results.
    """
    try:
        logger.info(f"Executing command for user {current_user.id}: {request.command}")

        result = await container_manager.execute_in_container(
            user_id=str(current_user.id),
            command=request.command,
            working_dir=request.working_dir
        )

        return ExecutionResponse(
            exit_code=result["exit_code"],
            output=result["output"],
            success=result["success"],
            executed_at=datetime.utcnow()
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to execute command for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Command execution failed: {str(e)}"
        )


@router.delete("/cleanup", dependencies=[Depends(security)])
async def cleanup_user_container(
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Clean up the user's container and associated resources.

    Stops and removes the user's container while preserving data volumes
    for future use. This is useful for freeing up resources when the
    container is no longer needed.
    """
    try:
        logger.info(f"Cleaning up container for user {current_user.id}")

        success = await container_manager.cleanup_user_container(str(current_user.id))

        if success:
            return {"message": "Container cleaned up successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cleanup container"
            )

    except Exception as e:
        logger.error(f"Failed to cleanup container for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Container cleanup failed: {str(e)}"
        )


# Admin endpoints (require admin privileges)
@router.get("/admin/list", response_model=ContainerListResponse)
async def list_all_containers(
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    List all active user containers (admin only).

    Returns information about all currently active user containers
    for monitoring and management purposes.
    """
    # TODO: Add admin role check
    # if not current_user.is_admin:
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    try:
        containers = container_manager.get_all_user_containers()

        container_responses = [
            ContainerResponse(
                user_id=container.user_id,
                container_id=container.container_id,
                container_name=container.container_name,
                project_type=container.project_type,
                status=container.status,
                port=container.port,
                preview_url=container.preview_url,
                created_at=container.created_at,
                last_accessed=container.last_accessed,
                resource_limits=container.resource_limits
            )
            for container in containers
        ]

        return ContainerListResponse(
            containers=container_responses,
            total_count=len(container_responses)
        )

    except Exception as e:
        logger.error(f"Failed to list containers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list containers: {str(e)}"
        )


@router.post("/admin/cleanup-inactive")
async def cleanup_inactive_containers(
    background_tasks: BackgroundTasks,
    max_idle_hours: int = 24,
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Clean up inactive containers (admin only).

    Removes containers that have been inactive for longer than the
    specified time period to free up system resources.
    """
    # TODO: Add admin role check
    # if not current_user.is_admin:
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    try:
        # Run cleanup in background
        background_tasks.add_task(
            container_manager.cleanup_inactive_containers,
            max_idle_hours
        )

        return {
            "message": f"Cleanup task started for containers inactive for {max_idle_hours} hours"
        }

    except Exception as e:
        logger.error(f"Failed to start cleanup task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start cleanup task: {str(e)}"
        )


@router.get("/health")
async def container_service_health(
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Check the health of the container management service.

    Verifies that the Docker daemon is accessible and the container
    management service is functioning properly.
    """
    try:
        # Test Docker connection
        container_manager.client.ping()

        # Get basic stats
        active_containers = len(container_manager.get_all_user_containers())

        return {
            "status": "healthy",
            "docker_connected": True,
            "active_containers": active_containers,
            "timestamp": datetime.utcnow()
        }

    except Exception as e:
        logger.error(f"Container service health check failed: {e}")
        return {
            "status": "unhealthy",
            "docker_connected": False,
            "error": str(e),
            "timestamp": datetime.utcnow()
        }
