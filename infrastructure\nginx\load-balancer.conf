# AI Coding Agent - Load Balancer Configuration
# Advanced load balancing for high-availability deployment

# Backend API load balancing with health checks
upstream backend_cluster {
    # Load balancing method: least_conn, ip_hash, or round_robin (default)
    least_conn;
    
    # Backend instances
    server backend-1:8000 max_fails=3 fail_timeout=30s;
    server backend-2:8000 max_fails=3 fail_timeout=30s;
    server backend-3:8000 max_fails=3 fail_timeout=30s backup;
    
    # Health check (requires nginx-plus or custom module)
    # health_check interval=10s fails=3 passes=2 uri=/api/v1/health;
    
    # Session persistence (if needed)
    # ip_hash;
}

# Frontend load balancing
upstream frontend_cluster {
    least_conn;
    
    server frontend-1:80 max_fails=3 fail_timeout=30s;
    server frontend-2:80 max_fails=3 fail_timeout=30s;
    server frontend-3:80 max_fails=3 fail_timeout=30s backup;
}

# Vector database cluster (read replicas)
upstream vector_db_cluster {
    # Use ip_hash for session affinity with vector DB
    ip_hash;
    
    server vector-db-1:8000 max_fails=2 fail_timeout=20s;
    server vector-db-2:8000 max_fails=2 fail_timeout=20s;
    server vector-db-3:8000 max_fails=2 fail_timeout=20s backup;
}

# Ollama service cluster
upstream ollama_cluster {
    least_conn;
    
    server ollama-1:11434 max_fails=2 fail_timeout=60s;
    server ollama-2:11434 max_fails=2 fail_timeout=60s;
    server ollama-3:11434 max_fails=2 fail_timeout=60s backup;
}

# Rate limiting zones for load balancer
limit_req_zone $binary_remote_addr zone=lb_api_limit:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=lb_auth_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=lb_admin_limit:10m rate=5r/s;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
limit_conn_zone $server_name zone=conn_limit_per_server:10m;

server {
    listen 80;
    server_name lb.ai-coding-agent.local;
    
    # Connection limits
    limit_conn conn_limit_per_ip 10;
    limit_conn conn_limit_per_server 1000;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Load balancer status page (restricted access)
    location /lb-status {
        # Restrict to internal networks
        allow **********/16;  # Docker network
        allow 10.0.0.0/8;     # Private network
        allow ***********/16; # Private network
        deny all;
        
        access_log off;
        return 200 "Load Balancer Status: OK\nUpstream Status: Check /lb-health\n";
        add_header Content-Type text/plain;
    }
    
    # Health check endpoint for load balancer
    location /lb-health {
        # Restrict to internal networks
        allow **********/16;
        allow 10.0.0.0/8;
        allow ***********/16;
        deny all;
        
        access_log off;
        
        # Custom health check logic
        proxy_pass http://backend_cluster/api/v1/health;
        proxy_connect_timeout 5s;
        proxy_read_timeout 5s;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }

    # API Routes with enhanced load balancing
    location /api/v1/auth/ {
        limit_req zone=lb_auth_limit burst=20 nodelay;
        
        proxy_pass http://backend_cluster;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Load-Balancer "nginx";
        
        # Retry configuration
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
        
        # Caching for auth endpoints (short-lived)
        proxy_cache_bypass $http_upgrade;
    }

    location /api/v1/admin/ {
        limit_req zone=lb_admin_limit burst=10 nodelay;
        
        proxy_pass http://backend_cluster;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Load-Balancer "nginx";
        
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 15s;
    }

    location /api/ {
        limit_req zone=lb_api_limit burst=40 nodelay;
        
        proxy_pass http://backend_cluster;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Load-Balancer "nginx";
        
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }

    # WebSocket load balancing with sticky sessions
    location /ws/ {
        proxy_pass http://backend_cluster;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        
        # Sticky sessions for WebSocket connections
        proxy_set_header X-Load-Balancer "nginx-ws";
    }

    # Vector database with session affinity
    location /vector-db/ {
        # Internal access only
        allow **********/16;
        deny all;
        
        proxy_pass http://vector_db_cluster/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 20s;
    }

    # Ollama service load balancing
    location /ollama/ {
        # Internal access only
        allow **********/16;
        deny all;
        
        proxy_pass http://ollama_cluster/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Longer timeouts for AI model processing
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 60s;
    }

    # Frontend load balancing
    location / {
        proxy_pass http://frontend_cluster;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Load-Balancer "nginx-frontend";
        
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 5s;
    }
}

# Enhanced logging for load balancer
log_format lb_main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for" '
                   'rt=$request_time uct="$upstream_connect_time" '
                   'uht="$upstream_header_time" urt="$upstream_response_time" '
                   'upstream_addr="$upstream_addr" upstream_status="$upstream_status"';

access_log /var/log/nginx/lb_access.log lb_main;
error_log /var/log/nginx/lb_error.log warn;
