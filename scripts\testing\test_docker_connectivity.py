#!/usr/bin/env python3
"""
Test Docker connectivity from within the backend container.

This script tests if the Docker SDK can connect to the Docker daemon
from within the backend container, which is essential for the
container-per-user functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend source to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "backend" / "src"))

try:
    from ai_coding_agent.services.container_manager import UserContainerManager, DOCKER_AVAILABLE
    print("✅ Container manager imports successful")
except ImportError as e:
    print(f"❌ Failed to import container manager: {e}")
    sys.exit(1)


async def test_docker_connectivity():
    """Test Docker connectivity and basic functionality."""
    print("🐳 Testing Docker connectivity...")
    
    if not DOCKER_AVAILABLE:
        print("❌ Docker SDK not available")
        return False
    
    try:
        # Initialize container manager
        print("📦 Initializing container manager...")
        container_manager = UserContainerManager()
        print("✅ Container manager initialized successfully")
        
        # Test Docker connection
        print("🔗 Testing Docker daemon connection...")
        container_manager.client.ping()
        print("✅ Docker daemon connection successful")
        
        # Test network creation (should already exist)
        print("🌐 Checking user container network...")
        try:
            network = container_manager.client.networks.get(container_manager.network_name)
            print(f"✅ Network '{container_manager.network_name}' exists")
        except Exception as e:
            print(f"⚠️ Network issue: {e}")
        
        # Test image availability
        print("🖼️ Checking base images...")
        base_images = ["node:18-alpine", "python:3.11-slim", "nginx:alpine"]
        for image in base_images:
            try:
                container_manager.client.images.get(image)
                print(f"✅ Image '{image}' available")
            except Exception:
                print(f"⚠️ Image '{image}' not available locally (will be pulled when needed)")
        
        # Test container creation (dry run)
        print("🧪 Testing container configuration...")
        config = container_manager.container_configs.get("react")
        if config:
            print(f"✅ React container config loaded: {config.image}")
        else:
            print("❌ React container config not found")
        
        print("✅ All Docker connectivity tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Docker connectivity test failed: {e}")
        return False


async def test_container_provisioning():
    """Test actual container provisioning (if Docker is available)."""
    print("\n🚀 Testing container provisioning...")
    
    try:
        container_manager = UserContainerManager()
        
        # Test user container provisioning
        print("📦 Provisioning test container...")
        test_user_id = "test-user-123"
        
        # Clean up any existing test container first
        try:
            await container_manager.cleanup_user_container(test_user_id)
            print("🧹 Cleaned up any existing test container")
        except:
            pass  # No existing container to clean up
        
        # Provision a new container
        from ai_coding_agent.services.container_manager import ProjectType
        user_container = await container_manager.provision_user_environment(
            user_id=test_user_id,
            project_type=ProjectType.PYTHON,
            project_name="test-project"
        )
        
        print(f"✅ Container provisioned successfully!")
        print(f"   Container ID: {user_container.container_id[:12]}...")
        print(f"   Container Name: {user_container.container_name}")
        print(f"   Status: {user_container.status}")
        print(f"   Port: {user_container.port}")
        print(f"   Preview URL: {user_container.preview_url}")
        
        # Test command execution
        print("⚡ Testing command execution...")
        result = await container_manager.execute_in_container(
            user_id=test_user_id,
            command="echo 'Hello from container!'"
        )
        
        if result["success"]:
            print(f"✅ Command executed successfully: {result['output'].strip()}")
        else:
            print(f"❌ Command execution failed: {result['output']}")
        
        # Clean up test container
        print("🧹 Cleaning up test container...")
        cleanup_success = await container_manager.cleanup_user_container(test_user_id)
        if cleanup_success:
            print("✅ Test container cleaned up successfully")
        else:
            print("⚠️ Test container cleanup had issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Container provisioning test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🔬 Docker Connectivity and Container Management Test")
    print("=" * 60)
    
    # Test 1: Basic Docker connectivity
    connectivity_ok = await test_docker_connectivity()
    
    if not connectivity_ok:
        print("\n❌ Docker connectivity failed. Container management will not work.")
        return 1
    
    # Test 2: Container provisioning (only if connectivity works)
    print("\n" + "=" * 60)
    provisioning_ok = await test_container_provisioning()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Docker Connectivity: {'✅ PASS' if connectivity_ok else '❌ FAIL'}")
    print(f"Container Provisioning: {'✅ PASS' if provisioning_ok else '❌ FAIL'}")
    
    if connectivity_ok and provisioning_ok:
        print("\n🎉 All tests passed! Container management system is fully functional.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Review the issues above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
