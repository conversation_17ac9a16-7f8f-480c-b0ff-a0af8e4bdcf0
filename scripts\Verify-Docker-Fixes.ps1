# Docker Container Fixes Verification Script (PowerShell)
# This script verifies all the critical fixes have been implemented correctly

param(
    [switch]$SkipBuild = $false
)

Write-Host "🔍 AI Coding Agent - Docker Fixes Verification" -ForegroundColor Cyan
Write-Host "=============================================="

# Function to print status
function Write-Status {
    param(
        [bool]$Success,
        [string]$Message
    )
    
    if ($Success) {
        Write-Host "✅ $Message" -ForegroundColor Green
    } else {
        Write-Host "❌ $Message" -ForegroundColor Red
        return $false
    }
    return $true
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

$allPassed = $true

# Check if .env file exists
Write-Host ""
Write-Host "1. Environment Configuration Check" -ForegroundColor Cyan
Write-Host "--------------------------------"

if (Test-Path ".env") {
    $allPassed = (Write-Status $true ".env file exists") -and $allPassed
    
    # Check for required environment variables
    $envContent = Get-Content ".env" -Raw
    $requiredVars = @("SECRET_KEY", "CONFIG_ENCRYPTION_KEY", "DB_PASSWORD")
    
    foreach ($var in $requiredVars) {
        if ($envContent -match "^$var=.+$") {
            $allPassed = (Write-Status $true "$var is set in .env") -and $allPassed
        } else {
            $allPassed = (Write-Status $false "$var is missing or empty in .env") -and $allPassed
        }
    }
} else {
    $allPassed = (Write-Status $false ".env file missing") -and $allPassed
    Write-Warning "Copy .env.template to .env and fill in required values"
}

# Check Docker files
Write-Host ""
Write-Host "2. Docker Configuration Check" -ForegroundColor Cyan
Write-Host "----------------------------"

# Check backend Dockerfile
if (Test-Path "backend/Dockerfile") {
    $allPassed = (Write-Status $true "Backend Dockerfile exists") -and $allPassed
    
    $dockerfileContent = Get-Content "backend/Dockerfile" -Raw
    
    # Check for critical fixes
    if ($dockerfileContent -match "PYTHONPATH=/app:/app/src") {
        $allPassed = (Write-Status $true "Backend Dockerfile has correct PYTHONPATH") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "Backend Dockerfile missing enhanced PYTHONPATH") -and $allPassed
    }
    
    if ($dockerfileContent -match "COPY src/ ./src/") {
        $allPassed = (Write-Status $true "Backend Dockerfile copies src directory correctly") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "Backend Dockerfile missing src directory copy") -and $allPassed
    }
    
    if ($dockerfileContent -match "ln -sf /app/src/ai_coding_agent /app/ai_coding_agent") {
        $allPassed = (Write-Status $true "Backend Dockerfile creates module symlink") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "Backend Dockerfile missing module symlink") -and $allPassed
    }
} else {
    $allPassed = (Write-Status $false "Backend Dockerfile missing") -and $allPassed
}

# Check frontend Dockerfile
if (Test-Path "frontend/Dockerfile") {
    $allPassed = (Write-Status $true "Frontend Dockerfile exists") -and $allPassed
    
    $frontendDockerfile = Get-Content "frontend/Dockerfile" -Raw
    
    if ($frontendDockerfile -match "npm run build") {
        $allPassed = (Write-Status $true "Frontend Dockerfile has build command") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "Frontend Dockerfile missing build command") -and $allPassed
    }
    
    if ($frontendDockerfile -match "test -f /app/build/index.html") {
        $allPassed = (Write-Status $true "Frontend Dockerfile has build verification") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "Frontend Dockerfile missing build verification") -and $allPassed
    }
} else {
    $allPassed = (Write-Status $false "Frontend Dockerfile missing") -and $allPassed
}

# Check docker-compose files
if (Test-Path "docker-compose.yml") {
    $allPassed = (Write-Status $true "docker-compose.yml exists") -and $allPassed
    
    $composeContent = Get-Content "docker-compose.yml" -Raw
    
    if ($composeContent -match "PYTHONPATH=/app:/app/src") {
        $allPassed = (Write-Status $true "docker-compose.yml has correct PYTHONPATH") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "docker-compose.yml missing enhanced PYTHONPATH") -and $allPassed
    }
    
    if ($composeContent -match "security_opt:") {
        $allPassed = (Write-Status $true "docker-compose.yml has security options") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "docker-compose.yml missing security options") -and $allPassed
    }
} else {
    $allPassed = (Write-Status $false "docker-compose.yml missing") -and $allPassed
}

# Check project structure
Write-Host ""
Write-Host "3. Project Structure Check" -ForegroundColor Cyan
Write-Host "------------------------"

$requiredDirs = @(
    "backend/src/ai_coding_agent",
    "backend/scripts", 
    "backend/tests",
    "frontend/src",
    "frontend/build"
)

foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        $allPassed = (Write-Status $true "$dir directory exists") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "$dir directory missing") -and $allPassed
    }
}

# Check if frontend build has static assets
if (Test-Path "frontend/build/static") {
    $staticFiles = Get-ChildItem "frontend/build/static" -Recurse
    if ($staticFiles.Count -gt 0) {
        $allPassed = (Write-Status $true "Frontend build has static assets") -and $allPassed
    } else {
        $allPassed = (Write-Status $false "Frontend build static directory is empty") -and $allPassed
    }
} else {
    Write-Warning "Frontend build/static directory missing (run 'npm run build' in frontend/)"
}

# Container build test (optional)
if (-not $SkipBuild) {
    Write-Host ""
    Write-Host "4. Container Build Test" -ForegroundColor Cyan
    Write-Host "---------------------"

    Write-Info "Testing container builds (this may take a few minutes)..."

    # Test backend build
    Write-Info "Building backend container..."
    try {
        $null = docker build -t ai-coding-agent-backend-test ./backend 2>$null
        $allPassed = (Write-Status $true "Backend container builds successfully") -and $allPassed
        $null = docker rmi ai-coding-agent-backend-test 2>$null
    } catch {
        $allPassed = (Write-Status $false "Backend container build failed") -and $allPassed
    }

    # Test frontend build
    Write-Info "Building frontend container..."
    try {
        $null = docker build -t ai-coding-agent-frontend-test ./frontend 2>$null
        $allPassed = (Write-Status $true "Frontend container builds successfully") -and $allPassed
        $null = docker rmi ai-coding-agent-frontend-test 2>$null
    } catch {
        $allPassed = (Write-Status $false "Frontend container build failed") -and $allPassed
    }
}

Write-Host ""
Write-Host "5. Quick Start Commands" -ForegroundColor Cyan
Write-Host "====================="

Write-Host ""
Write-Host "To start the application:" -ForegroundColor White
Write-Host "  docker-compose up -d" -ForegroundColor Gray
Write-Host ""
Write-Host "To start in development mode:" -ForegroundColor White
Write-Host "  docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d" -ForegroundColor Gray
Write-Host ""
Write-Host "To rebuild containers:" -ForegroundColor White
Write-Host "  docker-compose build --no-cache" -ForegroundColor Gray
Write-Host ""
Write-Host "To verify containers are running:" -ForegroundColor White
Write-Host "  docker-compose ps" -ForegroundColor Gray
Write-Host "  curl http://localhost:8000/api/v1/health" -ForegroundColor Gray
Write-Host "  curl http://localhost:3000/" -ForegroundColor Gray
Write-Host ""

if ($allPassed) {
    Write-Host "Verification complete! 🎉" -ForegroundColor Green
    exit 0
} else {
    Write-Host "Some checks failed. Please review the issues above." -ForegroundColor Red
    exit 1
}
