/**
 * Dev<PERSON>per Settings Component
 * Handles developer-specific preferences and debugging options
 */

import React from 'react';
import { Code, Bug, Zap, Activity, Save, Clock } from 'lucide-react';
import { usePreferences } from '../../contexts/PreferencesContext';

const developerFeatures = [
  {
    key: 'debugMode' as const,
    icon: Bug,
    title: 'Debug Mode',
    description: 'Enable debug information and developer tools',
  },
  {
    key: 'apiLogging' as const,
    icon: Activity,
    title: 'API Logging',
    description: 'Log API requests and responses to console',
  },
  {
    key: 'performanceMetrics' as const,
    icon: Zap,
    title: 'Performance Metrics',
    description: 'Show performance monitoring and timing data',
  },
  {
    key: 'experimentalFeatures' as const,
    icon: Code,
    title: 'Experimental Features',
    description: 'Enable beta and experimental functionality',
  },
  {
    key: 'autoSave' as const,
    icon: Save,
    title: 'Auto Save',
    description: 'Automatically save changes as you work',
  },
];

const autoSaveIntervals = [
  { value: 5, label: '5 seconds' },
  { value: 10, label: '10 seconds' },
  { value: 30, label: '30 seconds' },
  { value: 60, label: '1 minute' },
  { value: 120, label: '2 minutes' },
  { value: 300, label: '5 minutes' },
];

export const DeveloperSettings: React.FC = () => {
  const { preferences, updateSection } = usePreferences();
  const { developer } = preferences;

  const handleToggle = (key: keyof Omit<typeof developer, 'autoSaveInterval'>) => {
    updateSection('developer', {
      [key]: !developer[key],
    });
  };

  const handleAutoSaveIntervalChange = (interval: number) => {
    updateSection('developer', { autoSaveInterval: interval });
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          <Code className="h-5 w-5" />
          Developer Settings
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Advanced settings for developers and power users
        </p>
      </div>

      {/* Developer Features */}
      <div className="space-y-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white">
          Development Tools
        </h4>

        {developerFeatures.map((feature) => {
          const Icon = feature.icon;
          const isEnabled = developer[feature.key];

          return (
            <div
              key={feature.key}
              className="flex items-start justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div className="flex items-start gap-3">
                <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400 mt-0.5" />
                <div>
                  <h5 className="text-md font-medium text-gray-900 dark:text-white">
                    {feature.title}
                  </h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {feature.description}
                  </p>
                </div>
              </div>

              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isEnabled}
                  onChange={() => handleToggle(feature.key)}
                  className="sr-only peer"
                />
                <div className={`
                  relative w-11 h-6 rounded-full peer transition-colors duration-200 ease-in-out
                  ${isEnabled
                    ? 'bg-indigo-600'
                    : 'bg-gray-200 dark:bg-gray-700'
                  }
                  peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800
                `}>
                  <div className={`
                    absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 transition-transform duration-200 ease-in-out
                    ${isEnabled ? 'translate-x-full border-white' : 'translate-x-0'}
                  `} />
                </div>
              </label>
            </div>
          );
        })}
      </div>

      {/* Auto Save Settings */}
      {developer.autoSave && (
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Auto Save Interval
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {autoSaveIntervals.map((interval) => (
              <button
                key={interval.value}
                onClick={() => handleAutoSaveIntervalChange(interval.value)}
                className={`
                  p-3 border rounded-lg text-center transition-colors
                  ${developer.autoSaveInterval === interval.value
                    ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300'
                    : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }
                `}
              >
                {interval.label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Advanced Settings */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Advanced Configuration
        </h4>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Log Level
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
              <option value="error">Error</option>
              <option value="warn">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
              <option value="verbose">Verbose</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API Timeout (seconds)
            </label>
            <input
              type="number"
              min="5"
              max="120"
              defaultValue="30"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Cache Strategy
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
              <option value="aggressive">Aggressive Caching</option>
              <option value="normal">Normal Caching</option>
              <option value="minimal">Minimal Caching</option>
              <option value="disabled">Disabled</option>
            </select>
          </div>
        </div>
      </div>

      {/* Debug Information */}
      {developer.debugMode && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Debug Information
          </h4>

          <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 font-mono text-sm">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">User Agent:</span>
                <span className="text-gray-900 dark:text-white text-right">
                  {navigator.userAgent.substring(0, 50)}...
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Viewport:</span>
                <span className="text-gray-900 dark:text-white">
                  {window.innerWidth} × {window.innerHeight}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Color Scheme:</span>
                <span className="text-gray-900 dark:text-white">
                  {window.matchMedia('(prefers-color-scheme: dark)').matches ? 'Dark' : 'Light'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Timezone:</span>
                <span className="text-gray-900 dark:text-white">
                  {Intl.DateTimeFormat().resolvedOptions().timeZone}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Language:</span>
                <span className="text-gray-900 dark:text-white">
                  {navigator.language}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reset Options */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Reset Options
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <h6 className="font-medium text-gray-900 dark:text-white">
              Clear Cache
            </h6>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Clear all cached data and preferences
            </p>
          </button>

          <button className="p-3 border border-red-300 dark:border-red-600 rounded-lg text-left hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
            <h6 className="font-medium text-red-700 dark:text-red-400">
              Reset Developer Settings
            </h6>
            <p className="text-sm text-red-600 dark:text-red-500 mt-1">
              Reset all developer preferences to defaults
            </p>
          </button>
        </div>
      </div>
    </div>
  );
};
