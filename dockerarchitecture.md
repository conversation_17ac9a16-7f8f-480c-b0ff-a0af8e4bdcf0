# Docker-Based AI Coding Agent Architecture

## **System Overview**

Users interact with the AI Coding Agent frontend → Backend automatically provisions Docker containers → Each user gets isolated environment → AI maintains and hosts their projects.

## **Architecture Components**

### **1. Frontend (User Interface)**
```
AI Coding Agent Web App
├── Chat Interface (AI conversation)
├── Project Dashboard
├── Live Preview Panel
├── File Browser (optional)
└── Deployment Controls
```

### **2. Backend API & Orchestration**
```python
# Core Services
├── User Management Service
├── Container Orchestration Service
├── AI Agent Service (Claude API integration)
├── Project Management Service
├── File Sync Service
└── Monitoring & Maintenance Service
```

### **3. Docker Container Architecture**

#### **User Project Container Template**
```dockerfile
# Base container for each user project
FROM node:18-alpine  # or python:3.11-slim based on project type

# Create non-root user for security
RUN addgroup -g 1001 -S appuser && \
    adduser -S appuser -u 1001 -G appuser

# Set up workspace
WORKDIR /app
RUN chown -R appuser:appuser /app
USER appuser

# Install common tools
RUN npm install -g create-react-app next.js vite
# Or: pip install flask fastapi django

# Health check endpoint
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000
```

## **Implementation Flow**

### **Step 1: User Onboarding**
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend API
    participant D as Docker Engine

    U->>F: Sign up / Login
    F->>B: Create user session
    B->>B: Generate unique user ID
    B->>D: Provision user container
    D->>B: Container ready (port assigned)
    B->>F: Return container endpoint
    F->>U: Welcome to your workspace!
```

### **Step 2: AI-Driven Development**
```python
# Backend Container Management
class UserContainerManager:
    def __init__(self, user_id):
        self.user_id = user_id
        self.container_name = f"ai-agent-{user_id}"
        self.port = self.get_available_port()

    async def create_project(self, project_type, ai_instructions):
        """AI agent creates project in user's container"""
        # 1. Execute AI commands in container
        commands = await self.ai_agent.generate_setup_commands(
            project_type, ai_instructions
        )

        # 2. Run commands in isolated container
        for cmd in commands:
            await self.execute_in_container(cmd)

        # 3. Set up live preview
        await self.setup_preview_server()

        return f"http://preview-{self.user_id}.yourdomain.com"

    async def ai_modify_project(self, user_request):
        """AI makes changes based on user request"""
        # AI analyzes current project state
        current_files = await self.get_container_files()

        # Generate modification plan
        plan = await self.ai_agent.create_modification_plan(
            user_request, current_files
        )

        # Execute changes in container
        await self.apply_changes(plan)

        # Auto-deploy changes
        await self.deploy_changes()
```

## **Technical Implementation**

### **Container Orchestration Setup**

#### **Docker Compose for Multi-User Environment**
```yaml
# docker-compose.yml
version: '3.8'

services:
  # Main application
  ai-coding-agent:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./data:/app/data
    depends_on:
      - redis
      - postgres

  # Database for user management
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ai_coding_agent
      POSTGRES_USER: agent
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Session management & caching
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  # Reverse proxy for user containers
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ai-coding-agent

volumes:
  postgres_data:
  redis_data:
```

#### **Dynamic Container Provisioning**
```python
import docker
import asyncio
from typing import Dict, Optional

class ContainerOrchestrator:
    def __init__(self):
        self.client = docker.from_env()
        self.user_containers: Dict[str, dict] = {}

    async def provision_user_environment(self, user_id: str, project_type: str):
        """Create isolated container for user"""

        # Select appropriate base image
        base_images = {
            'react': 'node:18-alpine',
            'nextjs': 'node:18-alpine',
            'python': 'python:3.11-slim',
            'static': 'nginx:alpine'
        }

        container_config = {
            'image': base_images.get(project_type, 'node:18-alpine'),
            'name': f'user-{user_id}',
            'ports': {'3000/tcp': None},  # Auto-assign port
            'environment': {
                'USER_ID': user_id,
                'PROJECT_TYPE': project_type
            },
            'volumes': {
                f'user-{user_id}-data': {'bind': '/app', 'mode': 'rw'}
            },
            'network_mode': 'ai-coding-network',  # Isolated network
            'mem_limit': '512m',  # Resource limits
            'cpu_quota': 50000,   # 0.5 CPU
            'detach': True
        }

        try:
            container = self.client.containers.run(**container_config)

            # Get assigned port
            container.reload()
            port = container.ports['3000/tcp'][0]['HostPort']

            # Store container info
            self.user_containers[user_id] = {
                'container': container,
                'port': port,
                'status': 'running',
                'created_at': datetime.utcnow()
            }

            return {
                'container_id': container.id,
                'port': port,
                'preview_url': f'http://preview-{user_id}.yourdomain.com'
            }

        except docker.errors.APIError as e:
            raise Exception(f"Failed to create container: {str(e)}")
```

### **AI Agent Integration**

#### **Container Command Execution**
```python
class AIContainerAgent:
    def __init__(self, container_manager):
        self.container_manager = container_manager
        self.claude_client = AnthropicClient()

    async def execute_user_request(self, user_id: str, request: str):
        """AI processes user request and executes in their container"""

        # Get current project state
        project_state = await self.get_project_state(user_id)

        # Generate AI response and commands
        ai_response = await self.claude_client.generate_response(
            f"""
            User request: {request}
            Current project state: {project_state}

            Generate the necessary commands to fulfill this request.
            Return both explanation for user and executable commands.
            """
        )

        # Execute commands in user's container
        container = self.container_manager.get_user_container(user_id)

        for command in ai_response.commands:
            result = await self.execute_in_container(container, command)
            if result.exit_code != 0:
                # Handle errors and provide feedback
                await self.handle_command_error(user_id, command, result)

        # Return results to frontend
        return {
            'ai_explanation': ai_response.explanation,
            'changes_made': ai_response.changes,
            'preview_url': f'http://preview-{user_id}.yourdomain.com'
        }

    async def execute_in_container(self, container, command: str):
        """Safely execute command in user container"""
        try:
            result = container.exec_run(
                cmd=f"sh -c '{command}'",
                user='appuser',  # Non-root execution
                workdir='/app'
            )
            return {
                'exit_code': result.exit_code,
                'output': result.output.decode('utf-8')
            }
        except Exception as e:
            return {'exit_code': 1, 'error': str(e)}
```

### **Auto-Deployment & Hosting**

#### **Nginx Dynamic Configuration**
```python
class DynamicHosting:
    def __init__(self):
        self.nginx_config_path = '/etc/nginx/sites-enabled/'

    async def setup_user_subdomain(self, user_id: str, container_port: int):
        """Create subdomain for user's project"""

        config = f"""
        server {{
            listen 80;
            server_name preview-{user_id}.yourdomain.com;

            location / {{
                proxy_pass http://localhost:{container_port};
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # WebSocket support for hot reload
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }}
        }}
        """

        # Write config file
        config_file = f"{self.nginx_config_path}user-{user_id}.conf"
        with open(config_file, 'w') as f:
            f.write(config)

        # Reload nginx
        subprocess.run(['nginx', '-s', 'reload'])

        return f"https://preview-{user_id}.yourdomain.com"
```

## **Frontend Integration**

### **Real-time Communication**
```typescript
// Frontend WebSocket connection
class AIAgentConnection {
    private ws: WebSocket;
    private userId: string;

    constructor(userId: string) {
        this.userId = userId;
        this.connect();
    }

    private connect() {
        this.ws = new WebSocket(`wss://api.yourdomain.com/ws/${this.userId}`);

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);

            switch(data.type) {
                case 'ai_response':
                    this.displayAIResponse(data.message);
                    break;
                case 'container_ready':
                    this.updatePreviewUrl(data.preview_url);
                    break;
                case 'deployment_complete':
                    this.showSuccess('Changes deployed!');
                    break;
            }
        };
    }

    sendMessage(message: string) {
        this.ws.send(JSON.stringify({
            type: 'user_message',
            message: message,
            timestamp: Date.now()
        }));
    }
}
```

## **Security & Resource Management**

### **Container Security**
```yaml
# Security constraints for user containers
security_opt:
  - no-new-privileges:true
  - apparmor=docker-default

# Resource limits
deploy:
  resources:
    limits:
      cpus: '0.5'
      memory: 512M
    reservations:
      cpus: '0.1'
      memory: 128M

# Network isolation
networks:
  user-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.enable_ip_masquerade: 'true'
    ipam:
      config:
        - subnet: **********/16
```

## **Monitoring & Maintenance**

### **Container Health Monitoring**
```python
class ContainerMonitor:
    async def monitor_user_containers(self):
        """Monitor container health and resource usage"""
        for user_id, container_info in self.user_containers.items():
            container = container_info['container']

            # Health check
            if container.status != 'running':
                await self.restart_container(user_id)

            # Resource usage check
            stats = container.stats(stream=False)
            if self.is_resource_exceeded(stats):
                await self.scale_container_resources(user_id)

            # Clean up inactive containers
            if self.is_inactive(container_info, hours=24):
                await self.archive_container(user_id)
```

This architecture gives you:

1. **Complete Isolation**: Each user gets their own Docker container
2. **Automatic Setup**: Backend provisions containers on demand
3. **AI Integration**: Claude can execute commands safely in containers
4. **Live Hosting**: Automatic subdomain creation and reverse proxy
5. **Resource Management**: CPU/memory limits and monitoring
6. **Security**: Non-root execution, network isolation, resource limits
7. **Scalability**: Easy to add more containers as users grow

Would you like me to detail any specific part of this architecture or help you implement a particular component?
