# Copilot Security Setup Checklist for Isolated Project Environments

## 📋 Pre-Setup Verification Checklist

### ✅ System Requirements Check
- [ ] **Python Version**: Confirm Python 3.8+ is available
  ```bash
  python --version  # Should be 3.8 or higher
  ```
- [ ] **Virtual Environment Support**: Test venv module works
  ```bash
  python -m venv --help  # Should show help without errors
  ```
- [ ] **Disk Space**: Ensure minimum 5GB free space for project workspaces
  ```bash
  dir F:\ # Windows - check available space
  ```
- [ ] **Permissions**: Verify write access to user directories
  ```bash
  touch ~/.test_write && rm ~/.test_write  # Linux/Mac
  echo test > %USERPROFILE%\test_write && del %USERPROFILE%\test_write  # Windows
  ```

## 🏗️ Core Infrastructure Setup

### ✅ Directory Structure Creation
- [ ] **Create secure workspace root directory**
  ```python
  import os
  from pathlib import Path

  # Windows
  workspace_root = Path.home() / "AppData" / "Local" / "AiCodingAgent" / "Projects"
  # Linux/Mac
  workspace_root = Path.home() / ".ai_coding_agent" / "projects"

  workspace_root.mkdir(parents=True, exist_ok=True, mode=0o755)
  ```

- [ ] **Set proper directory permissions**
  ```python
  import stat

  # Set restrictive permissions (owner only)
  os.chmod(workspace_root, stat.S_IRWXU)  # 700 permissions
  ```

- [ ] **Create subdirectories for organization**
  ```python
  (workspace_root / "active").mkdir(exist_ok=True)
  (workspace_root / "archived").mkdir(exist_ok=True)
  (workspace_root / "logs").mkdir(exist_ok=True)
  (workspace_root / "temp").mkdir(exist_ok=True)
  ```

### ✅ Security Configuration Files
- [ ] **Create allowed packages whitelist**
  ```python
  allowed_packages = {
      # Data science
      "numpy", "pandas", "matplotlib", "seaborn", "plotly",
      # Web frameworks
      "fastapi", "flask", "django", "requests", "httpx",
      # Testing
      "pytest", "unittest", "mock",
      # Utilities
      "python-dotenv", "pydantic", "click", "typer",
      # Add more as needed but keep restrictive
  }

  with open(workspace_root / "allowed_packages.json", "w") as f:
      json.dump(list(allowed_packages), f, indent=2)
  ```

- [ ] **Create blocked packages blacklist**
  ```python
  blocked_packages = {
      # System access
      "os", "subprocess", "sys", "importlib",
      # Code execution
      "eval", "exec", "compile", "__import__",
      # File system
      "shutil", "pathlib", "glob",
      # Network (unless specifically allowed)
      "socket", "urllib", "ftplib", "telnetlib",
      # Process control
      "multiprocessing", "threading", "asyncio",
      # Dangerous packages
      "pickle", "marshal", "shelve"
  }

  with open(workspace_root / "blocked_packages.json", "w") as f:
      json.dump(list(blocked_packages), f, indent=2)
  ```

## 🔒 Security Measures Implementation

### ✅ Resource Limits Setup
- [ ] **Set project size limits**
  ```python
  PROJECT_LIMITS = {
      "max_size_bytes": 1024 * 1024 * 1024,  # 1GB
      "max_files": 10000,
      "max_packages": 50,
      "timeout_seconds": 300
  }

  with open(workspace_root / "resource_limits.json", "w") as f:
      json.dump(PROJECT_LIMITS, f, indent=2)
  ```

- [ ] **Create cleanup configuration**
  ```python
  CLEANUP_CONFIG = {
      "auto_cleanup_days": 7,
      "max_inactive_days": 30,
      "max_concurrent_projects_per_user": 5,
      "cleanup_schedule": "daily"
  }

  with open(workspace_root / "cleanup_config.json", "w") as f:
      json.dump(CLEANUP_CONFIG, f, indent=2)
  ```

### ✅ Logging and Monitoring Setup
- [ ] **Create security log file**
  ```python
  import logging

  # Set up security logging
  security_logger = logging.getLogger('ai_agent_security')
  handler = logging.FileHandler(workspace_root / "logs" / "security.log")
  formatter = logging.Formatter(
      '%(asctime)s - %(levelname)s - %(message)s'
  )
  handler.setFormatter(formatter)
  security_logger.addHandler(handler)
  security_logger.setLevel(logging.INFO)
  ```

- [ ] **Create audit log structure**
  ```python
  AUDIT_LOG_TEMPLATE = {
      "timestamp": None,
      "user_id": None,
      "project_id": None,
      "action": None,
      "details": {},
      "security_level": "INFO",
      "ip_address": None
  }

  with open(workspace_root / "audit_log_template.json", "w") as f:
      json.dump(AUDIT_LOG_TEMPLATE, f, indent=2)
  ```

## 🛡️ Project Isolation Implementation

### ✅ Virtual Environment Creation Function
- [ ] **Implement secure venv creation**
  ```python
  def create_isolated_project(user_id: str, project_name: str):
      # Generate unique project ID
      project_id = hashlib.sha256(f"{user_id}_{project_name}_{time.time()}".encode()).hexdigest()[:16]

      # Create user-specific directory
      user_dir = workspace_root / "active" / user_id
      user_dir.mkdir(exist_ok=True, mode=0o755)

      # Create project directory
      project_dir = user_dir / project_id
      project_dir.mkdir(exist_ok=True, mode=0o755)

      # Create virtual environment
      venv_dir = project_dir / "venv"
      subprocess.run([sys.executable, "-m", "venv", str(venv_dir)], check=True)

      # Create project metadata
      metadata = {
          "user_id": user_id,
          "project_name": project_name,
          "project_id": project_id,
          "created_at": datetime.now().isoformat(),
          "python_version": sys.version,
          "status": "active"
      }

      with open(project_dir / "project_metadata.json", "w") as f:
          json.dump(metadata, f, indent=2)

      return project_id, project_dir
  ```

### ✅ Package Installation Validation
- [ ] **Implement safe package installer**
  ```python
  def safe_install_package(project_dir: Path, package_name: str, version: str = None):
      # Load allowed packages
      with open(workspace_root / "allowed_packages.json") as f:
          allowed = set(json.load(f))

      with open(workspace_root / "blocked_packages.json") as f:
          blocked = set(json.load(f))

      # Security checks
      if package_name in blocked:
          raise SecurityError(f"Package {package_name} is blocked")

      if package_name not in allowed:
          raise SecurityError(f"Package {package_name} not in whitelist")

      # Check project size before install
      current_size = sum(f.stat().st_size for f in project_dir.rglob('*') if f.is_file())
      if current_size > PROJECT_LIMITS["max_size_bytes"]:
          raise ResourceError("Project size limit exceeded")

      # Install package with timeout and restrictions
      pip_executable = project_dir / "venv" / "Scripts" / "pip.exe"  # Windows
      # pip_executable = project_dir / "venv" / "bin" / "pip"  # Linux/Mac

      package_spec = f"{package_name}=={version}" if version else package_name

      subprocess.run([
          str(pip_executable), "install", package_spec,
          "--no-cache-dir",
          "--timeout", "300",
          "--no-deps"  # Prevent dependency surprises
      ], check=True, timeout=300)
  ```

## 🔍 Monitoring and Maintenance

### ✅ Health Check Functions
- [ ] **Implement project health monitoring**
  ```python
  def check_project_health(project_dir: Path):
      checks = {
          "directory_exists": project_dir.exists(),
          "venv_exists": (project_dir / "venv").exists(),
          "metadata_exists": (project_dir / "project_metadata.json").exists(),
          "size_within_limits": False,
          "package_count_ok": False
      }

      if checks["directory_exists"]:
          # Check size
          total_size = sum(f.stat().st_size for f in project_dir.rglob('*') if f.is_file())
          checks["size_within_limits"] = total_size < PROJECT_LIMITS["max_size_bytes"]

          # Check package count
          pip_list = subprocess.run([
              str(project_dir / "venv" / "Scripts" / "pip"), "list", "--format=json"
          ], capture_output=True, text=True)

          if pip_list.returncode == 0:
              packages = json.loads(pip_list.stdout)
              checks["package_count_ok"] = len(packages) < PROJECT_LIMITS["max_packages"]

      return checks
  ```

### ✅ Cleanup and Maintenance
- [ ] **Implement automatic cleanup**
  ```python
  def cleanup_old_projects():
      active_dir = workspace_root / "active"
      archived_dir = workspace_root / "archived"

      cutoff_date = datetime.now() - timedelta(days=CLEANUP_CONFIG["auto_cleanup_days"])

      for user_dir in active_dir.iterdir():
          if not user_dir.is_dir():
              continue

          for project_dir in user_dir.iterdir():
              metadata_file = project_dir / "project_metadata.json"

              if metadata_file.exists():
                  with open(metadata_file) as f:
                      metadata = json.load(f)

                  created_at = datetime.fromisoformat(metadata["created_at"])

                  if created_at < cutoff_date:
                      # Archive old project
                      archive_path = archived_dir / user_dir.name / project_dir.name
                      archive_path.parent.mkdir(parents=True, exist_ok=True)
                      shutil.move(str(project_dir), str(archive_path))
  ```

## 🚨 Security Incident Response

### ✅ Security Event Logging
- [ ] **Implement security event handler**
  ```python
  def log_security_event(user_id: str, event_type: str, details: dict, severity: str = "WARNING"):
      event = {
          "timestamp": datetime.now().isoformat(),
          "user_id": user_id,
          "event_type": event_type,
          "details": details,
          "severity": severity,
          "action_taken": "logged"
      }

      security_logger = logging.getLogger('ai_agent_security')
      security_logger.warning(json.dumps(event))

      # If critical, also write to separate critical log
      if severity == "CRITICAL":
          with open(workspace_root / "logs" / "critical_security.log", "a") as f:
              f.write(json.dumps(event) + "\n")
  ```

## ✅ Final Verification Steps

### System Integration Test
- [ ] **Test complete workflow**
  ```python
  def test_complete_workflow():
      # Test project creation
      project_id, project_dir = create_isolated_project("test_user", "test_project")
      assert project_dir.exists()

      # Test package installation
      safe_install_package(project_dir, "requests", "2.28.0")

      # Test health check
      health = check_project_health(project_dir)
      assert all(health.values())

      # Test cleanup (on old test projects)
      cleanup_old_projects()

      print("✅ All security measures verified and working!")

  # Run the test
  test_complete_workflow()
  ```

### Security Audit Checklist
- [ ] **Verify no system-level access possible from projects**
- [ ] **Confirm resource limits are enforced**
- [ ] **Test package whitelist/blacklist enforcement**
- [ ] **Verify project isolation (no cross-project access)**
- [ ] **Confirm logging captures all security events**
- [ ] **Test cleanup and archival processes**

## 🔧 Copilot Implementation Commands

When setting up, run these commands in sequence:

```bash
# 1. Create the workspace structure
python -c "
from pathlib import Path
import json
import os
import stat

# Create workspace
workspace_root = Path.home() / '.ai_coding_agent' / 'projects'
workspace_root.mkdir(parents=True, exist_ok=True)
os.chmod(workspace_root, stat.S_IRWXU)

# Create subdirectories
for subdir in ['active', 'archived', 'logs', 'temp']:
    (workspace_root / subdir).mkdir(exist_ok=True)

print('✅ Workspace structure created')
"

# 2. Create configuration files
python -c "
# [Include the configuration file creation code from above]
print('✅ Configuration files created')
"

# 3. Run verification test
python -c "
# [Include the test_complete_workflow function]
test_complete_workflow()
"
```

This checklist ensures Copilot can systematically verify and implement secure project isolation without Docker complexity.
