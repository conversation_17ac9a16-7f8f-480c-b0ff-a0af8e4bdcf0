import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { useIsTouchDevice } from '../utils/responsive';
import { hapticFeedback } from '../utils/touch';

/**
 * Breadcrumb navigation component for improved navigation hierarchy
 */

interface BreadcrumbItem {
  label: string;
  path: string;
  icon?: React.ComponentType<any>;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  showHomeIcon?: boolean;
  className?: string;
  maxItems?: number;
}

const routeLabels: Record<string, string> = {
  '/': 'Home',
  '/dashboard': 'Dashboard',
  '/profile': 'Profile',
  '/settings': 'Settings',
  '/login': 'Login',
  '/register': 'Register',
  '/showcase': 'Component Showcase',
};

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  showHomeIcon = true,
  className = '',
  maxItems = 4,
}) => {
  const location = useLocation();
  const isTouch = useIsTouchDevice();

  const handleClick = () => {
    if (isTouch) hapticFeedback.light();
  };

  // Generate breadcrumb items from current path if not provided
  const getBreadcrumbItems = (): BreadcrumbItem[] => {
    if (items) return items;

    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbItems: BreadcrumbItem[] = [];

    // Always include home
    breadcrumbItems.push({
      label: 'Home',
      path: '/',
      icon: showHomeIcon ? Home : undefined,
    });

    // Build path incrementally
    let currentPath = '';
    pathSegments.forEach((segment) => {
      currentPath += `/${segment}`;
      const label = routeLabels[currentPath] || segment.charAt(0).toUpperCase() + segment.slice(1);

      breadcrumbItems.push({
        label,
        path: currentPath,
      });
    });

    return breadcrumbItems;
  };

  const breadcrumbItems = getBreadcrumbItems();

  // Truncate items if too many
  const displayItems = breadcrumbItems.length > maxItems
    ? [
        breadcrumbItems[0], // Home
        { label: '...', path: '', icon: undefined },
        ...breadcrumbItems.slice(-maxItems + 2)
      ]
    : breadcrumbItems;

  if (breadcrumbItems.length <= 1) {
    return null; // Don't show breadcrumb for home page only
  }

  return (
    <nav
      className={`flex items-center space-x-1 text-sm ${className}`}
      aria-label="Breadcrumb navigation"
      role="navigation"
    >
      <ol className="flex items-center space-x-1">
        {displayItems.map((item, index) => {
          const isLast = index === displayItems.length - 1;
          const isEllipsis = item.label === '...';
          const Icon = item.icon;

          return (
            <li key={`${item.path}-${index}`} className="flex items-center">
              {index > 0 && (
                <ChevronRight
                  className="w-4 h-4 text-gray-400 dark:text-gray-500 mx-1"
                  aria-hidden="true"
                />
              )}

              {isEllipsis ? (
                <span
                  className="text-gray-500 dark:text-gray-400 px-2"
                  aria-hidden="true"
                >
                  {item.label}
                </span>
              ) : isLast ? (
                <span
                  className="
                    flex items-center text-gray-900 dark:text-white font-medium
                    px-2 py-1 rounded-md
                  "
                  aria-current="page"
                >
                  {Icon && (
                    <Icon className="w-4 h-4 mr-1" aria-hidden="true" />
                  )}
                  {item.label}
                </span>
              ) : (
                <Link
                  to={item.path}
                  onClick={handleClick}
                  className="
                    flex items-center text-gray-600 dark:text-gray-300
                    hover:text-blue-600 dark:hover:text-blue-400
                    px-2 py-1 rounded-md
                    focus:outline-none focus:ring-2 focus:ring-blue-500
                    transition-colors duration-150
                  "
                  aria-label={`Go to ${item.label}`}
                >
                  {Icon && (
                    <Icon className="w-4 h-4 mr-1" aria-hidden="true" />
                  )}
                  {item.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
