{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ai-coding-agent.com/schemas/roadmap.json", "title": "AI Coding Agent Roadmap Schema", "description": "JSON schema for project roadmaps in the AI Coding Agent system", "type": "object", "required": ["project_id", "name", "version", "phases"], "properties": {"project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project"}, "name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Human-readable name of the roadmap"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Semantic version of the roadmap (major.minor.patch)"}, "description": {"type": "string", "maxLength": 2000, "description": "Optional description of the roadmap"}, "status": {"type": "string", "enum": ["pending", "in_progress", "completed", "blocked", "cancelled"], "default": "pending", "description": "Overall status of the roadmap"}, "created_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when roadmap was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when roadmap was last updated"}, "project_metadata": {"type": "object", "description": "Additional project-specific metadata", "properties": {"tech_stack": {"type": "object", "properties": {"frontend": {"type": "array", "items": {"type": "string"}}, "backend": {"type": "array", "items": {"type": "string"}}, "database": {"type": "array", "items": {"type": "string"}}, "deployment": {"type": "array", "items": {"type": "string"}}}}, "team_size": {"type": "integer", "minimum": 1}, "estimated_duration": {"type": "string", "description": "Human-readable duration estimate"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium"}}}, "phases": {"type": "array", "minItems": 1, "description": "Ordered list of project phases", "items": {"$ref": "#/definitions/phase"}}}, "definitions": {"phase": {"type": "object", "required": ["id", "name", "order_index", "steps"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the phase"}, "name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Human-readable name of the phase"}, "description": {"type": "string", "maxLength": 2000, "description": "Detailed description of the phase"}, "order_index": {"type": "integer", "minimum": 0, "description": "Zero-based ordering index for the phase"}, "status": {"type": "string", "enum": ["pending", "in_progress", "completed", "blocked", "cancelled"], "default": "pending", "description": "Current status of the phase"}, "dependencies": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of phase IDs that must be completed before this phase can start"}, "estimated_duration": {"type": "string", "description": "Human-readable duration estimate for the phase"}, "started_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when phase was started"}, "completed_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when phase was completed"}, "project_metadata": {"type": "object", "description": "Phase-specific metadata"}, "steps": {"type": "array", "minItems": 1, "description": "Ordered list of steps within the phase", "items": {"$ref": "#/definitions/step"}}}}, "step": {"type": "object", "required": ["id", "name", "order_index", "tasks"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the step"}, "name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Human-readable name of the step"}, "description": {"type": "string", "maxLength": 2000, "description": "Detailed description of the step"}, "order_index": {"type": "integer", "minimum": 0, "description": "Zero-based ordering index for the step within its phase"}, "status": {"type": "string", "enum": ["pending", "in_progress", "completed", "blocked", "cancelled"], "default": "pending", "description": "Current status of the step"}, "dependencies": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of step IDs that must be completed before this step can start"}, "estimated_duration": {"type": "string", "description": "Human-readable duration estimate for the step"}, "started_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when step was started"}, "completed_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when step was completed"}, "project_metadata": {"type": "object", "description": "Step-specific metadata"}, "tasks": {"type": "array", "minItems": 1, "description": "Ordered list of tasks within the step", "items": {"$ref": "#/definitions/task"}}}}, "task": {"type": "object", "required": ["id", "name", "order_index", "assigned_agent"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the task"}, "name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Human-readable name of the task"}, "description": {"type": "string", "maxLength": 2000, "description": "Detailed description of the task"}, "order_index": {"type": "integer", "minimum": 0, "description": "Zero-based ordering index for the task within its step"}, "status": {"type": "string", "enum": ["pending", "in_progress", "completed", "failed", "blocked", "cancelled"], "default": "pending", "description": "Current status of the task"}, "assigned_agent": {"type": "string", "enum": ["architect", "frontend", "backend", "shell", "issue_fix"], "description": "AI agent responsible for executing this task"}, "dependencies": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of task IDs that must be completed before this task can start"}, "estimated_duration": {"type": "string", "description": "Human-readable duration estimate for the task"}, "started_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when task was started"}, "completed_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when task was completed"}, "error_message": {"type": "string", "description": "Error message if task failed"}, "artifacts": {"type": "array", "description": "List of artifacts produced by the task", "items": {"$ref": "#/definitions/artifact"}}, "project_metadata": {"type": "object", "description": "Task-specific metadata"}}}, "artifact": {"type": "object", "required": ["type", "name"], "properties": {"type": {"type": "string", "enum": ["code", "documentation", "configuration", "test", "deployment", "design"], "description": "Type of artifact produced"}, "name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Name or filename of the artifact"}, "path": {"type": "string", "description": "File path relative to project root"}, "content": {"type": "string", "description": "Content of the artifact (for small files)"}, "description": {"type": "string", "maxLength": 1000, "description": "Description of the artifact"}, "size": {"type": "integer", "minimum": 0, "description": "Size of the artifact in bytes"}, "checksum": {"type": "string", "description": "SHA-256 checksum of the artifact content"}, "created_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when artifact was created"}}}}}