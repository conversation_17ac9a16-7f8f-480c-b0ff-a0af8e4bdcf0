# Frontend Environment Configuration for AI Coding Agent
# Copy this file to .env.local and fill in your actual values

# API Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000

# Authentication
REACT_APP_SUPABASE_URL=your_supabase_url_here
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# IDE Features
REACT_APP_ENABLE_MONACO_EDITOR=true
REACT_APP_ENABLE_LIVE_COLLABORATION=true
REACT_APP_ENABLE_AGENT_CHAT=true
REACT_APP_ENABLE_FILE_SYNC=true

# AI Model Configuration
REACT_APP_ARCHITECT_MODEL=llama3.2:3b
REACT_APP_FRONTEND_MODEL=starcoder2:3b
REACT_APP_BACKEND_MODEL=deepseek-coder:6.7b-instruct
REACT_APP_SHELL_MODEL=qwen2.5:3b
REACT_APP_DEBUG_MODEL=deepseek-coder:6.7b-instruct
REACT_APP_TEST_MODEL=qwen2.5:3b

# Performance Settings
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_MAX_TABS=20
REACT_APP_AUTO_SAVE_INTERVAL=30000
REACT_APP_COLLABORATION_UPDATE_INTERVAL=1000

# Security Settings
REACT_APP_SECURE_WEBSOCKETS=false
REACT_APP_CSRF_PROTECTION=true
REACT_APP_CONTENT_SECURITY_POLICY=true

# Development Settings
REACT_APP_DEBUG_MODE=true
REACT_APP_MOCK_DATA=true
REACT_APP_LOG_LEVEL=info
