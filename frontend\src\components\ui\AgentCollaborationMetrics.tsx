/**
 * Agent Collaboration Metrics Dashboard
 *
 * Displays real-time metrics and statistics for agent collaboration
 */

import React, { useState, useEffect } from 'react';
import {
  Activity,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Zap,
  Target
} from 'lucide-react';
import { CollaborationMetrics } from '../../types/agents';
import { agentService } from '../../services/agentService';

interface MetricCardProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  title: string;
  value: string | number;
  subtitle?: string;
  color: string;
  trend?: 'up' | 'down' | 'stable';
}

const MetricCard: React.FC<MetricCardProps> = ({
  icon: Icon,
  title,
  value,
  subtitle,
  color,
  trend
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
      <div className="flex items-center justify-between mb-2">
        <div className={`p-2 rounded-lg ${color}`}>
          <Icon size={20} className="text-white" />
        </div>
        {trend && (
          <div className={`flex items-center text-sm ${
            trend === 'up' ? 'text-green-600' :
            trend === 'down' ? 'text-red-600' : 'text-gray-600'
          }`}>
            <TrendingUp size={14} className={trend === 'down' ? 'rotate-180' : ''} />
          </div>
        )}
      </div>

      <div className="space-y-1">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
          {value}
        </h3>
        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {title}
        </p>
        {subtitle && (
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {subtitle}
          </p>
        )}
      </div>
    </div>
  );
};

interface AgentCollaborationMetricsProps {
  className?: string;
}

const AgentCollaborationMetrics: React.FC<AgentCollaborationMetricsProps> = ({
  className = ''
}) => {
  const [metrics, setMetrics] = useState<CollaborationMetrics>({
    activeAgents: 0,
    totalTasks: 0,
    tasksInProgress: 0,
    completedTasks: 0,
    averageTaskTime: 0,
    errorRate: 0,
    collaborationEvents: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadMetrics = async () => {
      try {
        const data = await agentService.getCollaborationMetrics();
        setMetrics(data);
      } catch (error) {
        console.error('Failed to load metrics:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMetrics();

    // Update metrics every 5 seconds
    const interval = setInterval(loadMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className={`${className} bg-gray-50 dark:bg-gray-900 p-6`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
          <div className="grid grid-cols-2 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const formatTime = (minutes: number) => {
    if (minutes < 1) return `${Math.round(minutes * 60)}s`;
    if (minutes < 60) return `${Math.round(minutes)}m`;
    return `${Math.round(minutes / 60)}h`;
  };

  const formatPercentage = (rate: number) => {
    return `${Math.round(rate * 100)}%`;
  };

  return (
    <div className={`${className} bg-gray-50 dark:bg-gray-900 p-6`}>
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Collaboration Metrics
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Real-time agent performance and collaboration statistics
        </p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <MetricCard
          icon={Users}
          title="Active Agents"
          value={metrics.activeAgents}
          subtitle="Currently working"
          color="bg-blue-500"
          trend="stable"
        />

        <MetricCard
          icon={Activity}
          title="Tasks in Progress"
          value={metrics.tasksInProgress}
          subtitle={`${metrics.completedTasks} completed`}
          color="bg-green-500"
          trend="up"
        />

        <MetricCard
          icon={Clock}
          title="Avg Task Time"
          value={formatTime(metrics.averageTaskTime)}
          subtitle="Per task completion"
          color="bg-yellow-500"
          trend="down"
        />

        <MetricCard
          icon={CheckCircle}
          title="Success Rate"
          value={formatPercentage(1 - metrics.errorRate)}
          subtitle="Task completion rate"
          color="bg-green-500"
          trend="up"
        />

        <MetricCard
          icon={Zap}
          title="Collaboration Events"
          value={metrics.collaborationEvents}
          subtitle="Agent interactions"
          color="bg-purple-500"
          trend="up"
        />

        <MetricCard
          icon={Target}
          title="Total Tasks"
          value={metrics.totalTasks}
          subtitle="All time"
          color="bg-indigo-500"
          trend="stable"
        />
      </div>

      {/* System Health */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          System Health
        </h3>

        <div className="space-y-3">
          {/* Overall Performance */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Overall Performance</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.round((1 - metrics.errorRate) * 100)}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {formatPercentage(1 - metrics.errorRate)}
              </span>
            </div>
          </div>

          {/* Agent Utilization */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Agent Utilization</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(100, (metrics.activeAgents / 5) * 100)}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {metrics.activeAgents}/5
              </span>
            </div>
          </div>

          {/* Task Throughput */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Task Throughput</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(100, (metrics.tasksInProgress / 10) * 100)}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {metrics.tasksInProgress}/10
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Insights */}
      {metrics.bottleneckAgent && (
        <div className="mt-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={16} className="text-yellow-600 dark:text-yellow-400" />
            <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Performance Alert
            </span>
          </div>
          <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
            {metrics.bottleneckAgent} agent is experiencing higher than normal load.
            Consider redistributing tasks or optimizing workflows.
          </p>
        </div>
      )}

      {/* Peak Performance Time */}
      {metrics.peakPerformanceTime && (
        <div className="mt-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Target size={16} className="text-green-600 dark:text-green-400" />
            <span className="text-sm font-medium text-green-800 dark:text-green-200">
              Peak Performance
            </span>
          </div>
          <p className="text-sm text-green-700 dark:text-green-300 mt-1">
            System performs best around {metrics.peakPerformanceTime}.
            Schedule critical tasks during this time for optimal results.
          </p>
        </div>
      )}
    </div>
  );
};

export default AgentCollaborationMetrics;
