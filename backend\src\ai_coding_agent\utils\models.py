"""
Model configuration utilities for enhanced routing and health monitoring.

This module provides utilities for loading and validating the enhanced
models configuration with health routing, task timeouts, and quality overrides.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ModelHealthTracker:
    """Tracks model health and performance metrics."""

    def __init__(self):
        self.model_health = {}
        self.failure_counts = {}
        self.last_check = {}
        self.timeout_until = {}

    def record_success(self, model_name: str):
        """Record a successful model interaction."""
        self.failure_counts[model_name] = 0
        self.model_health[model_name] = min(1.0, self.model_health.get(model_name, 0.8) + 0.1)
        self.last_check[model_name] = datetime.now()

        # Clear timeout if model has recovered
        if model_name in self.timeout_until:
            del self.timeout_until[model_name]

    def record_failure(self, model_name: str, max_failures: int = 3, timeout_duration: int = 300):
        """Record a model failure and potentially mark as unhealthy."""
        self.failure_counts[model_name] = self.failure_counts.get(model_name, 0) + 1
        self.model_health[model_name] = max(0.0, self.model_health.get(model_name, 0.8) - 0.2)
        self.last_check[model_name] = datetime.now()

        if self.failure_counts[model_name] >= max_failures:
            self.timeout_until[model_name] = datetime.now() + timedelta(seconds=timeout_duration)
            logger.warning(f"Model {model_name} marked unhealthy after {max_failures} failures")

    def is_healthy(self, model_name: str, health_threshold: float = 0.8) -> bool:
        """Check if a model is healthy and available."""
        # Check if model is in timeout
        if model_name in self.timeout_until:
            if datetime.now() < self.timeout_until[model_name]:
                return False
            else:
                # Timeout expired, remove it
                del self.timeout_until[model_name]

        # Check health score
        health_score = self.model_health.get(model_name, 0.8)
        return health_score >= health_threshold

    def get_health_score(self, model_name: str) -> float:
        """Get the current health score for a model."""
        return self.model_health.get(model_name, 0.8)


class EnhancedModelsConfig:
    """Enhanced models configuration with health routing and task-specific settings."""

    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            config_path = str(Path(__file__).parent.parent / "models_config.json")

        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.health_tracker = ModelHealthTracker()

    def _load_config(self) -> Dict[str, Any]:
        """Load the models configuration from JSON file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)

            # Validate required sections
            required_sections = [
                "providers", "routing", "performance_settings",
                "health_routing", "task_timeouts", "quality_thresholds"
            ]

            for section in required_sections:
                if section not in config:
                    logger.warning(f"Missing configuration section: {section}")

            return config

        except FileNotFoundError:
            logger.error(f"Models configuration file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in models configuration: {e}")
            return {}

    def get_task_timeout(self, task_type: str) -> int:
        """Get timeout for a specific task type."""
        task_timeouts = self.config.get("task_timeouts", {})
        return task_timeouts.get(task_type, 120)  # Default 2 minutes

    def get_model_for_agent(self, agent_name: str, task_type: Optional[str] = None) -> str:
        """Get the best available model for an agent, considering health and task type."""
        routing = self.config.get("routing", {}).get(agent_name, {})
        health_config = self.config.get("health_routing", {})

        health_threshold = health_config.get("health_score_threshold", 0.8)
        auto_fallback = health_config.get("auto_fallback_on_failure", True)

        # Check task-specific routing first
        if task_type and "task_routing" in routing:
            task_model = routing["task_routing"].get(task_type)
            if task_model and self.health_tracker.is_healthy(task_model, health_threshold):
                return task_model

        # Try primary model
        primary_model = routing.get("primary")
        if primary_model and self.health_tracker.is_healthy(primary_model, health_threshold):
            return primary_model

        # Fallback to secondary if auto_fallback is enabled
        if auto_fallback:
            secondary_model = routing.get("secondary")
            if secondary_model and self.health_tracker.is_healthy(secondary_model, health_threshold):
                logger.info(f"Falling back to secondary model {secondary_model} for agent {agent_name}")
                return secondary_model

            # Final fallback
            fallback_model = routing.get("fallback")
            if fallback_model and self.health_tracker.is_healthy(fallback_model, health_threshold):
                logger.warning(f"Using fallback model {fallback_model} for agent {agent_name}")
                return fallback_model

        # If all else fails, return primary model anyway
        logger.error(f"No healthy models available for agent {agent_name}, using primary anyway")
        return primary_model or "llama3.2:3b"

    def get_quality_thresholds(self, model_name: str) -> Dict[str, float]:
        """Get quality thresholds for a specific model."""
        # Start with global thresholds
        thresholds = self.config.get("quality_thresholds", {}).copy()

        # Apply model-specific overrides
        overrides = self.config.get("model_quality_overrides", {}).get(model_name, {})
        thresholds.update(overrides)

        return thresholds

    def record_model_success(self, model_name: str):
        """Record a successful model interaction."""
        self.health_tracker.record_success(model_name)

    def record_model_failure(self, model_name: str):
        """Record a model failure."""
        health_config = self.config.get("health_routing", {})
        max_failures = health_config.get("max_consecutive_failures", 3)
        timeout_duration = health_config.get("unhealthy_model_timeout", 300)

        self.health_tracker.record_failure(model_name, max_failures, timeout_duration)

    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status for all models."""
        status = {}

        for provider_config in self.config.get("providers", {}).values():
            for model_name in provider_config.get("models", {}):
                status[model_name] = {
                    "health_score": self.health_tracker.get_health_score(model_name),
                    "is_healthy": self.health_tracker.is_healthy(model_name),
                    "failure_count": self.health_tracker.failure_counts.get(model_name, 0),
                    "last_check": self.health_tracker.last_check.get(model_name),
                    "timeout_until": self.health_tracker.timeout_until.get(model_name)
                }

        return status


# Global instance
_models_config = None

def get_models_config() -> EnhancedModelsConfig:
    """Get the global models configuration instance."""
    global _models_config
    if _models_config is None:
        _models_config = EnhancedModelsConfig()
    return _models_config


def reload_models_config():
    """Reload the models configuration from file."""
    global _models_config
    _models_config = EnhancedModelsConfig()
    logger.info("Models configuration reloaded")
