import { supabase, Profile } from '../lib/supabase';
import { Session } from '@supabase/supabase-js';

export interface AuthUser {
  id: string;
  email: string;
  username?: string;
  created_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  username: string;
}

export interface UpdateProfileData {
  username?: string;
  email?: string;
}

export interface ChangePasswordData {
  password: string;
}

class SupabaseAuthService {
  // Get current user session
  async getCurrentSession(): Promise<Session | null> {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.error('Error getting session:', error);
      return null;
    }
    return session;
  }

  // Sign up new user
  async signUp(credentials: RegisterCredentials): Promise<{ user: AuthUser | null; error: string | null }> {
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
      });

      if (authError) {
        return { user: null, error: authError.message };
      }

      if (!authData.user) {
        return { user: null, error: 'Failed to create user account' };
      }

      // Create profile in public.profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email: credentials.email,
          username: credentials.username,
        });

      if (profileError) {
        console.error('Error creating profile:', profileError);
        // Note: User auth was created but profile failed
        // In a production app, you might want to handle this differently
      }

      const user: AuthUser = {
        id: authData.user.id,
        email: credentials.email,
        username: credentials.username,
        created_at: authData.user.created_at,
      };

      return { user, error: null };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Registration failed'
      };
    }
  }

  // Sign in user
  async signIn(credentials: LoginCredentials): Promise<{ user: AuthUser | null; error: string | null }> {
    try {
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (authError) {
        return { user: null, error: authError.message };
      }

      if (!authData.user) {
        return { user: null, error: 'Login failed' };
      }

      // Get user profile
      const profile = await this.getUserProfile(authData.user.id);

      const user: AuthUser = {
        id: authData.user.id,
        email: authData.user.email || credentials.email,
        username: profile?.username,
        created_at: authData.user.created_at,
      };

      return { user, error: null };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Login failed'
      };
    }
  }

  // Sign out user
  async signOut(): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        return { error: error.message };
      }
      return { error: null };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Logout failed'
      };
    }
  }

  // Get user profile from database
  async getUserProfile(userId: string): Promise<Profile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      return null;
    }
  }

  // Update user profile
  async updateProfile(userId: string, updates: UpdateProfileData): Promise<{ profile: Profile | null; error: string | null }> {
    try {
      // Update profile in database
      const { data, error: dbError } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (dbError) {
        return { profile: null, error: dbError.message };
      }

      // Update auth email if provided
      if (updates.email) {
        const { error: authError } = await supabase.auth.updateUser({
          email: updates.email,
        });

        if (authError) {
          console.error('Error updating auth email:', authError);
          // Profile was updated but auth email failed
        }
      }

      return { profile: data, error: null };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error.message : 'Profile update failed'
      };
    }
  }

  // Change password
  async changePassword(newPassword: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        return { error: error.message };
      }

      return { error: null };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Password change failed'
      };
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        const profile = await this.getUserProfile(session.user.id);
        const user: AuthUser = {
          id: session.user.id,
          email: session.user.email || '',
          username: profile?.username,
          created_at: session.user.created_at,
        };
        callback(user);
      } else {
        callback(null);
      }
    });
  }
}

export const supabaseAuthService = new SupabaseAuthService();
export default supabaseAuthService;
