# Ollama GPU Memory Optimization for Quadro P1000 (4GB)

## Current Status
- **GPU Memory Available**: 4,096 MB
- **Currently Used**: 2,690 MB (66%)
- **Available for Optimization**: 1,406 MB (34%)

## Optimization Strategy

### 1. Set Ollama Environment Variables for Maximum GPU Usage

Create a batch file to configure Ollama with optimal memory settings:

```batch
@echo off
REM Configure Ollama for maximum 4GB GPU usage
set OLLAMA_GPU_MEMORY_FRACTION=0.95
set OLLAMA_MAX_LOADED_MODELS=2
set OLLAMA_MAX_QUEUE=8
set OLLAMA_NUM_PARALLEL=2
set OLLAMA_FLASH_ATTENTION=1

REM Start Ollama with optimized settings
ollama serve
```

### 2. Model-Specific Memory Optimization

**Recommended Model Loading Strategy for 4GB:**

1. **Primary Models (Keep Loaded):**
   - `llama3.2:3b` (~2.0 GB) - Architect agent
   - `starcoder2:3b` (~1.7 GB) - Frontend agent
   - **Total**: ~3.7 GB (optimal for 4GB)

2. **Secondary Models (Load on Demand):**
   - `qwen2.5:3b` (~1.9 GB) - Shell/Analysis
   - `yi-coder:1.5b` (~866 MB) - Fast completion
   - `deepseek-coder:6.7b-instruct` (~3.8 GB) - Complex tasks only

### 3. Concurrent Model Loading

**Option A: Two 3B Models Simultaneously**
```bash
# Load two optimal models that fit together
ollama run llama3.2:3b &
ollama run starcoder2:3b &
```

**Option B: One Large + One Small Model**
```bash
# Load one large model + one fast model
ollama run deepseek-coder:6.7b-instruct &
ollama run yi-coder:1.5b &
```

### 4. Dynamic Memory Management

**Smart Loading Script:**
```bash
#!/bin/bash
# Smart model loading based on available GPU memory

check_gpu_memory() {
    nvidia-smi --query-gpu=memory.free --format=csv,noheader,nounits
}

load_optimal_models() {
    free_memory=$(check_gpu_memory)

    if [ $free_memory -gt 3500 ]; then
        echo "Loading large model..."
        ollama run deepseek-coder:6.7b-instruct
    elif [ $free_memory -gt 2000 ]; then
        echo "Loading medium models..."
        ollama run llama3.2:3b &
        ollama run starcoder2:3b &
    else
        echo "Loading small model..."
        ollama run yi-coder:1.5b
    fi
}
```

## Immediate Actions

### Step 1: Configure Environment Variables
```batch
set OLLAMA_GPU_MEMORY_FRACTION=0.95
set OLLAMA_MAX_LOADED_MODELS=2
set OLLAMA_NUM_PARALLEL=2
```

### Step 2: Restart Ollama with New Settings
```batch
# Stop current Ollama
taskkill /f /im ollama.exe

# Start with new configuration
ollama serve
```

### Step 3: Preload Optimal Model Combination
```batch
# Load two models that work well together and fit in 4GB
ollama run llama3.2:3b "warmup"
ollama run starcoder2:3b "warmup"
```

## Expected Results

After optimization:
- **GPU Memory Usage**: 95% (3,891 MB / 4,096 MB)
- **Model Response Time**: 30-50% faster due to more models staying in GPU memory
- **Concurrent Requests**: Can handle 2 different model types simultaneously
- **Memory Efficiency**: Minimal CPU fallback, maximum GPU utilization

## Performance Monitoring

Use this command to monitor GPU usage:
```batch
# Monitor GPU usage every 2 seconds
nvidia-smi -l 2
```

Expected output after optimization:
```
|    0   N/A  N/A            1656      C   ...Programs\Ollama\ollama.exe     3800MiB |
```

This will give you maximum performance from your Quadro P1000!
