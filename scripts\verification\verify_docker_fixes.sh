#!/bin/bash

# Docker Container Fixes Verification Script
# This script verifies all the critical fixes have been implemented correctly

set -e  # Exit on any error

echo "🔍 AI Coding Agent - Docker Fixes Verification"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# Check if .env file exists
echo ""
echo "1. Environment Configuration Check"
echo "--------------------------------"

if [ -f ".env" ]; then
    print_status 0 ".env file exists"
    
    # Check for required environment variables
    required_vars=("SECRET_KEY" "CONFIG_ENCRYPTION_KEY" "DB_PASSWORD")
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" .env && ! grep -q "^${var}=$" .env; then
            print_status 0 "$var is set in .env"
        else
            print_status 1 "$var is missing or empty in .env"
        fi
    done
else
    print_status 1 ".env file missing"
    print_warning "Copy .env.template to .env and fill in required values"
fi

# Check Docker files
echo ""
echo "2. Docker Configuration Check"
echo "----------------------------"

# Check backend Dockerfile
if [ -f "backend/Dockerfile" ]; then
    print_status 0 "Backend Dockerfile exists"
    
    # Check for critical fixes
    if grep -q "PYTHONPATH=/app:/app/src" backend/Dockerfile; then
        print_status 0 "Backend Dockerfile has correct PYTHONPATH"
    else
        print_status 1 "Backend Dockerfile missing enhanced PYTHONPATH"
    fi
    
    if grep -q "COPY src/ ./src/" backend/Dockerfile; then
        print_status 0 "Backend Dockerfile copies src directory correctly"
    else
        print_status 1 "Backend Dockerfile missing src directory copy"
    fi
    
    if grep -q "ln -sf /app/src/ai_coding_agent /app/ai_coding_agent" backend/Dockerfile; then
        print_status 0 "Backend Dockerfile creates module symlink"
    else
        print_status 1 "Backend Dockerfile missing module symlink"
    fi
else
    print_status 1 "Backend Dockerfile missing"
fi

# Check frontend Dockerfile
if [ -f "frontend/Dockerfile" ]; then
    print_status 0 "Frontend Dockerfile exists"
    
    if grep -q "npm run build" frontend/Dockerfile; then
        print_status 0 "Frontend Dockerfile has build command"
    else
        print_status 1 "Frontend Dockerfile missing build command"
    fi
    
    if grep -q "test -f /app/build/index.html" frontend/Dockerfile; then
        print_status 0 "Frontend Dockerfile has build verification"
    else
        print_status 1 "Frontend Dockerfile missing build verification"
    fi
else
    print_status 1 "Frontend Dockerfile missing"
fi

# Check docker-compose files
if [ -f "docker-compose.yml" ]; then
    print_status 0 "docker-compose.yml exists"
    
    if grep -q "PYTHONPATH=/app:/app/src" docker-compose.yml; then
        print_status 0 "docker-compose.yml has correct PYTHONPATH"
    else
        print_status 1 "docker-compose.yml missing enhanced PYTHONPATH"
    fi
    
    if grep -q "security_opt:" docker-compose.yml; then
        print_status 0 "docker-compose.yml has security options"
    else
        print_status 1 "docker-compose.yml missing security options"
    fi
else
    print_status 1 "docker-compose.yml missing"
fi

# Check project structure
echo ""
echo "3. Project Structure Check"
echo "------------------------"

required_dirs=("backend/src/ai_coding_agent" "backend/scripts" "backend/tests" "frontend/src" "frontend/build")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_status 0 "$dir directory exists"
    else
        print_status 1 "$dir directory missing"
    fi
done

# Check if frontend build has static assets
if [ -d "frontend/build/static" ]; then
    if [ "$(ls -A frontend/build/static)" ]; then
        print_status 0 "Frontend build has static assets"
    else
        print_status 1 "Frontend build static directory is empty"
    fi
else
    print_warning "Frontend build/static directory missing (run 'npm run build' in frontend/)"
fi

echo ""
echo "4. Container Build Test"
echo "---------------------"

print_info "Testing container builds (this may take a few minutes)..."

# Test backend build
print_info "Building backend container..."
if docker build -t ai-coding-agent-backend-test ./backend > /dev/null 2>&1; then
    print_status 0 "Backend container builds successfully"
    docker rmi ai-coding-agent-backend-test > /dev/null 2>&1 || true
else
    print_status 1 "Backend container build failed"
fi

# Test frontend build
print_info "Building frontend container..."
if docker build -t ai-coding-agent-frontend-test ./frontend > /dev/null 2>&1; then
    print_status 0 "Frontend container builds successfully"
    docker rmi ai-coding-agent-frontend-test > /dev/null 2>&1 || true
else
    print_status 1 "Frontend container build failed"
fi

echo ""
echo "5. Quick Start Commands"
echo "====================="

echo ""
echo "To start the application:"
echo "  docker-compose up -d"
echo ""
echo "To start in development mode:"
echo "  docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d"
echo ""
echo "To rebuild containers:"
echo "  docker-compose build --no-cache"
echo ""
echo "To verify containers are running:"
echo "  docker-compose ps"
echo "  curl http://localhost:8000/api/v1/health"
echo "  curl http://localhost:3000/"
echo ""

echo "Verification complete! 🎉"
