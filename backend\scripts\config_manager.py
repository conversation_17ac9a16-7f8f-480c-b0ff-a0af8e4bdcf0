#!/usr/bin/env python3
"""
Environment-specific configuration management.
Provides easy switching between development, testing, and production configs.
"""

import os
import shutil
from pathlib import Path
from typing import Dict, Optional

class ConfigManager:
    """Manage environment-specific configurations."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.config_dir = self.project_root / "config"
        self.env_file = self.project_root / ".env"

        # Environment templates (simplified to Supabase + Redis only)
        self.environments = {
            "development": {
                "DATABASE_MODE": "supabase",
                "DEBUG": "true",
                "REDIS_URL": "redis://localhost:6379",
                "CORS_ORIGINS": "http://localhost:3000,http://localhost:8080",
                "LOG_LEVEL": "DEBUG"
            },
            "testing": {
                "DATABASE_MODE": "supabase",
                "DEBUG": "true",
                "REDIS_URL": "redis://localhost:6379",
                "CORS_ORIGINS": "http://localhost:3000",
                "LOG_LEVEL": "INFO"
            },
            "production": {
                "DATABASE_MODE": "supabase",
                "DEBUG": "false",
                "REDIS_URL": "redis://redis:6379",
                "CORS_ORIGINS": "https://yourdomain.com",
                "LOG_LEVEL": "WARNING"
            }
        }

    def switch_environment(self, env_name: str):
        """Switch to specific environment configuration."""
        if env_name not in self.environments:
            print(f"❌ Unknown environment: {env_name}")
            print(f"Available environments: {list(self.environments.keys())}")
            return False

        print(f"🔄 Switching to {env_name} environment...")

        # Backup current .env
        if self.env_file.exists():
            backup_path = self.env_file.with_suffix(f".env.backup.{env_name}")
            shutil.copy2(self.env_file, backup_path)
            print(f"   📁 Backed up current .env to {backup_path.name}")

        # Load base .env
        base_config = self._load_base_config()

        # Apply environment-specific overrides
        env_config = self.environments[env_name]
        base_config.update(env_config)

        # Write new .env
        self._write_env_file(base_config)
        print(f"   ✅ Switched to {env_name} environment")

        # Validate configuration
        self._validate_config(env_name)

        return True

    def _load_base_config(self) -> Dict[str, str]:
        """Load base configuration from existing .env."""
        config = {}
        if self.env_file.exists():
            with open(self.env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
        return config

    def _write_env_file(self, config: Dict[str, str]):
        """Write configuration to .env file."""
        with open(self.env_file, 'w') as f:
            f.write("# AI Coding Agent Environment Configuration\n")
            f.write(f"# Generated for environment\n\n")

            # Group related settings (updated for new architecture)
            groups = {
                "Application": ["APP_NAME", "DEBUG", "HOST", "PORT", "ENVIRONMENT"],
                "Database": ["DATABASE_MODE", "DB_HOST", "DB_PORT", "DB_NAME", "DB_USER", "DB_PASSWORD"],
                "Supabase": ["SUPABASE_URL", "SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY"],
                "Redis Cache": ["REDIS_URL", "REDIS_PASSWORD", "REDIS_DB"],
                "Embeddings": ["LTKB_EMBEDDING_MODEL", "STPM_EMBEDDING_MODEL"],
                "Security": ["SECRET_KEY", "CORS_ORIGINS"],
                "Logging": ["LOG_LEVEL", "LOG_FILE"]
            }

            for group_name, keys in groups.items():
                f.write(f"# {group_name} Settings\n")
                for key in keys:
                    if key in config:
                        f.write(f"{key}={config[key]}\n")
                f.write("\n")

            # Write remaining keys
            written_keys = set()
            for keys in groups.values():
                written_keys.update(keys)

            remaining = set(config.keys()) - written_keys
            if remaining:
                f.write("# Other Settings\n")
                for key in sorted(remaining):
                    f.write(f"{key}={config[key]}\n")

    def _validate_config(self, env_name: str):
        """Validate configuration for environment."""
        print(f"   🔍 Validating {env_name} configuration...")

        try:
            # Test import
            import ai_coding_agent.config
            from ai_coding_agent.config import settings

            print(f"   ✅ Configuration loaded successfully")
            print(f"   📊 Database mode: {settings.database.mode}")
            print(f"   🐛 Debug mode: {settings.debug}")

        except Exception as e:
            print(f"   ❌ Configuration validation failed: {e}")

    def list_environments(self):
        """List available environments."""
        print("📋 Available environments:")
        for env_name, config in self.environments.items():
            db_mode = config.get("DATABASE_MODE", "unknown")
            debug = config.get("DEBUG", "unknown")
            print(f"   🌍 {env_name}: DB={db_mode}, Debug={debug}")

    def create_environment(self, env_name: str, config: Dict[str, str]):
        """Create a new environment configuration."""
        if env_name in self.environments:
            print(f"⚠️ Environment {env_name} already exists")
            return False

        self.environments[env_name] = config
        print(f"✅ Created environment: {env_name}")
        return True

def main():
    """CLI interface for configuration management."""
    import sys

    manager = ConfigManager()

    if len(sys.argv) < 2:
        print("🔧 Configuration Manager")
        print("Usage:")
        print("  python config_manager.py list")
        print("  python config_manager.py switch <environment>")
        return

    command = sys.argv[1]

    if command == "list":
        manager.list_environments()
    elif command == "switch" and len(sys.argv) == 3:
        env_name = sys.argv[2]
        manager.switch_environment(env_name)
    else:
        print("❌ Invalid command")

if __name__ == "__main__":
    main()
