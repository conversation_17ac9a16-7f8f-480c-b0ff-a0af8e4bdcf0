# PowerShell Pre-Build Verification Script for AI Coding Agent
# Run this BEFORE building Docker containers to ensure all files are in place
# Usage: .\scripts\Verify-PreBuild-Working.ps1

Write-Host "AI Coding Agent - Pre-Build File Structure Verification" -ForegroundColor Cyan
Write-Host "=======================================================" -ForegroundColor Cyan

# Function to check if file exists
function Test-FileExists {
    param(
        [string]$FilePath,
        [string]$Description
    )

    if (Test-Path $FilePath -PathType Leaf) {
        Write-Host "[OK] $Description : $FilePath" -ForegroundColor Green
        return $true
    } else {
        Write-Host "[MISSING] $Description : $FilePath" -ForegroundColor Red
        return $false
    }
}

# Function to check if directory exists
function Test-DirectoryExists {
    param(
        [string]$DirectoryPath,
        [string]$Description
    )

    if (Test-Path $DirectoryPath -PathType Container) {
        Write-Host "[OK] $Description : $DirectoryPath" -ForegroundColor Green
        return $true
    } else {
        Write-Host "[MISSING] $Description : $DirectoryPath" -ForegroundColor Red
        return $false
    }
}

$ErrorCount = 0

Write-Host ""
Write-Host "Backend File Structure Verification" -ForegroundColor Yellow
Write-Host "-----------------------------------" -ForegroundColor Yellow

# Check backend directories
if (!(Test-DirectoryExists "backend\src\ai_coding_agent" "Backend source code")) { $ErrorCount++ }
if (!(Test-DirectoryExists "backend\config" "Backend configuration")) { $ErrorCount++ }
if (!(Test-DirectoryExists "backend\scripts" "Backend scripts")) { $ErrorCount++ }
if (!(Test-DirectoryExists "backend\tests" "Backend tests")) { $ErrorCount++ }

# Check critical backend files
if (!(Test-FileExists "backend\Dockerfile" "Backend Dockerfile")) { $ErrorCount++ }
if (!(Test-FileExists "backend\requirements.txt" "Backend requirements")) { $ErrorCount++ }
if (!(Test-FileExists "backend\src\ai_coding_agent\main.py" "Main application file")) { $ErrorCount++ }
if (!(Test-FileExists "backend\src\ai_coding_agent\__init__.py" "Package init file")) { $ErrorCount++ }
if (!(Test-FileExists "backend\src\ai_coding_agent\config.py" "Configuration module")) { $ErrorCount++ }

# Check backend scripts
if (!(Test-FileExists "backend\scripts\test_migration.py" "Migration test script")) { $ErrorCount++ }
if (!(Test-FileExists "backend\scripts\setup_pgvector.sql" "pgvector setup script")) { $ErrorCount++ }
if (!(Test-FileExists "backend\scripts\migrate_db.py" "Database migration script")) { $ErrorCount++ }

# Check backend services
if (!(Test-FileExists "backend\src\ai_coding_agent\services\vector_db.py" "Vector DB service")) { $ErrorCount++ }
if (!(Test-FileExists "backend\src\ai_coding_agent\services\redis_cache.py" "Redis cache service")) { $ErrorCount++ }

Write-Host ""
Write-Host "Frontend File Structure Verification" -ForegroundColor Yellow
Write-Host "------------------------------------" -ForegroundColor Yellow

# Check frontend directories
if (!(Test-DirectoryExists "frontend\src" "Frontend source code")) { $ErrorCount++ }
if (!(Test-DirectoryExists "frontend\public" "Frontend public assets")) { $ErrorCount++ }

# Check critical frontend files
if (!(Test-FileExists "frontend\Dockerfile" "Frontend Dockerfile")) { $ErrorCount++ }
if (!(Test-FileExists "frontend\package.json" "Frontend package.json")) { $ErrorCount++ }
if (!(Test-FileExists "frontend\nginx.conf" "Nginx configuration")) { $ErrorCount++ }

# Check for React app file (JavaScript or TypeScript)
if (Test-Path "frontend\src\App.js" -PathType Leaf) {
    Write-Host "[OK] Main React component : frontend\src\App.js" -ForegroundColor Green
} elseif (Test-Path "frontend\src\App.jsx" -PathType Leaf) {
    Write-Host "[OK] Main React component : frontend\src\App.jsx" -ForegroundColor Green
} elseif (Test-Path "frontend\src\App.tsx" -PathType Leaf) {
    Write-Host "[OK] Main React component : frontend\src\App.tsx" -ForegroundColor Green
} elseif (Test-Path "frontend\src\App.ts" -PathType Leaf) {
    Write-Host "[OK] Main React component : frontend\src\App.ts" -ForegroundColor Green
} else {
    Write-Host "[MISSING] Main React component : frontend\src\App.js/jsx/ts/tsx" -ForegroundColor Red
    $ErrorCount++
}

Write-Host ""
Write-Host "Docker Configuration Verification" -ForegroundColor Yellow
Write-Host "---------------------------------" -ForegroundColor Yellow

# Check Docker files
if (!(Test-FileExists "docker-compose.yml" "Docker Compose configuration")) { $ErrorCount++ }
if (!(Test-FileExists "docker-compose.dev.yml" "Docker Compose development configuration")) { $ErrorCount++ }

# Check environment files
if (Test-Path ".env" -PathType Leaf) {
    Write-Host "[OK] Environment file : .env" -ForegroundColor Green

    # Check for required environment variables
    Write-Host ""
    Write-Host "Environment Variables Check:" -ForegroundColor Blue

    $envContent = Get-Content ".env" -Raw

    if ($envContent -match "SECRET_KEY=") {
        Write-Host "[OK] SECRET_KEY found in .env" -ForegroundColor Green
    } else {
        Write-Host "[MISSING] SECRET_KEY missing in .env" -ForegroundColor Red
        $ErrorCount++
    }

    if ($envContent -match "CONFIG_ENCRYPTION_KEY=") {
        Write-Host "[OK] CONFIG_ENCRYPTION_KEY found in .env" -ForegroundColor Green
    } else {
        Write-Host "[MISSING] CONFIG_ENCRYPTION_KEY missing in .env" -ForegroundColor Red
        $ErrorCount++
    }

    if ($envContent -match "DB_PASSWORD=") {
        Write-Host "[OK] DB_PASSWORD found in .env" -ForegroundColor Green
    } else {
        Write-Host "[WARNING] DB_PASSWORD not found in .env (will use default)" -ForegroundColor Yellow
    }
} else {
    Write-Host "[MISSING] Environment file : .env" -ForegroundColor Red
    Write-Host "Create .env file with required variables:" -ForegroundColor Yellow
    Write-Host "SECRET_KEY=your-secret-key-here-32-chars-min"
    Write-Host "CONFIG_ENCRYPTION_KEY=your-encryption-key-32-chars-min"
    Write-Host "DB_PASSWORD=your-database-password"
    $ErrorCount++
}

Write-Host ""
Write-Host "Volume Mount Directories" -ForegroundColor Yellow
Write-Host "-----------------------" -ForegroundColor Yellow

# Check volume mount directories
if (!(Test-DirectoryExists "user-projects" "User projects directory")) { $ErrorCount++ }
if (!(Test-DirectoryExists "database\init" "Database initialization scripts")) { $ErrorCount++ }

Write-Host ""
Write-Host "=======================================================" -ForegroundColor Cyan
Write-Host "Pre-Build Verification Complete!" -ForegroundColor Cyan
Write-Host ""

if ($ErrorCount -eq 0) {
    Write-Host "SUCCESS: All checks passed! Ready to build containers." -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. docker-compose build --no-cache"
    Write-Host "2. docker-compose up -d"
    Write-Host "3. .\scripts\Verify-Containers.ps1"
} else {
    Write-Host "ERROR: Found $ErrorCount issues. Please fix them before building." -ForegroundColor Red
    Write-Host ""
    Write-Host "Common fixes:" -ForegroundColor Yellow
    Write-Host "1. Ensure all source files are in the correct directories"
    Write-Host "2. Create missing .env file with required variables"
    Write-Host "3. Check file permissions and paths"
}
Write-Host "=======================================================" -ForegroundColor Cyan
