# PowerShell Container Verification Script for AI Coding Agent
# This script verifies that all essential files are properly included in containers
# Usage: .\scripts\Verify-Containers.ps1

param(
    [switch]$Quick
)

Write-Host "🔍 AI Coding Agent - Container File Verification" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Function to check if container is running
function Test-ContainerRunning {
    param([string]$ContainerName)
    
    try {
        $result = docker-compose ps --services --filter "status=running" 2>$null
        if ($result -contains $ContainerName) {
            Write-Host "✅ $ContainerName container is running" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $ContainerName container is not running" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error checking $ContainerName container status" -ForegroundColor Red
        return $false
    }
}

# Function to verify file exists in container
function Test-FileInContainer {
    param(
        [string]$ContainerName,
        [string]$FilePath,
        [string]$Description
    )
    
    try {
        $result = docker-compose exec -T $ContainerName test -f $FilePath 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description`: $FilePath" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Missing $Description`: $FilePath" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error checking $Description`: $FilePath" -ForegroundColor Red
        return $false
    }
}

# Function to verify directory exists in container
function Test-DirectoryInContainer {
    param(
        [string]$ContainerName,
        [string]$DirectoryPath,
        [string]$Description
    )
    
    try {
        $result = docker-compose exec -T $ContainerName test -d $DirectoryPath 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description`: $DirectoryPath" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Missing $Description`: $DirectoryPath" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error checking $Description`: $DirectoryPath" -ForegroundColor Red
        return $false
    }
}

# Function to list directory contents in container
function Show-ContainerDirectory {
    param(
        [string]$ContainerName,
        [string]$DirectoryPath,
        [string]$Description
    )
    
    Write-Host "📁 $Description ($DirectoryPath):" -ForegroundColor Blue
    try {
        docker-compose exec -T $ContainerName ls -la $DirectoryPath 2>$null
    } catch {
        Write-Host "❌ Cannot access directory: $DirectoryPath" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🐳 Checking Container Status" -ForegroundColor Yellow
Write-Host "----------------------------" -ForegroundColor Yellow

# Check if Docker is running
try {
    docker version >$null 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Docker is not installed or not in PATH." -ForegroundColor Red
    exit 1
}

# Check if containers are running
$backendRunning = Test-ContainerRunning "backend"
$frontendRunning = Test-ContainerRunning "frontend"

Write-Host ""
Write-Host "🔧 Backend Container Verification" -ForegroundColor Yellow
Write-Host "--------------------------------" -ForegroundColor Yellow

if ($backendRunning) {
    # Verify backend file structure
    Test-DirectoryInContainer "backend" "/app" "App root directory"
    Test-DirectoryInContainer "backend" "/app/ai_coding_agent" "Main application code"
    Test-DirectoryInContainer "backend" "/app/config" "Configuration directory"
    Test-DirectoryInContainer "backend" "/app/scripts" "Scripts directory"
    Test-DirectoryInContainer "backend" "/app/tests" "Tests directory"
    
    # Verify key files
    Test-FileInContainer "backend" "/app/requirements.txt" "Requirements file"
    Test-FileInContainer "backend" "/app/ai_coding_agent/main.py" "Main application file"
    Test-FileInContainer "backend" "/app/ai_coding_agent/__init__.py" "Package init file"
    Test-FileInContainer "backend" "/app/scripts/test_migration.py" "Migration test script"
    Test-FileInContainer "backend" "/app/scripts/setup_pgvector.sql" "pgvector setup script"
    
    # Verify service files
    Test-FileInContainer "backend" "/app/ai_coding_agent/services/vector_db.py" "Vector DB service"
    Test-FileInContainer "backend" "/app/ai_coding_agent/services/redis_cache.py" "Redis cache service"
    Test-FileInContainer "backend" "/app/ai_coding_agent/config.py" "Configuration module"
    
    if (!$Quick) {
        # List key directories
        Write-Host ""
        Show-ContainerDirectory "backend" "/app" "App root"
        Write-Host ""
        Show-ContainerDirectory "backend" "/app/ai_coding_agent" "Main application"
        Write-Host ""
        Show-ContainerDirectory "backend" "/app/scripts" "Scripts"
    }
    
    # Check Python dependencies
    Write-Host ""
    Write-Host "📦 Checking Python Dependencies:" -ForegroundColor Blue
    
    $dependencies = @(
        @{Name="Redis"; Import="redis"},
        @{Name="Supabase"; Import="supabase"},
        @{Name="LangChain"; Import="langchain"},
        @{Name="AsyncPG"; Import="asyncpg"}
    )
    
    foreach ($dep in $dependencies) {
        try {
            docker-compose exec -T backend python -c "import $($dep.Import); print('✅ $($dep.Name) installed')" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ $($dep.Name) installed" -ForegroundColor Green
            } else {
                Write-Host "❌ $($dep.Name) not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ $($dep.Name) not installed" -ForegroundColor Red
        }
    }
} else {
    Write-Host "⚠️ Backend container not running - skipping verification" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🌐 Frontend Container Verification" -ForegroundColor Yellow
Write-Host "---------------------------------" -ForegroundColor Yellow

if ($frontendRunning) {
    # Verify frontend file structure
    Test-DirectoryInContainer "frontend" "/usr/share/nginx/html" "Nginx web root"
    Test-FileInContainer "frontend" "/usr/share/nginx/html/index.html" "Main HTML file"
    Test-FileInContainer "frontend" "/etc/nginx/conf.d/default.conf" "Nginx configuration"
    
    if (!$Quick) {
        # List web root contents
        Write-Host ""
        Show-ContainerDirectory "frontend" "/usr/share/nginx/html" "Web root contents"
    }
    
    # Check if static assets exist
    Write-Host ""
    Write-Host "📦 Checking Frontend Assets:" -ForegroundColor Blue
    
    try {
        $jsFiles = docker-compose exec -T frontend find /usr/share/nginx/html -name "*.js" 2>$null
        if ($jsFiles) {
            Write-Host "✅ JavaScript files found" -ForegroundColor Green
        } else {
            Write-Host "❌ No JavaScript files found" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error checking JavaScript files" -ForegroundColor Red
    }
    
    try {
        $cssFiles = docker-compose exec -T frontend find /usr/share/nginx/html -name "*.css" 2>$null
        if ($cssFiles) {
            Write-Host "✅ CSS files found" -ForegroundColor Green
        } else {
            Write-Host "❌ No CSS files found" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error checking CSS files" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ Frontend container not running - skipping verification" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🧪 Quick Functionality Tests" -ForegroundColor Yellow
Write-Host "---------------------------" -ForegroundColor Yellow

if ($backendRunning) {
    Write-Host "Testing backend health endpoint..."
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/health" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Backend health check passed" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Backend health check returned status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Backend health check failed (may still be starting)" -ForegroundColor Yellow
    }
}

if ($frontendRunning) {
    Write-Host "Testing frontend accessibility..."
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Frontend accessible on port 3000" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Frontend returned status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Frontend not accessible on port 3000" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host "🎯 Verification Complete!" -ForegroundColor Cyan
Write-Host ""
Write-Host "If you see any ❌ errors above, run the following commands:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Rebuild containers with all files:"
Write-Host "   docker-compose down"
Write-Host "   docker-compose build --no-cache"
Write-Host "   docker-compose up -d"
Write-Host ""
Write-Host "2. Check specific issues:"
Write-Host "   docker-compose logs backend"
Write-Host "   docker-compose logs frontend"
Write-Host ""
Write-Host "3. Verify file copying manually:"
Write-Host "   docker-compose exec backend ls -la /app/"
Write-Host "   docker-compose exec frontend ls -la /usr/share/nginx/html/"
Write-Host "=================================================" -ForegroundColor Cyan
