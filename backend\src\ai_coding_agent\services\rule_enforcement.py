"""
Universal Rule Enforcement System for AI Coding Agent.

This module ensures ALL LLMs (local Ollama and cloud models) follow the same:
- System rules and guardrails
- Orchestration workflow
- Security policies
- Quality standards
- Agent-specific constraints

CRITICAL: Every model interaction MUST go through this enforcement layer.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path
from enum import Enum

from pydantic import BaseModel, Field
from ..config import settings

# Configure rule enforcement logger
rule_logger = logging.getLogger("rule_enforcement")
rule_logger.setLevel(logging.INFO)


class RuleViolationType(str, Enum):
    """Types of rule violations."""
    SECURITY_VIOLATION = "security_violation"
    WORKFLOW_VIOLATION = "workflow_violation"
    QUALITY_VIOLATION = "quality_violation"
    AGENT_CONSTRAINT_VIOLATION = "agent_constraint_violation"
    OUTPUT_FORMAT_VIOLATION = "output_format_violation"


class RuleViolation(BaseModel):
    """Represents a rule violation."""
    violation_type: RuleViolationType
    severity: str  # "low", "medium", "high", "critical"
    description: str
    rule_id: str
    suggested_fix: Optional[str] = None


class AgentConstraints(BaseModel):
    """Agent-specific constraints and rules."""
    agent_name: str
    allowed_actions: List[str]
    forbidden_actions: List[str]
    output_format_requirements: Dict[str, Any]
    security_level: str  # "low", "medium", "high", "critical"
    max_execution_time: int  # seconds
    requires_user_approval: List[str]  # actions requiring approval


class SystemRules(BaseModel):
    """Core system rules that ALL models must follow."""

    # Security Rules
    never_expose_secrets: bool = True
    never_execute_dangerous_commands: bool = True
    always_validate_user_input: bool = True
    require_admin_for_system_changes: bool = True

    # Orchestration Rules
    enforce_sequential_execution: bool = True
    prevent_parallel_agent_execution: bool = True
    require_task_completion_verification: bool = True
    maintain_audit_trail: bool = True

    # Quality Rules
    require_structured_output: bool = True
    enforce_code_quality_standards: bool = True
    require_error_handling: bool = True
    mandate_documentation: bool = True

    # Agent Workflow Rules
    respect_agent_specialization: bool = True
    follow_task_routing: bool = True
    use_proper_handoffs: bool = True
    maintain_context_continuity: bool = True


class RuleEnforcementEngine:
    """
    Core engine that enforces rules across all LLM interactions.

    This engine:
    1. Validates all prompts before sending to any LLM
    2. Enforces system rules and agent constraints
    3. Validates all LLM outputs against rules
    4. Blocks or modifies rule-violating content
    5. Logs all violations for security monitoring
    """

    def __init__(self):
        """Initialize the rule enforcement engine."""
        self.system_rules = SystemRules()
        self.agent_constraints = self._load_agent_constraints()
        self.violation_history: List[RuleViolation] = []

    def _load_agent_constraints(self) -> Dict[str, AgentConstraints]:
        """Load agent-specific constraints from configuration."""
        return {
            "architect": AgentConstraints(
                agent_name="architect",
                allowed_actions=[
                    "project_planning", "roadmap_creation", "agent_coordination",
                    "requirements_analysis", "architecture_design"
                ],
                forbidden_actions=[
                    "direct_code_execution", "file_system_modification",
                    "network_requests", "database_changes"
                ],
                output_format_requirements={
                    "must_include": ["task_breakdown", "agent_assignments", "timeline"],
                    "format": "structured_json"
                },
                security_level="high",
                max_execution_time=300,
                requires_user_approval=["project_structure_changes", "major_architecture_decisions"]
            ),
            "frontend": AgentConstraints(
                agent_name="frontend",
                allowed_actions=[
                    "ui_component_creation", "react_development", "css_styling",
                    "frontend_testing", "responsive_design"
                ],
                forbidden_actions=[
                    "backend_api_calls", "database_access", "server_configuration",
                    "security_policy_changes"
                ],
                output_format_requirements={
                    "must_include": ["component_code", "styling", "tests"],
                    "format": "code_with_metadata"
                },
                security_level="medium",
                max_execution_time=180,
                requires_user_approval=["major_ui_changes", "new_dependencies"]
            ),
            "backend": AgentConstraints(
                agent_name="backend",
                allowed_actions=[
                    "api_development", "database_design", "business_logic",
                    "security_implementation", "performance_optimization"
                ],
                forbidden_actions=[
                    "frontend_modifications", "ui_changes", "direct_user_interaction"
                ],
                output_format_requirements={
                    "must_include": ["api_endpoints", "data_models", "security_measures"],
                    "format": "code_with_documentation"
                },
                security_level="critical",
                max_execution_time=300,
                requires_user_approval=["database_schema_changes", "security_policy_updates"]
            ),
            "shell": AgentConstraints(
                agent_name="shell",
                allowed_actions=[
                    "safe_command_execution", "file_operations", "environment_setup",
                    "dependency_installation", "build_processes"
                ],
                forbidden_actions=[
                    "system_administration", "user_management", "network_configuration",
                    "security_bypass_attempts"
                ],
                output_format_requirements={
                    "must_include": ["commands_executed", "results", "safety_checks"],
                    "format": "execution_log"
                },
                security_level="critical",
                max_execution_time=120,
                requires_user_approval=["system_modifications", "new_software_installation"]
            ),
            "debug": AgentConstraints(
                agent_name="debug",
                allowed_actions=[
                    "error_analysis", "code_debugging", "performance_profiling",
                    "log_analysis", "issue_identification"
                ],
                forbidden_actions=[
                    "code_modification", "system_changes", "data_deletion"
                ],
                output_format_requirements={
                    "must_include": ["issue_analysis", "root_cause", "suggested_fixes"],
                    "format": "diagnostic_report"
                },
                security_level="medium",
                max_execution_time=240,
                requires_user_approval=["major_refactoring_suggestions"]
            ),
            "test": AgentConstraints(
                agent_name="test",
                allowed_actions=[
                    "test_creation", "test_execution", "coverage_analysis",
                    "quality_assessment", "test_strategy_development"
                ],
                forbidden_actions=[
                    "production_testing", "destructive_testing", "security_testing"
                ],
                output_format_requirements={
                    "must_include": ["test_cases", "coverage_report", "quality_metrics"],
                    "format": "test_suite_with_results"
                },
                security_level="medium",
                max_execution_time=180,
                requires_user_approval=["integration_testing", "load_testing"]
            )
        }

    def validate_prompt(self, prompt: str, agent_name: str, task_type: str) -> tuple[bool, List[RuleViolation]]:
        """
        Validate a prompt before sending to any LLM.

        Args:
            prompt: The prompt to validate
            agent_name: Target agent name
            task_type: Type of task being requested

        Returns:
            tuple: (is_valid, list_of_violations)
        """
        violations = []

        # Check agent constraints
        if agent_name in self.agent_constraints:
            constraints = self.agent_constraints[agent_name]

            # Check if task type is allowed for this agent
            if task_type not in constraints.allowed_actions:
                violations.append(RuleViolation(
                    violation_type=RuleViolationType.AGENT_CONSTRAINT_VIOLATION,
                    severity="high",
                    description=f"Task type '{task_type}' not allowed for agent '{agent_name}'",
                    rule_id="agent_action_constraint",
                    suggested_fix=f"Route this task to an appropriate agent that handles '{task_type}'"
                ))

        # Check for security violations in prompt
        security_violations = self._check_security_violations(prompt)
        violations.extend(security_violations)

        # Check workflow compliance
        workflow_violations = self._check_workflow_compliance(prompt, agent_name)
        violations.extend(workflow_violations)

        # Log violations
        for violation in violations:
            rule_logger.warning(f"Prompt validation violation: {violation.description}")
            self.violation_history.append(violation)

        return len(violations) == 0, violations

    def _check_security_violations(self, prompt: str) -> List[RuleViolation]:
        """Check for security violations in the prompt."""
        violations = []

        # Check for dangerous commands
        dangerous_patterns = [
            "rm -rf", "sudo", "chmod 777", "eval(", "exec(",
            "os.system", "subprocess.call", "shell=True"
        ]

        for pattern in dangerous_patterns:
            if pattern in prompt.lower():
                violations.append(RuleViolation(
                    violation_type=RuleViolationType.SECURITY_VIOLATION,
                    severity="critical",
                    description=f"Dangerous pattern detected: {pattern}",
                    rule_id="dangerous_command_detection",
                    suggested_fix="Remove dangerous commands or use safe alternatives"
                ))

        return violations

    def _check_workflow_compliance(self, prompt: str, agent_name: str) -> List[RuleViolation]:
        """Check if prompt follows orchestration workflow rules."""
        violations = []

        # Check for parallel execution requests (forbidden)
        parallel_indicators = ["parallel", "concurrent", "simultaneously", "at the same time"]
        for indicator in parallel_indicators:
            if indicator in prompt.lower():
                violations.append(RuleViolation(
                    violation_type=RuleViolationType.WORKFLOW_VIOLATION,
                    severity="high",
                    description="Parallel execution requested but forbidden by system rules",
                    rule_id="sequential_execution_enforcement",
                    suggested_fix="Modify request to use sequential execution"
                ))

        return violations

    def validate_output(self, output: str, agent_name: str, task_type: str) -> tuple[bool, List[RuleViolation]]:
        """
        Validate LLM output against system rules and agent constraints.

        Args:
            output: The LLM output to validate
            agent_name: Agent that generated the output
            task_type: Type of task that was performed

        Returns:
            tuple: (is_valid, list_of_violations)
        """
        violations = []

        # Check output format requirements
        if agent_name in self.agent_constraints:
            constraints = self.agent_constraints[agent_name]
            format_violations = self._check_output_format(output, constraints)
            violations.extend(format_violations)

        # Check for security violations in output
        security_violations = self._check_output_security(output)
        violations.extend(security_violations)

        # Check quality standards
        quality_violations = self._check_quality_standards(output, task_type)
        violations.extend(quality_violations)

        # Log violations
        for violation in violations:
            rule_logger.error(f"Output validation violation: {violation.description}")
            self.violation_history.append(violation)

        return len(violations) == 0, violations

    def _check_output_format(self, output: str, constraints: AgentConstraints) -> List[RuleViolation]:
        """Check if output meets format requirements."""
        violations = []

        format_reqs = constraints.output_format_requirements
        required_fields = format_reqs.get("must_include", [])

        for field in required_fields:
            if field not in output.lower():
                violations.append(RuleViolation(
                    violation_type=RuleViolationType.OUTPUT_FORMAT_VIOLATION,
                    severity="medium",
                    description=f"Required field '{field}' missing from output",
                    rule_id="output_format_compliance",
                    suggested_fix=f"Include '{field}' in the output"
                ))

        return violations

    def _check_output_security(self, output: str) -> List[RuleViolation]:
        """Check output for security violations."""
        violations = []

        # Check for exposed secrets
        secret_patterns = [
            r"sk-[a-zA-Z0-9]{32,}",  # OpenAI API keys
            r"sk-ant-[a-zA-Z0-9]{32,}",  # Anthropic API keys
            r"password\s*[:=]\s*['\"][^'\"]+['\"]",  # Passwords
            r"api_key\s*[:=]\s*['\"][^'\"]+['\"]",  # API keys
        ]

        import re
        for pattern in secret_patterns:
            if re.search(pattern, output, re.IGNORECASE):
                violations.append(RuleViolation(
                    violation_type=RuleViolationType.SECURITY_VIOLATION,
                    severity="critical",
                    description="Potential secret or API key exposed in output",
                    rule_id="secret_exposure_prevention",
                    suggested_fix="Remove or mask sensitive information"
                ))

        return violations

    def _check_quality_standards(self, output: str, task_type: str) -> List[RuleViolation]:
        """Check output against quality standards."""
        violations = []

        # Check for minimum content length
        if len(output.strip()) < 50:
            violations.append(RuleViolation(
                violation_type=RuleViolationType.QUALITY_VIOLATION,
                severity="medium",
                description="Output too short to be meaningful",
                rule_id="minimum_content_length",
                suggested_fix="Provide more detailed and comprehensive output"
            ))

        return violations

    def inject_system_rules(self, prompt: str, agent_name: str, task_type: str) -> str:
        """
        Inject system rules and constraints into the prompt.

        This ensures ALL LLMs (local and cloud) receive the same rules.
        """
        # Load copilot rules
        copilot_rules = self._load_copilot_rules()

        # Get agent-specific constraints
        agent_constraints = self.agent_constraints.get(agent_name, None)

        # Build enhanced prompt with rules
        enhanced_prompt = f"""
{copilot_rules}

AGENT ROLE: {agent_name.upper()}
TASK TYPE: {task_type}

CRITICAL SYSTEM RULES - MUST FOLLOW:
1. SECURITY FIRST: Never expose secrets, API keys, or sensitive data
2. SEQUENTIAL EXECUTION: Never suggest parallel agent execution
3. AGENT SPECIALIZATION: Stay within your agent's capabilities
4. STRUCTURED OUTPUT: Always provide structured, well-formatted responses
5. ERROR HANDLING: Include proper error handling in all code
6. DOCUMENTATION: Document all code and decisions
7. USER APPROVAL: Flag actions requiring user approval

{f"AGENT CONSTRAINTS: {agent_constraints.dict() if agent_constraints else 'None'}" }

ORIGINAL TASK:
{prompt}

RESPONSE REQUIREMENTS:
- Follow all system rules above
- Provide structured output appropriate for your agent role
- Include error handling and validation
- Document your approach and decisions
- Flag any actions requiring user approval
"""

        return enhanced_prompt

    def _load_copilot_rules(self) -> str:
        """Load copilot rules from .copilot-rules.md file."""
        try:
            copilot_file = Path(".copilot-rules.md")
            if copilot_file.exists():
                return copilot_file.read_text()
            else:
                return "# Default System Rules\n- Follow security best practices\n- Maintain code quality\n- Document all work"
        except Exception as e:
            rule_logger.warning(f"Could not load copilot rules: {e}")
            return "# Default System Rules\n- Follow security best practices"

    def get_violation_summary(self) -> Dict[str, Any]:
        """Get summary of rule violations for monitoring."""
        if not self.violation_history:
            return {"total_violations": 0, "by_type": {}, "by_severity": {}}

        by_type = {}
        by_severity = {}

        for violation in self.violation_history:
            # Count by type
            vtype = violation.violation_type.value
            by_type[vtype] = by_type.get(vtype, 0) + 1

            # Count by severity
            severity = violation.severity
            by_severity[severity] = by_severity.get(severity, 0) + 1

        return {
            "total_violations": len(self.violation_history),
            "by_type": by_type,
            "by_severity": by_severity,
            "recent_violations": [v.dict() for v in self.violation_history[-5:]]
        }


# Global rule enforcement engine instance
rule_engine = RuleEnforcementEngine()
