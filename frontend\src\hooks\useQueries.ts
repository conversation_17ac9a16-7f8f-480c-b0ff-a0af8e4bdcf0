import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '../services/api';
import { queryKeys } from '../lib/queryClient';

// Health check query
export const useHealthCheck = () => {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: () => apiService.healthCheck(),
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000, // Consider stale after 10 seconds
  });
};

// Mutation for invalidating health check cache
export const useInvalidateHealthCheck = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      // This is just a helper to manually invalidate cache
      await queryClient.invalidateQueries({ queryKey: queryKeys.health });
    },
  });
};

// Hook for prefetching health check
export const usePrefetchHealthCheck = () => {
  const queryClient = useQueryClient();

  const prefetchHealthCheck = () => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.health,
      queryFn: () => apiService.healthCheck(),
      staleTime: 10000,
    });
  };

  return prefetchHealthCheck;
};

// Example hook for user profile (placeholder for future implementation)
export const useUserProfile = (userId?: string) => {
  return useQuery({
    queryKey: userId ? queryKeys.userProfile(userId) : ['user', 'profile'],
    queryFn: async () => {
      // Placeholder - implement when user profile API is available
      throw new Error('User profile API not implemented yet');
    },
    enabled: false, // Disabled until API is implemented
  });
};

// Dashboard data hook (placeholder for future implementation)
export const useDashboardData = () => {
  return useQuery({
    queryKey: queryKeys.dashboard,
    queryFn: async () => {
      // Placeholder - implement when dashboard API is available
      return {
        projects: [],
        recentActivity: [],
        stats: {
          totalProjects: 0,
          activeProjects: 0,
          completedProjects: 0,
        },
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
