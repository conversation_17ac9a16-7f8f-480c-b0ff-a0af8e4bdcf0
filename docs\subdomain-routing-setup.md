# Subdomain Routing Setup Guide

## Overview

This guide explains how to set up and test the container-per-user subdomain routing system. Each user's project runs in an isolated container and is accessible via a unique subdomain.

## Architecture

```
User Request: http://preview-user123.localhost
     ↓
NGINX Reverse Proxy
     ↓
Authentication Check (Backend API)
     ↓
Port Resolution (Backend API)
     ↓
User Container (Dynamic Port)
```

## Local Development Setup

### 1. DNS Configuration

For local development, you need to configure DNS to route subdomains to localhost.

#### Option A: Hosts File (Simple)

Add entries to your hosts file:

**Windows:** `C:\Windows\System32\drivers\etc\hosts`
**Linux/Mac:** `/etc/hosts`

```
127.0.0.1 preview-test-user-123.localhost
127.0.0.1 preview-user456.localhost
127.0.0.1 preview-demo.localhost
```

#### Option B: Wildcard DNS (Recommended)

Use a service like `dnsmasq` for wildcard subdomain resolution:

**Linux/Mac:**
```bash
# Install dnsmasq
sudo apt-get install dnsmasq  # Ubuntu/Debian
brew install dnsmasq          # macOS

# Configure wildcard resolution
echo 'address=/.localhost/127.0.0.1' | sudo tee -a /etc/dnsmasq.conf

# Restart dnsmasq
sudo systemctl restart dnsmasq  # Linux
sudo brew services restart dnsmasq  # macOS
```

**Windows:**
Use Acrylic DNS Proxy or similar tool for wildcard DNS.

### 2. Docker Compose Configuration

Ensure your `docker-compose.yml` includes the NGINX service:

```yaml
services:
  nginx:
    image: nginx:alpine
    container_name: ai-coding-agent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./infrastructure/nginx/user-projects.conf:/etc/nginx/conf.d/user-projects.conf:ro
    networks:
      - ai-coding-agent-network
    depends_on:
      - backend
      - frontend
```

### 3. Start the System

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs nginx
docker-compose logs backend
```

## Testing the Subdomain Routing

### 1. Run the Test Script

```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Install test dependencies
pip install aiohttp

# Run tests
python scripts/test_subdomain_routing.py
```

### 2. Manual Testing

#### Step 1: Provision a Container

```bash
curl -X POST http://localhost:8000/api/v1/containers/provision \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "project_type": "react",
    "project_name": "test-project"
  }'
```

#### Step 2: Test Subdomain Access

```bash
# Test subdomain routing
curl -v http://preview-YOUR-USER-ID.localhost

# Check NGINX auth endpoint
curl -H "X-User-ID: YOUR-USER-ID" \
     -H "X-Real-IP: 127.0.0.1" \
     http://localhost:8000/api/v1/containers/auth-check

# Check port resolution
curl -H "X-User-ID: YOUR-USER-ID" \
     http://localhost:8000/api/v1/containers/get-port
```

## Security Features

### 1. Authentication
- All subdomain requests require valid user authentication
- NGINX validates access via backend API before proxying

### 2. User Isolation
- Each user gets a dedicated container
- Network isolation between user containers
- Resource limits prevent resource exhaustion

### 3. Rate Limiting
- Per-IP rate limiting for subdomain requests
- Per-user rate limiting for container access
- Connection limits to prevent abuse

## Troubleshooting

### Common Issues

#### 1. Subdomain Not Resolving
```bash
# Check DNS resolution
nslookup preview-test.localhost
ping preview-test.localhost

# Verify hosts file or DNS configuration
```

#### 2. NGINX 502 Bad Gateway
```bash
# Check if user container is running
docker ps | grep user-

# Check container logs
docker logs user-YOUR-USER-ID-react

# Verify port mapping
docker port user-YOUR-USER-ID-react
```

#### 3. Authentication Failures
```bash
# Check backend logs
docker-compose logs backend

# Test auth endpoint directly
curl -v http://localhost:8000/api/v1/containers/auth-check \
  -H "X-User-ID: test-user"
```

#### 4. Port Resolution Issues
```bash
# Check container manager status
curl http://localhost:8000/api/v1/containers/health

# Verify Docker connectivity
docker version
docker network ls
```

### Debug Commands

```bash
# Check NGINX configuration
docker exec ai-coding-agent-nginx nginx -t

# View NGINX access logs
docker exec ai-coding-agent-nginx tail -f /var/log/nginx/access.log

# View user project logs
docker exec ai-coding-agent-nginx tail -f /var/log/nginx/user-projects.log

# Test container connectivity
docker exec ai-coding-agent-nginx wget -qO- http://backend:8000/api/v1/containers/health
```

## Production Considerations

### 1. SSL/HTTPS
- Configure SSL certificates for production domains
- Use Let's Encrypt for automatic certificate management
- Update NGINX configuration for HTTPS

### 2. Domain Configuration
- Replace `.localhost` with your production domain
- Configure proper DNS records for wildcard subdomains
- Set up CDN if needed for static assets

### 3. Security Hardening
- Implement proper authentication and authorization
- Add request signing for NGINX-backend communication
- Configure firewall rules for container network

### 4. Monitoring
- Set up monitoring for container health
- Monitor subdomain access patterns
- Alert on authentication failures or unusual activity

## Next Steps

1. **Integration Testing**: Test full user workflow end-to-end
2. **Performance Testing**: Load test subdomain routing under high traffic
3. **Security Audit**: Verify container isolation and access controls
4. **Production Deployment**: Configure for production environment

The subdomain routing system is now ready for comprehensive testing and production deployment! 🚀
