/**
 * Privacy Settings Component
 * Handles privacy, data collection, and security preferences
 */

import React from 'react';
import { Shield, Eye, Cookie, Database, Users, Link } from 'lucide-react';
import { usePreferences } from '../../contexts/PreferencesContext';

const privacyOptions = [
  {
    key: 'analytics' as const,
    icon: Database,
    title: 'Analytics Data',
    description: 'Allow collection of usage analytics to improve the service',
  },
  {
    key: 'cookies' as const,
    icon: <PERSON><PERSON>,
    title: 'Cookies',
    description: 'Allow cookies for enhanced functionality and preferences',
  },
  {
    key: 'dataCollection' as const,
    icon: Database,
    title: 'Data Collection',
    description: 'Allow collection of additional data for personalization',
  },
  {
    key: 'thirdPartyIntegrations' as const,
    icon: Link,
    title: 'Third-party Integrations',
    description: 'Allow integrations with external services',
  },
];

const profileVisibilityOptions = [
  {
    value: 'public',
    label: 'Public',
    description: 'Your profile is visible to everyone',
  },
  {
    value: 'private',
    label: 'Private',
    description: 'Your profile is only visible to you',
  },
  {
    value: 'friends',
    label: 'Friends Only',
    description: 'Your profile is visible to your connections only',
  },
];

export const PrivacySettings: React.FC = () => {
  const { preferences, updateSection } = usePreferences();
  const { privacy } = preferences;

  const handleToggle = (key: keyof Omit<typeof privacy, 'profileVisibility'>) => {
    updateSection('privacy', {
      [key]: !privacy[key],
    });
  };

  const handleProfileVisibilityChange = (visibility: string) => {
    updateSection('privacy', {
      profileVisibility: visibility as any
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Privacy Settings
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Control your privacy and data sharing preferences
        </p>
      </div>

      {/* Privacy Options */}
      <div className="space-y-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white">
          Data & Analytics
        </h4>

        {privacyOptions.map((option) => {
          const Icon = option.icon;
          const isEnabled = privacy[option.key];

          return (
            <div
              key={option.key}
              className="flex items-start justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div className="flex items-start gap-3">
                <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400 mt-0.5" />
                <div>
                  <h5 className="text-md font-medium text-gray-900 dark:text-white">
                    {option.title}
                  </h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {option.description}
                  </p>
                </div>
              </div>

              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isEnabled}
                  onChange={() => handleToggle(option.key)}
                  className="sr-only peer"
                />
                <div className={`
                  relative w-11 h-6 rounded-full peer transition-colors duration-200 ease-in-out
                  ${isEnabled
                    ? 'bg-indigo-600'
                    : 'bg-gray-200 dark:bg-gray-700'
                  }
                  peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800
                `}>
                  <div className={`
                    absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 transition-transform duration-200 ease-in-out
                    ${isEnabled ? 'translate-x-full border-white' : 'translate-x-0'}
                  `} />
                </div>
              </label>
            </div>
          );
        })}
      </div>

      {/* Profile Visibility */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
          <Users className="h-4 w-4" />
          Profile Visibility
        </h4>
        <div className="space-y-3">
          {profileVisibilityOptions.map((option) => (
            <label key={option.value} className="flex items-start">
              <input
                type="radio"
                name="profileVisibility"
                value={option.value}
                checked={privacy.profileVisibility === option.value}
                onChange={(e) => handleProfileVisibilityChange(e.target.value)}
                className="mt-1 mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <div>
                <div className="text-gray-900 dark:text-white font-medium">
                  {option.label}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {option.description}
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Data Management */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Data Management
        </h4>

        <div className="space-y-4">
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-start gap-3">
              <Eye className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
              <div>
                <h5 className="text-md font-medium text-yellow-800 dark:text-yellow-200">
                  Data Retention
                </h5>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  Your data is retained according to our privacy policy. You can request data deletion at any time.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button className="p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <h6 className="font-medium text-gray-900 dark:text-white">
                Download Your Data
              </h6>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Export all your personal data
              </p>
            </button>

            <button className="p-3 border border-red-300 dark:border-red-600 rounded-lg text-left hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
              <h6 className="font-medium text-red-700 dark:text-red-400">
                Delete Account Data
              </h6>
              <p className="text-sm text-red-600 dark:text-red-500 mt-1">
                Permanently delete your account
              </p>
            </button>
          </div>
        </div>
      </div>

      {/* Security Settings */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Security Options
        </h4>

        <div className="space-y-4">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                defaultChecked
                className="mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <div>
                <span className="text-gray-900 dark:text-white font-medium">
                  Two-Factor Authentication
                </span>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Add an extra layer of security to your account
                </p>
              </div>
            </label>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                defaultChecked
                className="mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <div>
                <span className="text-gray-900 dark:text-white font-medium">
                  Login Notifications
                </span>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Get notified when someone logs into your account
                </p>
              </div>
            </label>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <div>
                <span className="text-gray-900 dark:text-white font-medium">
                  Device Trust
                </span>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Remember trusted devices for faster login
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};
