# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Documentation
docs/_build/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Node modules (if any)
node_modules/

# Jupyter notebooks
.ipynb_checkpoints/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# Backup files
*.bak
*.backup
