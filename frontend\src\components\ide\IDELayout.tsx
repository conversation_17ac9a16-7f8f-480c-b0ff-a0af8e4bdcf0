import React, { useState, useCallback, useEffect } from 'react';
import {
  Code,
  Folder,
  MessageSquare,
  Activity,
  Play,
  Square,
  Settings,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { AICodeEditor } from './AICodeEditor';
import { FileExplorer } from './FileExplorer';
import { AIChatPanel } from './AIChatPanel';
import AgentCollaborationGraph from '../ui/AgentCollaborationGraph';
import AgentCollaborationMetrics from '../ui/AgentCollaborationMetrics';
import {
  FileNode,
  EditorTab,
  IDEState,
  AgentAction,
  AIAssistantMessage,
  ProjectPreview,
  AICodeAssistanceRequest
} from '../../types/ide';
import { AgentRole } from '../../types/agents';
import { agentService } from '../../services/agentService';

interface IDELayoutProps {
  className?: string;
}

interface TabBarProps {
  tabs: EditorTab[];
  activeTabId: string | null;
  onTabSelect: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
}

interface StatusBarProps {
  currentFile?: string;
  agentActivity: string[];
  projectStatus: string;
}

interface ProjectPreviewPanelProps {
  preview: ProjectPreview | null;
  onRefresh: () => void;
}

const TabBar: React.FC<TabBarProps> = ({ tabs, activeTabId, onTabSelect, onTabClose }) => {
  return (
    <div className="flex bg-gray-800 border-b border-gray-700 overflow-x-auto">
      {tabs.map((tab) => (
        <div
          key={tab.id}
          className={`
            flex items-center px-3 py-2 border-r border-gray-700 cursor-pointer min-w-[120px] max-w-[200px]
            ${tab.active ? 'bg-gray-900 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
          `}
          onClick={() => onTabSelect(tab.id)}
        >
          <Code size={14} className="mr-2 flex-shrink-0" />
          <span className="truncate text-sm" title={tab.name}>
            {tab.name}
          </span>
          {tab.modified && <span className="ml-1 text-yellow-400 text-xs">●</span>}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onTabClose(tab.id);
            }}
            className="ml-2 p-1 hover:bg-gray-600 rounded flex-shrink-0"
          >
            <X size={12} />
          </button>
        </div>
      ))}
    </div>
  );
};

const StatusBar: React.FC<StatusBarProps> = ({ currentFile, agentActivity, projectStatus }) => {
  return (
    <div className="bg-blue-600 text-white px-3 py-1 text-xs flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <span>AI Coding Agent IDE</span>
        {currentFile && (
          <span className="text-blue-200">
            {currentFile}
          </span>
        )}
        <span className={`px-2 py-1 rounded ${
          projectStatus === 'running' ? 'bg-green-600' :
          projectStatus === 'error' ? 'bg-red-600' : 'bg-gray-600'
        }`}>
          {projectStatus}
        </span>
      </div>

      <div className="flex items-center space-x-4">
        {agentActivity.length > 0 && (
          <div className="flex items-center space-x-2">
            <Activity size={12} className="animate-pulse" />
            <span>{agentActivity[0]}</span>
          </div>
        )}
        <span>Ready</span>
      </div>
    </div>
  );
};

const ProjectPreviewPanel: React.FC<ProjectPreviewPanelProps> = ({ preview, onRefresh }) => {
  if (!preview) {
    return (
      <div className="h-full bg-gray-900 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <Play size={48} className="mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Preview Available</h3>
          <p className="text-sm">Start your project to see a live preview</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-white">
      <div className="h-full w-full border-0" style={{ minHeight: '400px' }}>
        {preview.type === 'web' && preview.url ? (
          <iframe
            src={preview.url}
            className="w-full h-full border-0"
            title="Project Preview"
            sandbox="allow-scripts allow-same-origin allow-forms"
          />
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500">
            <div className="text-center">
              <Play size={48} className="mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Preview Loading...</h3>
              <p className="text-sm">Preparing your project preview</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export const IDELayout: React.FC<IDELayoutProps> = ({ className = '' }) => {
  const [ideState, setIdeState] = useState<IDEState>({
    openTabs: [],
    activeTabId: null,
    sidebarVisible: true,
    rightPanelVisible: true,
    agentCollaborationVisible: true,
    theme: 'dark',
    files: [],
    aiChat: {
      messages: [],
      isLoading: false,
    },
    projectPreview: null,
  });

  const [agentActions, setAgentActions] = useState<AgentAction[]>([]);
  const [agentActivity, setAgentActivity] = useState<string[]>([]);
  const [isCollaborationMaximized, setIsCollaborationMaximized] = useState(false);

  // Initialize sample data
  useEffect(() => {
    const sampleFiles: FileNode[] = [
      {
        id: '1',
        name: 'src',
        type: 'folder',
        path: '/src',
        children: [
          {
            id: '2',
            name: 'components',
            type: 'folder',
            path: '/src/components',
            children: [
              { id: '3', name: 'App.tsx', type: 'file', path: '/src/components/App.tsx' },
              { id: '4', name: 'Header.tsx', type: 'file', path: '/src/components/Header.tsx' },
            ]
          },
          { id: '5', name: 'index.tsx', type: 'file', path: '/src/index.tsx' },
          { id: '6', name: 'styles.css', type: 'file', path: '/src/styles.css' },
        ]
      },
      {
        id: '7',
        name: 'public',
        type: 'folder',
        path: '/public',
        children: [
          { id: '8', name: 'index.html', type: 'file', path: '/public/index.html' },
        ]
      },
      { id: '9', name: 'package.json', type: 'file', path: '/package.json' },
      { id: '10', name: 'README.md', type: 'file', path: '/README.md' },
    ];

    setIdeState(prev => ({ ...prev, files: sampleFiles }));

    // Sample agent activity
    const activities = [
      'Frontend Agent: Updating component styles...',
      'Backend Agent: Processing API requests...',
      'Test Agent: Running automated tests...',
    ];
    setAgentActivity(activities);
  }, []);

  const activeTab = ideState.openTabs.find(tab => tab.active);

  const handleFileSelect = useCallback((file: FileNode) => {
    if (file.type === 'file') {
      // Check if file is already open
      const existingTab = ideState.openTabs.find(tab => tab.path === file.path);

      if (existingTab) {
        // Switch to existing tab
        setIdeState(prev => ({
          ...prev,
          activeTabId: existingTab.id,
          openTabs: prev.openTabs.map(tab => ({
            ...tab,
            active: tab.id === existingTab.id
          }))
        }));
      } else {
        // Create new tab
        const newTab: EditorTab = {
          id: `tab-${Date.now()}`,
          name: file.name,
          path: file.path,
          content: `// ${file.name}\n// This is a sample file content\n\nfunction example() {\n  console.log('Hello from ${file.name}');\n}\n\nexport default example;`,
          language: file.name.endsWith('.tsx') ? 'typescript' :
                   file.name.endsWith('.css') ? 'css' :
                   file.name.endsWith('.json') ? 'json' :
                   file.name.endsWith('.md') ? 'markdown' : 'typescript',
          active: true,
          modified: false,
        };

        setIdeState(prev => ({
          ...prev,
          activeTabId: newTab.id,
          openTabs: [
            ...prev.openTabs.map(tab => ({ ...tab, active: false })),
            newTab
          ]
        }));
      }
    }
  }, [ideState.openTabs]);

  const handleTabSelect = useCallback((tabId: string) => {
    setIdeState(prev => ({
      ...prev,
      activeTabId: tabId,
      openTabs: prev.openTabs.map(tab => ({
        ...tab,
        active: tab.id === tabId
      }))
    }));
  }, []);

  const handleTabClose = useCallback((tabId: string) => {
    setIdeState(prev => {
      const tabIndex = prev.openTabs.findIndex(tab => tab.id === tabId);
      const newTabs = prev.openTabs.filter(tab => tab.id !== tabId);

      let newActiveTabId = prev.activeTabId;
      if (prev.activeTabId === tabId) {
        if (newTabs.length > 0) {
          const newActiveIndex = Math.min(tabIndex, newTabs.length - 1);
          newActiveTabId = newTabs[newActiveIndex].id;
          newTabs[newActiveIndex] = { ...newTabs[newActiveIndex], active: true };
        } else {
          newActiveTabId = null;
        }
      }

      return {
        ...prev,
        activeTabId: newActiveTabId,
        openTabs: newTabs
      };
    });
  }, []);

  const handleContentChange = useCallback((content: string) => {
    setIdeState(prev => ({
      ...prev,
      openTabs: prev.openTabs.map(tab =>
        tab.active
          ? { ...tab, content, modified: true }
          : tab
      )
    }));
  }, []);

  const handleSendMessage = useCallback(async (message: string, agentRole?: AgentRole) => {
    const userMessage: AIAssistantMessage = {
      id: `msg-${Date.now()}`,
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setIdeState(prev => ({
      ...prev,
      aiChat: {
        ...prev.aiChat,
        messages: [...prev.aiChat.messages, userMessage]
      }
    }));

    try {
      const response = await agentService.executeTask({
        task_description: message,
        preferred_agent: agentRole,
        context: {
          currentFile: ideState.openTabs.find(tab => tab.active)?.path,
          projectType: 'web'
        }
      });

      const assistantMessage: AIAssistantMessage = {
        id: `msg-${Date.now()}-assistant`,
        type: 'assistant',
        content: response.result || 'No response from agent',
        timestamp: new Date(),
        agentRole: response.assignedAgent,
        metadata: {
          taskId: response.id,
          executionTime: 1500, // Mock execution time
          tokenCount: response.result?.length || 0
        }
      };

      setIdeState(prev => ({
        ...prev,
        aiChat: {
          ...prev.aiChat,
          messages: [...prev.aiChat.messages, assistantMessage]
        }
      }));
    } catch (error) {
      console.error('Failed to send message:', error);

      const errorMessage: AIAssistantMessage = {
        id: `msg-${Date.now()}-error`,
        type: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date(),
        isError: true
      };

      setIdeState(prev => ({
        ...prev,
        aiChat: {
          ...prev.aiChat,
          messages: [...prev.aiChat.messages, errorMessage]
        }
      }));
    }
  }, [ideState.openTabs]);

  return (
    <div className={`h-screen w-screen flex flex-col bg-gray-900 text-white overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-semibold">AI Coding Agent IDE</h1>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIdeState(prev => ({ ...prev, sidebarVisible: !prev.sidebarVisible }))}
              className={`p-2 rounded hover:bg-gray-700 ${ideState.sidebarVisible ? 'text-blue-400' : 'text-gray-400'}`}
            >
              <Folder size={16} />
            </button>
            <button
              onClick={() => setIdeState(prev => ({ ...prev, agentCollaborationVisible: !prev.agentCollaborationVisible }))}
              className={`p-2 rounded hover:bg-gray-700 ${ideState.agentCollaborationVisible ? 'text-blue-400' : 'text-gray-400'}`}
            >
              <Activity size={16} />
            </button>
            <button
              onClick={() => setIdeState(prev => ({ ...prev, rightPanelVisible: !prev.rightPanelVisible }))}
              className={`p-2 rounded hover:bg-gray-700 ${ideState.rightPanelVisible ? 'text-blue-400' : 'text-gray-400'}`}
            >
              <MessageSquare size={16} />
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button className="p-2 rounded hover:bg-gray-700 text-gray-400">
            <Settings size={16} />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        {ideState.sidebarVisible && (
          <div className="w-64 border-r border-gray-700 flex flex-col">
            <FileExplorer
              files={ideState.files}
              onFileSelect={handleFileSelect}
              onFileCreate={() => {}}
              onFileDelete={() => {}}
              onFileRename={() => {}}
              onRefresh={() => {}}
              selectedFile={activeTab?.path}
            />
          </div>
        )}

        {/* Center Panel */}
        <div className="flex-1 flex flex-col">
          {/* Agent Collaboration Graph */}
          {ideState.agentCollaborationVisible && (
            <div className={`border-b border-gray-700 ${isCollaborationMaximized ? 'flex-1' : 'h-64'}`}>
              <div className="h-full bg-gray-800 relative">
                <div className="absolute top-2 right-2 z-10 flex space-x-1">
                  <button
                    onClick={() => setIsCollaborationMaximized(!isCollaborationMaximized)}
                    className="p-1 bg-gray-700 hover:bg-gray-600 rounded text-white"
                  >
                    {isCollaborationMaximized ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
                  </button>
                  <button
                    onClick={() => setIdeState(prev => ({ ...prev, agentCollaborationVisible: false }))}
                    className="p-1 bg-gray-700 hover:bg-gray-600 rounded text-white"
                  >
                    <X size={14} />
                  </button>
                </div>
                <div className="h-full flex">
                  <div className="flex-1">
                    <AgentCollaborationGraph />
                  </div>
                  <div className="w-80 border-l border-gray-700">
                    <AgentCollaborationMetrics />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Editor Area */}
          {!isCollaborationMaximized && (
            <div className="flex-1 flex flex-col">
              {/* Tab Bar */}
              {ideState.openTabs.length > 0 && (
                <TabBar
                  tabs={ideState.openTabs}
                  activeTabId={ideState.activeTabId}
                  onTabSelect={handleTabSelect}
                  onTabClose={handleTabClose}
                />
              )}

              {/* Editor or Preview Split */}
              <div className="flex-1 flex">
                {/* Code Editor */}
                <div className="flex-1">
                  {activeTab ? (
                    <AICodeEditor
                      tab={activeTab}
                      onContentChange={handleContentChange}
                      onSave={() => console.log('Save file')}
                      theme={ideState.theme}
                      agentActions={agentActions}
                      onRequestAIAssistance={(request: AICodeAssistanceRequest) => {
                        handleSendMessage(`Help me with this code: ${request.context.selectedText || ''}`);
                      }}
                    />
                  ) : (
                    <div className="h-full bg-gray-900 flex items-center justify-center text-gray-500">
                      <div className="text-center">
                        <Code size={64} className="mx-auto mb-4 text-gray-600" />
                        <h2 className="text-xl mb-2">Welcome to AI Coding Agent IDE</h2>
                        <p>Open a file to start coding with AI assistance</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Project Preview */}
                {ideState.projectPreview && (
                  <div className="w-1/2 border-l border-gray-700">
                    <div className="bg-gray-800 px-3 py-2 border-b border-gray-700 flex items-center justify-between">
                      <span className="text-sm font-medium">Preview</span>
                      <button
                        onClick={() => {/* TODO: Refresh preview */}}
                        className="p-1 hover:bg-gray-700 rounded"
                      >
                        <Play size={14} />
                      </button>
                    </div>
                    <ProjectPreviewPanel
                      preview={ideState.projectPreview}
                      onRefresh={() => {}}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - AI Chat */}
        {ideState.rightPanelVisible && (
          <div className="w-80 border-l border-gray-700">
            <AIChatPanel
              chat={ideState.aiChat}
              onSendMessage={handleSendMessage}
              onClearChat={() => setIdeState(prev => ({
                ...prev,
                aiChat: { ...prev.aiChat, messages: [] }
              }))}
              currentFile={activeTab?.path}
              selectedText={activeTab?.content ? 'Selected code...' : undefined}
            />
          </div>
        )}
      </div>

      {/* Status Bar */}
      <StatusBar
        currentFile={activeTab?.name}
        agentActivity={agentActivity}
        projectStatus="ready"
      />
    </div>
  );
};
