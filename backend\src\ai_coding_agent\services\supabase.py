"""
Supabase service for handling cloud database operations.

This service manages the connection to Supabase for storing and retrieving
long-term knowledge, best practices, and shared data across users.
"""

from typing import Dict, List, Optional, Any, TYPE_CHECKING
import asyncio
from datetime import datetime

if TYPE_CHECKING:
    from supabase import Client

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    create_client = None
    Client = None

from ..config import settings


class SupabaseService:
    """Service for managing Supabase cloud database operations."""

    def __init__(self):
        """Initialize Supabase client."""
        if not SUPABASE_AVAILABLE or create_client is None:
            raise ImportError(
                "supabase-py is required for Supabase functionality. "
                "Install with: pip install supabase"
            )

        self.url = settings.supabase.url
        self.key = settings.supabase.anon_key
        self.service_key = settings.supabase.service_role_key

        if not self.url or not self.key:
            raise ValueError(
                "SUPABASE_URL and SUPABASE_ANON_KEY must be set in environment variables"
            )

        # Initialize client with anon key for basic operations
        self.client = create_client(self.url, self.key)

        # Initialize service client for admin operations
        if self.service_key:
            self.service_client = create_client(self.url, self.service_key)
        else:
            self.service_client = self.client

    async def store_best_practice(
        self,
        category: str,
        title: str,
        description: str,
        content: str,
        tags: List[str],
        tech_stack: List[str],
        metadata: Optional[Dict] = None
    ) -> Dict:
        """Store a best practice in Supabase."""
        data = {
            "category": category,
            "title": title,
            "description": description,
            "content": content,
            "tags": tags,
            "tech_stack": tech_stack,
            "metadata": metadata or {},
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        result = self.service_client.table("best_practices").insert(data).execute()
        return result.data[0] if result.data else {}

    async def get_best_practices(
        self,
        category: Optional[str] = None,
        tech_stack: Optional[List[str]] = None,
        tags: Optional[List[str]] = None,
        limit: int = 50
    ) -> List[Dict]:
        """Retrieve best practices from Supabase with filtering."""
        query = self.client.table("best_practices").select("*")

        if category:
            query = query.eq("category", category)

        if tech_stack:
            query = query.overlaps("tech_stack", tech_stack)

        if tags:
            query = query.overlaps("tags", tags)

        result = query.limit(limit).execute()
        return result.data or []

    async def store_tech_stack_metadata(
        self,
        name: str,
        type: str,  # "framework", "library", "tool", "language"
        version: str,
        description: str,
        installation_cmd: str,
        config_template: Dict,
        dependencies: List[str],
        best_practices: List[str],
        documentation_url: str,
        metadata: Optional[Dict] = None
    ) -> Dict:
        """Store tech stack metadata in Supabase."""
        data = {
            "name": name,
            "type": type,
            "version": version,
            "description": description,
            "installation_cmd": installation_cmd,
            "config_template": config_template,
            "dependencies": dependencies,
            "best_practices": best_practices,
            "documentation_url": documentation_url,
            "metadata": metadata or {},
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        result = self.service_client.table("tech_stack_metadata").insert(data).execute()
        return result.data[0] if result.data else {}

    async def get_tech_stack_metadata(
        self,
        name: Optional[str] = None,
        type: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict]:
        """Retrieve tech stack metadata from Supabase."""
        query = self.client.table("tech_stack_metadata").select("*")

        if name:
            query = query.ilike("name", f"%{name}%")

        if type:
            query = query.eq("type", type)

        result = query.limit(limit).execute()
        return result.data or []

    async def store_security_policy(
        self,
        name: str,
        category: str,
        rule_type: str,  # "require", "forbid", "recommend", "warn"
        description: str,
        rule_content: Dict,
        applies_to: List[str],  # ["frontend", "backend", "database", "api"]
        severity: str,  # "low", "medium", "high", "critical"
        metadata: Optional[Dict] = None
    ) -> Dict:
        """Store security policy in Supabase."""
        data = {
            "name": name,
            "category": category,
            "rule_type": rule_type,
            "description": description,
            "rule_content": rule_content,
            "applies_to": applies_to,
            "severity": severity,
            "metadata": metadata or {},
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        result = self.service_client.table("security_policies").insert(data).execute()
        return result.data[0] if result.data else {}

    async def get_security_policies(
        self,
        category: Optional[str] = None,
        applies_to: Optional[List[str]] = None,
        severity: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """Retrieve security policies from Supabase."""
        query = self.client.table("security_policies").select("*")

        if category:
            query = query.eq("category", category)

        if applies_to:
            query = query.overlaps("applies_to", applies_to)

        if severity:
            query = query.eq("severity", severity)

        result = query.limit(limit).execute()
        return result.data or []

    async def search_knowledge_base(
        self,
        search_term: str,
        categories: Optional[List[str]] = None,
        tech_stack: Optional[List[str]] = None,
        limit: int = 20
    ) -> Dict[str, List[Dict]]:
        """Search across all knowledge base tables."""
        results = {
            "best_practices": [],
            "tech_stack_metadata": [],
            "security_policies": []
        }

        # Search best practices
        bp_query = self.client.table("best_practices").select("*").or_(
            f"title.ilike.%{search_term}%,"
            f"description.ilike.%{search_term}%,"
            f"content.ilike.%{search_term}%"
        )

        if categories:
            bp_query = bp_query.in_("category", categories)
        if tech_stack:
            bp_query = bp_query.overlaps("tech_stack", tech_stack)

        bp_result = bp_query.limit(limit).execute()
        results["best_practices"] = bp_result.data or []

        # Search tech stack metadata
        ts_query = self.client.table("tech_stack_metadata").select("*").or_(
            f"name.ilike.%{search_term}%,"
            f"description.ilike.%{search_term}%"
        )

        ts_result = ts_query.limit(limit).execute()
        results["tech_stack_metadata"] = ts_result.data or []

        # Search security policies
        sp_query = self.client.table("security_policies").select("*").or_(
            f"name.ilike.%{search_term}%,"
            f"description.ilike.%{search_term}%"
        )

        sp_result = sp_query.limit(limit).execute()
        results["security_policies"] = sp_result.data or []

        return results

    async def create_tables(self) -> bool:
        """Create necessary tables in Supabase (if they don't exist)."""
        # Note: In production, tables should be created via Supabase dashboard
        # or migrations. This is for development/testing.

        try:
            # Check if tables exist by trying to select from them
            tables = ["best_practices", "tech_stack_metadata", "security_policies"]

            for table in tables:
                try:
                    self.client.table(table).select("id").limit(1).execute()
                except Exception as e:
                    print(f"Table {table} might not exist: {e}")
                    # In a real setup, you'd create tables here or via migration

            return True
        except Exception as e:
            print(f"Error checking/creating tables: {e}")
            return False

    def get_connection_status(self) -> Dict[str, Any]:
        """Get Supabase connection status."""
        try:
            # Simple health check
            result = self.client.table("best_practices").select("id").limit(1).execute()
            return {
                "connected": True,
                "url": self.url,
                "has_data": bool(result.data),
                "error": None
            }
        except Exception as e:
            return {
                "connected": False,
                "url": self.url,
                "has_data": False,
                "error": str(e)
            }


# Global instance
_supabase_service = None


def get_supabase_service() -> SupabaseService:
    """Get or create Supabase service instance."""
    global _supabase_service
    if _supabase_service is None:
        if not SUPABASE_AVAILABLE:
            raise ImportError(
                "supabase-py is required. Install with: pip install supabase"
            )
        _supabase_service = SupabaseService()
    return _supabase_service
