/**
 * Agent Node Component for ReactFlow
 *
 * Displays individual AI agents in the collaboration graph
 */

import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from '@xyflow/react';
import {
  Bot,
  Cpu,
  Terminal,
  TestTube,
  Eye,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import { AgentInfo, AgentRole } from '../../types/agents';

interface AgentNodeData {
  agent: AgentInfo;
  isActive: boolean;
  taskCount: number;
}

const getAgentIcon = (role: AgentRole) => {
  switch (role) {
    case AgentRole.ARCHITECT:
      return Cpu;
    case AgentRole.DEVELOPER:
      return Bot;
    case AgentRole.SHELL:
      return Terminal;
    case AgentRole.TESTER:
      return TestTube;
    case AgentRole.REVIEWER:
      return Eye;
    default:
      return Bot;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-500';
    case 'busy':
      return 'bg-blue-500';
    case 'idle':
      return 'bg-gray-400';
    case 'error':
      return 'bg-red-500';
    default:
      return 'bg-gray-400';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active':
      return CheckCircle;
    case 'busy':
      return Loader;
    case 'idle':
      return Clock;
    case 'error':
      return AlertCircle;
    default:
      return Clock;
  }
};

const AgentNodeComponent: React.FC<NodeProps> = ({ data }) => {
  const nodeData = data as unknown as AgentNodeData;
  const { agent, isActive, taskCount } = nodeData;
  const IconComponent = getAgentIcon(agent.role);
  const StatusIcon = getStatusIcon(agent.status);
  const statusColor = getStatusColor(agent.status);

  return (
    <div className={`
      relative bg-white dark:bg-gray-800 rounded-lg border-2 shadow-lg min-w-48 p-4
      ${isActive ? 'border-blue-500 shadow-blue-200' : 'border-gray-200 dark:border-gray-600'}
      ${isActive ? 'animate-pulse' : ''}
    `}>
      {/* Input/Output Handles */}
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />

      {/* Status Indicator */}
      <div className={`absolute -top-2 -right-2 w-4 h-4 rounded-full ${statusColor} border-2 border-white`}>
        {agent.status === 'busy' && (
          <div className="w-full h-full rounded-full animate-ping bg-current opacity-75"></div>
        )}
      </div>

      {/* Agent Header */}
      <div className="flex items-center space-x-3 mb-3">
        <div className={`p-2 rounded-lg ${isActive ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}>
          <IconComponent size={20} />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
            {agent.name}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
            {agent.role}
          </p>
        </div>
      </div>

      {/* Agent Status */}
      <div className="flex items-center space-x-2 mb-3">
        <StatusIcon size={14} className={`${
          agent.status === 'active' ? 'text-green-500' :
          agent.status === 'busy' ? 'text-blue-500 animate-spin' :
          agent.status === 'error' ? 'text-red-500' : 'text-gray-400'
        }`} />
        <span className={`text-xs font-medium ${
          agent.status === 'active' ? 'text-green-600' :
          agent.status === 'busy' ? 'text-blue-600' :
          agent.status === 'error' ? 'text-red-600' : 'text-gray-500'
        }`}>
          {agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}
        </span>
      </div>

      {/* Task Count */}
      <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
        <span>Active Tasks:</span>
        <span className={`font-medium ${taskCount > 0 ? 'text-blue-600' : 'text-gray-400'}`}>
          {taskCount}
        </span>
      </div>

      {/* Capabilities Preview */}
      <div className="mt-2 flex flex-wrap gap-1">
        {agent.capabilities.slice(0, 2).map((capability, index) => (
          <span
            key={index}
            className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded text-gray-600 dark:text-gray-300"
          >
            {capability.replace('_', ' ')}
          </span>
        ))}
        {agent.capabilities.length > 2 && (
          <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded text-gray-500">
            +{agent.capabilities.length - 2}
          </span>
        )}
      </div>

      {/* Activity Indicator */}
      {isActive && (
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentNodeComponent;
