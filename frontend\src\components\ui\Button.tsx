import React from 'react';
import { cn } from '../../utils/cn';
import { useIsTouchDevice } from '../../utils/responsive';
import { hapticFeedback } from '../../utils/touch';
import { ariaUtils } from '../../utils/accessibility';

/**
 * Enhanced Button component with touch-friendly interactions and accessibility
 */

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  isLoading?: boolean; // For backward compatibility
  loadingText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  haptic?: 'light' | 'medium' | 'heavy' | 'success' | 'error' | false;
  fullWidth?: boolean;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      loading = false,
      isLoading = false, // Backward compatibility
      loadingText,
      icon,
      iconPosition = 'left',
      haptic = 'light',
      fullWidth = false,
      children,
      onClick,
      disabled,
      ...props
    },
    ref
  ) => {
    const isTouch = useIsTouchDevice();
    const isButtonLoading = loading || isLoading;

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (isButtonLoading || disabled) return;

      // Provide haptic feedback on touch devices
      if (isTouch && haptic) {
        switch (haptic) {
          case 'light':
            hapticFeedback.light();
            break;
          case 'medium':
            hapticFeedback.medium();
            break;
          case 'heavy':
            hapticFeedback.heavy();
            break;
          case 'success':
            hapticFeedback.success();
            break;
          case 'error':
            hapticFeedback.error();
            break;
        }
      }

      onClick?.(e);
    };

    const variantClasses = {
      primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white shadow-sm border-transparent',
      secondary: 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500 text-white shadow-sm border-transparent',
      outline: 'border border-gray-300 bg-white hover:bg-gray-50 focus:ring-blue-500 text-gray-700 shadow-sm',
      ghost: 'hover:bg-gray-100 focus:ring-gray-500 text-gray-700 border-transparent',
      danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white shadow-sm border-transparent',
    };

    const darkVariantClasses = {
      primary: 'dark:bg-blue-500 dark:hover:bg-blue-600',
      secondary: 'dark:bg-gray-500 dark:hover:bg-gray-600',
      outline: 'dark:border-gray-600 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-200',
      ghost: 'dark:hover:bg-gray-800 dark:text-gray-200',
      danger: 'dark:bg-red-500 dark:hover:bg-red-600',
    };

    const sizeClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base',
      xl: 'px-8 py-4 text-lg',
    };

    const iconSizeClasses = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-4 h-4',
      lg: 'w-5 h-5',
      xl: 'w-6 h-6',
    };

    const LoadingSpinner = () => (
      <svg
        className={cn('animate-spin', iconSizeClasses[size])}
        fill="none"
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

    return (
      <button
        ref={ref}
        className={cn(
          // Base styles
          'inline-flex items-center justify-center border',
          'font-medium rounded-md transition-all duration-150',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'relative overflow-hidden',

          // Touch-friendly enhancements
          isTouch && 'min-h-[44px] min-w-[44px]',
          isTouch && 'active:scale-95 active:bg-opacity-80 transition-transform duration-75',

          // Variant styles
          variantClasses[variant],
          darkVariantClasses[variant],

          // Size styles
          sizeClasses[size],

          // Full width
          fullWidth && 'w-full',

          // Loading state
          isButtonLoading && 'cursor-wait',

          className
        )}
        onClick={handleClick}
        disabled={disabled || isButtonLoading}
        aria-disabled={disabled || isButtonLoading}
        {...ariaUtils.button}
        {...props}
      >
        {/* Loading content */}
        {isButtonLoading && (
          <>
            <LoadingSpinner />
            {(loadingText || 'Loading...') && (
              <span className="ml-2">{loadingText || 'Loading...'}</span>
            )}
          </>
        )}

        {/* Normal content */}
        {!isButtonLoading && (
          <>
            {icon && iconPosition === 'left' && (
              <span className={cn('flex-shrink-0', children && 'mr-2')}>
                {React.isValidElement(icon)
                  ? React.cloneElement(icon as React.ReactElement<any>, {
                      className: cn(iconSizeClasses[size], (icon as any).props?.className),
                      'aria-hidden': true,
                    })
                  : icon
                }
              </span>
            )}

            <span className="flex-1">{children}</span>

            {icon && iconPosition === 'right' && (
              <span className={cn('flex-shrink-0', children && 'ml-2')}>
                {React.isValidElement(icon)
                  ? React.cloneElement(icon as React.ReactElement<any>, {
                      className: cn(iconSizeClasses[size], (icon as any).props?.className),
                      'aria-hidden': true,
                    })
                  : icon
                }
              </span>
            )}
          </>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
