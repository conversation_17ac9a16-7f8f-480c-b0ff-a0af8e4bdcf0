# AI Coding Agent - Compatible Dependencies (Fallback Version)
# This version uses more stable/compatible package versions

# Core web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Configuration and settings
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0
python-dotenv==1.0.0
pyyaml==6.0.1

# Database - Using asyncpg instead of psycopg2-binary to avoid compilation issues
sqlalchemy==2.0.23
asyncpg==0.29.0
# psycopg2-binary==2.9.10  # COMMENTED OUT - Often causes build failures
alembic==1.13.1

# Supabase (with compatible versions)
supabase==2.3.0

# Redis for caching and real-time features
redis==5.0.1
hiredis==2.2.3

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# File handling
aiofiles==23.2.0

# AI and ML - Using more stable versions
langchain==0.1.0
langchain-community==0.0.13
# langchain-ollama==0.1.0  # COMMENTED OUT - May cause conflicts

# Ollama client
ollama==0.1.7

# HTTP client - Using only one to avoid conflicts
httpx==0.25.2
# aiohttp==3.9.0  # COMMENTED OUT - Avoiding multiple HTTP clients

# Logging
structlog==23.2.0

# Additional stable dependencies
typing-extensions==4.8.0
annotated-types==0.6.0
