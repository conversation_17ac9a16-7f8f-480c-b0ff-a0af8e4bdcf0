import React from 'react';
import { LoadingSpinner } from './ui';

interface PageLoadingProps {
  message?: string;
}

const PageLoading: React.FC<PageLoadingProps> = ({
  message = 'Loading page...'
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        <p className="text-gray-600 dark:text-gray-400 text-lg">
          {message}
        </p>
      </div>
    </div>
  );
};

export default PageLoading;
