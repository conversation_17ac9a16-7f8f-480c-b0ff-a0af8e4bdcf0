#!/usr/bin/env python3
"""
Test script to verify all critical imports are working correctly.
This helps identify any remaining import path issues.
"""

import sys
import os
from pathlib import Path

# Add backend src to path
backend_src = Path(__file__).parent / "backend" / "src"
sys.path.insert(0, str(backend_src))

def test_import(module_name, description):
    """Test importing a module and report the result."""
    try:
        __import__(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except Exception as e:
        print(f"❌ {description}: {module_name} - {e}")
        return False

def main():
    """Run all import tests."""
    print("🔍 Testing AI Coding Agent Import Structure")
    print("=" * 50)
    
    # Set minimal environment variables to avoid validation errors
    os.environ.setdefault("SECRET_KEY", "test_secret_key_that_is_at_least_32_characters_long")
    os.environ.setdefault("CONFIG_ENCRYPTION_KEY", "test_encryption_key_32_chars_long")
    
    tests = [
        ("ai_coding_agent.config", "Configuration"),
        ("ai_coding_agent.models", "Database Models"),
        ("ai_coding_agent.models.base", "Database Base"),
        ("ai_coding_agent.models.user", "User Model"),
        ("ai_coding_agent.models.roadmap", "Roadmap Models"),
        ("ai_coding_agent.models.audit", "Audit Models"),
        ("ai_coding_agent.services.auth", "Authentication Service"),
        ("ai_coding_agent.services.user", "User Service"),
        ("ai_coding_agent.services.vector_db", "Vector Database Service"),
        ("ai_coding_agent.services.supabase", "Supabase Service"),
        ("ai_coding_agent.routers.health", "Health Router"),
        ("ai_coding_agent.routers.auth", "Auth Router"),
        ("ai_coding_agent.routers.ai", "AI Router"),
        ("ai_coding_agent.routers.admin", "Admin Router"),
        ("ai_coding_agent.orchestrator", "Orchestrator"),
        ("ai_coding_agent.main", "Main Application"),
    ]
    
    passed = 0
    total = len(tests)
    
    for module_name, description in tests:
        if test_import(module_name, description):
            passed += 1
    
    print("=" * 50)
    print(f"📊 Results: {passed}/{total} imports successful")
    
    if passed == total:
        print("🎉 All imports working correctly!")
        return 0
    else:
        print("⚠️  Some imports failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    exit(main())
