#!/usr/bin/env python3
"""
Test Import Paths - Verify all import paths work correctly after restructuring
"""

import sys
import traceback
from pathlib import Path

# Add backend/src to Python path for development testing
backend_src = Path(__file__).parent.parent.parent / "backend" / "src"
if backend_src.exists():
    sys.path.insert(0, str(backend_src))

def test_basic_imports():
    """Test basic module imports."""
    print("🧪 Testing basic module imports...")

    try:
        # Test basic config import
        from ai_coding_agent.config import settings
        print("  ✅ Config imported successfully")

        # Test model imports
        from ai_coding_agent.models.roadmap import Project, Task, TaskStatus
        print("  ✅ Roadmap models imported successfully")

        # Test service imports
        from ai_coding_agent.services.roadmap import RoadmapService
        print("  ✅ Roadmap service imported successfully")

        return True

    except Exception as e:
        print(f"  ❌ Basic import failed: {e}")
        traceback.print_exc()
        return False

def test_agent_imports():
    """Test agent-related imports."""
    print("\n🤖 Testing agent imports...")

    try:
        from ai_coding_agent.agents import AGENT_CONFIGS, AgentRole
        print(f"  ✅ Agent configs imported: {len(AGENT_CONFIGS)} agents")

        from ai_coding_agent.orchestrator import EnhancedOrchestrator
        print("  ✅ Orchestrator imported successfully")

        return True

    except Exception as e:
        print(f"  ❌ Agent import failed: {e}")
        traceback.print_exc()
        return False

def test_service_imports():
    """Test service layer imports."""
    print("\n🔧 Testing service imports...")

    try:
        from ai_coding_agent.services.vector_db import VectorDBClient
        print("  ✅ Vector DB service imported successfully")

        from ai_coding_agent.services.ai.providers.ollama import OllamaProvider
        print("  ✅ Ollama provider imported successfully")

        return True

    except Exception as e:
        print(f"  ❌ Service import failed: {e}")
        traceback.print_exc()
        return False

def test_router_imports():
    """Test router imports."""
    print("\n🌐 Testing router imports...")

    try:
        from ai_coding_agent.routers.health import router as health_router
        print("  ✅ Health router imported successfully")

        from ai_coding_agent.routers.roadmap import router as roadmap_router
        print("  ✅ Roadmap router imported successfully")

        return True

    except Exception as e:
        print(f"  ❌ Router import failed: {e}")
        traceback.print_exc()
        return False

def test_main_app():
    """Test main application import."""
    print("\n🚀 Testing main application...")

    try:
        from ai_coding_agent.main import app
        print("  ✅ Main FastAPI app imported successfully")

        return True

    except Exception as e:
        print(f"  ❌ Main app import failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all import tests."""
    print("🔍 AI Coding Agent - Import Path Verification")
    print("=" * 50)

    tests = [
        ("Basic Imports", test_basic_imports),
        ("Agent Imports", test_agent_imports),
        ("Service Imports", test_service_imports),
        ("Router Imports", test_router_imports),
        ("Main App", test_main_app),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")

    print("\n" + "=" * 50)
    print(f"📊 Import Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All import paths are working correctly!")
        print("\n✅ The restructuring was successful!")
        print("✅ All modules can be imported properly")
        print("✅ Ready for multi-service Docker deployment")
        return True
    else:
        print(f"❌ {total - passed} import tests failed")
        print("Please check the import paths and module structure")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
