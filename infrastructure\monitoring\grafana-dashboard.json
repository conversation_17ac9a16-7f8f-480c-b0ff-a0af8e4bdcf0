{"dashboard": {"id": null, "title": "AI Coding Agent - System Overview", "tags": ["ai-coding-agent", "monitoring", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Health Overview", "type": "stat", "targets": [{"expr": "up", "legendFormat": "{{job}} - {{instance}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN"}}, "type": "value"}, {"options": {"1": {"text": "UP"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate (req/sec)", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{job}} - {{method}} {{status}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Error Rate (%)", "type": "graph", "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100", "legendFormat": "{{job}} Error Rate"}], "yAxes": [{"label": "Error Rate (%)", "min": 0, "max": 100}, {"show": false}], "alert": {"conditions": [{"evaluator": {"params": [5], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "10s", "handler": 1, "name": "High Error Rate Alert", "noDataState": "no_data", "notifications": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Response Time (95th percentile)", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "{{job}} - 95th percentile"}], "yAxes": [{"label": "Response Time (s)", "min": 0}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "CPU Usage (%)", "type": "graph", "targets": [{"expr": "100 - (avg by(instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "{{instance}} CPU Usage"}], "yAxes": [{"label": "CPU Usage (%)", "min": 0, "max": 100}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Memory Usage (%)", "type": "graph", "targets": [{"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "{{instance}} Memory Usage"}], "yAxes": [{"label": "Memory Usage (%)", "min": 0, "max": 100}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "AI Service Metrics", "type": "table", "targets": [{"expr": "up{job=~\"ollama|vector-db|ai-.*\"}", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true}, "indexByName": {}, "renameByName": {"job": "Service", "instance": "Instance", "Value": "Status"}}}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 8, "title": "Vector Database Performance", "type": "graph", "targets": [{"expr": "rate(vector_db_queries_total[5m])", "legendFormat": "Queries/sec"}, {"expr": "vector_db_embeddings_count", "legendFormat": "Total Embeddings"}], "yAxes": [{"label": "Queries/sec", "min": 0}, {"label": "Embeddings Count", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 9, "title": "Ollama Model Usage", "type": "graph", "targets": [{"expr": "rate(ollama_requests_total[5m])", "legendFormat": "{{model}} - Requests/sec"}, {"expr": "ollama_request_duration_seconds", "legendFormat": "{{model}} - Duration"}], "yAxes": [{"label": "Requests/sec", "min": 0}, {"label": "Duration (s)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 10, "title": "Database Connections", "type": "graph", "targets": [{"expr": "postgres_connections_active", "legendFormat": "Active Connections"}, {"expr": "postgres_connections_idle", "legendFormat": "Idle Connections"}], "yAxes": [{"label": "Connections", "min": 0}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}}, {"id": 11, "title": "Disk Usage", "type": "graph", "targets": [{"expr": "(1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100", "legendFormat": "{{instance}} - {{mountpoint}}"}], "yAxes": [{"label": "Disk Usage (%)", "min": 0, "max": 100}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}}, {"id": 12, "title": "Network I/O", "type": "graph", "targets": [{"expr": "rate(node_network_receive_bytes_total[5m])", "legendFormat": "{{instance}} - {{device}} RX"}, {"expr": "rate(node_network_transmit_bytes_total[5m])", "legendFormat": "{{instance}} - {{device}} TX"}], "yAxes": [{"label": "Bytes/sec", "min": 0}, {"show": false}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 48}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up, instance)", "refresh": 1, "includeAll": true, "multi": true}, {"name": "job", "type": "query", "query": "label_values(up, job)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "enable": true, "expr": "changes(up[1m]) > 0", "iconColor": "blue", "titleFormat": "Service {{job}} state changed"}]}}}