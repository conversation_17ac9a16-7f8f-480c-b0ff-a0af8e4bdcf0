# Authentication System Migration Guide

## 🎯 **Current State**

Your AI Coding Agent has **two authentication systems**:

### **Primary System: Supabase Auth** ✅
- **File**: `src/ai_coding_agent/services/supabase_auth.py`
- **Router**: `src/ai_coding_agent/routers/supabase_auth.py`
- **Endpoint**: `/api/v1/auth/*`
- **Purpose**: Modern cloud-based authentication with social login

### **Legacy System: Local Auth** 🔄
- **File**: `src/ai_coding_agent/services/auth.py`
- **Router**: `src/ai_coding_agent/routers/auth.py`
- **Endpoint**: `/api/v1/legacy-auth/*`
- **Purpose**: Core JWT utilities + backward compatibility

---

## 🔧 **What Each File Does**

### **`supabase_auth.py` - KEEP** ✅
```python
# Primary authentication system
- User registration via Supabase
- Social authentication (Google, GitHub)
- Password reset & email verification
- User sync between Supabase ↔ Local DB
- Modern security features
```

### **`auth.py` - REFACTOR** 🔄
```python
# Core utilities (keep these)
- create_access_token()
- verify_token()
- get_password_hash()
- get_current_user() - NEEDED FOR ADMIN
- get_current_superuser() - NEEDED FOR ADMIN

# Router endpoints (can remove)
- /register, /login, /refresh - REDUNDANT
```

---

## 📋 **Migration Steps**

### **Phase 1: Immediate (No Breaking Changes)**
1. ✅ **Updated main.py** - Supabase auth is now primary at `/api/v1/auth`
2. ✅ **Legacy auth moved** to `/api/v1/legacy-auth` for compatibility
3. ✅ **Admin system uses** `auth.py` utilities (no change needed)

### **Phase 2: Frontend Migration (Optional)**
```typescript
// Update frontend to use primary auth endpoints
// OLD: /api/v1/supabase-auth/login
// NEW: /api/v1/auth/login

// Update in frontend/src/services/supabaseAuth.ts
const API_BASE = '/api/v1/auth';  // Changed from /api/v1/supabase-auth
```

### **Phase 3: Cleanup (Future)**
```bash
# When ready to remove legacy completely:
1. Remove auth.router from main.py
2. Keep only utility functions in auth.py
3. Rename auth.py → jwt_utils.py
4. Update imports in admin middleware
```

---

## 🚨 **IMPORTANT: Don't Remove Yet**

### **Why Keep Both For Now:**

1. **Admin System Dependency** 🔐
   ```python
   # admin_auth.py uses these from auth.py:
   from ..services.auth import get_current_user
   ```

2. **JWT Utilities Shared** 🔑
   ```python
   # supabase_auth.py imports from auth.py:
   from ..services import auth as legacy_auth
   legacy_auth.create_access_token(...)
   ```

3. **Backward Compatibility** 🔄
   ```python
   # Legacy endpoints still work:
   /api/v1/legacy-auth/login
   /api/v1/legacy-auth/register
   ```

---

## 🎯 **Recommended Architecture**

```
Authentication System
├── supabase_auth.py (PRIMARY)
│   ├── User registration/login
│   ├── Social authentication  
│   ├── Password reset
│   └── User management
│
├── auth.py (UTILITIES)
│   ├── JWT token creation/verification
│   ├── Password hashing
│   ├── get_current_user (for admin)
│   └── get_current_superuser (for admin)
│
└── admin_auth.py (ADMIN ONLY)
    ├── get_current_admin_user
    └── Admin-specific dependencies
```

---

## 🧪 **Testing Both Systems**

### **Test Primary Auth (Supabase)**
```bash
# Register new user
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","username":"testuser"}'

# Login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'
```

### **Test Legacy Auth (Backward Compatibility)**
```bash
# Legacy login (still works)
curl -X POST http://localhost:8000/api/v1/legacy-auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'
```

### **Test Admin Auth (Uses auth.py utilities)**
```bash
# Admin endpoint (uses get_current_user from auth.py)
curl -X GET http://localhost:8000/api/v1/admin/auth/check \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## 🔮 **Future Cleanup Plan**

### **When You're Ready (Phase 3):**

1. **Rename Files**
   ```bash
   mv src/ai_coding_agent/services/auth.py src/ai_coding_agent/services/jwt_utils.py
   ```

2. **Update Imports**
   ```python
   # In admin_auth.py
   from ..services.jwt_utils import get_current_user
   
   # In supabase_auth.py  
   from ..services import jwt_utils
   ```

3. **Remove Legacy Router**
   ```python
   # Remove from main.py
   # app.include_router(auth.router, prefix="/api/v1/legacy-auth")
   ```

---

## ✅ **Current Status: OPTIMAL**

Your current setup is actually **well-architected**:
- ✅ Modern Supabase auth for users
- ✅ Shared JWT utilities for consistency  
- ✅ Admin system properly secured
- ✅ Backward compatibility maintained
- ✅ Clear separation of concerns

**Recommendation**: Keep both files as-is for now. The system works well and provides flexibility.
