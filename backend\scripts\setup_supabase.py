"""
Supabase Setup and Configuration Helper.

This script helps configure Supabase credentials and test the connection.
"""

import os
from pathlib import Path

def setup_supabase_env():
    """Set up Supabase environment variables."""
    print("🔧 Supabase Setup Helper")
    print("=" * 40)

    env_path = Path(".env")

    print(f"📁 Environment file: {env_path.absolute()}")
    print(f"   Exists: {'✓' if env_path.exists() else '✗'}")

    if env_path.exists():
        # Read existing .env file
        with open(env_path, 'r') as f:
            env_content = f.read()

        # Check for existing Supabase variables
        has_url = "SUPABASE_URL=" in env_content
        has_key = "SUPABASE_ANON_KEY=" in env_content

        print(f"   SUPABASE_URL configured: {'✓' if has_url else '✗'}")
        print(f"   SUPABASE_ANON_KEY configured: {'✓' if has_key else '✗'}")

        if has_url and has_key:
            print("✅ Supabase already configured!")
            return True
    else:
        env_content = ""

    print("\n📝 To configure Supabase:")
    print("1. Create a new project at https://supabase.com")
    print("2. Go to Settings > API")
    print("3. Copy your Project URL and anon/public key")
    print("4. Add to your .env file:")
    print()
    print("SUPABASE_URL=https://your-project.supabase.co")
    print("SUPABASE_ANON_KEY=your-anon-key-here")
    print()

    # Optionally prompt for manual input
    try:
        setup_now = input("Would you like to set up Supabase credentials now? (y/N): ").lower().strip()

        if setup_now == 'y':
            url = input("Enter your Supabase URL: ").strip()
            key = input("Enter your Supabase anon key: ").strip()

            if url and key:
                # Add to .env file
                if not env_content.endswith('\n') and env_content:
                    env_content += '\n'

                env_content += f"\n# Supabase Configuration\n"
                env_content += f"SUPABASE_URL={url}\n"
                env_content += f"SUPABASE_ANON_KEY={key}\n"

                with open(env_path, 'w') as f:
                    f.write(env_content)

                print("✅ Supabase credentials saved to .env file")
                return True
            else:
                print("⚠️  Incomplete credentials provided")

    except KeyboardInterrupt:
        print("\n⚠️  Setup cancelled")

    return False


def check_supabase_configuration():
    """Check current Supabase configuration."""
    print("\n🔍 Checking Current Configuration...")

    # Load from environment or .env file
    from ai_coding_agent.config import settings

    url = settings.supabase.url
    key = settings.supabase.anon_key

    print(f"   SUPABASE_URL: {'✓ Set' if url else '✗ Missing'}")
    print(f"   SUPABASE_ANON_KEY: {'✓ Set' if key else '✗ Missing'}")

    if url:
        print(f"   URL: {url}")
    if key:
        print(f"   Key: {key[:20]}..." if len(key) > 20 else key)

    return bool(url and key)


def main():
    """Main setup function."""
    print("🚀 Supabase Configuration Helper")
    print("=" * 50)

    # Check current configuration
    configured = check_supabase_configuration()

    if not configured:
        # Setup if not configured
        setup_supabase_env()

        # Re-check after setup
        configured = check_supabase_configuration()

    if configured:
        print("\n🎉 Supabase is configured!")
        print("   You can now run: python test_supabase_auth_simple.py")
    else:
        print("\n⚠️  Supabase not configured")
        print("   Please set up your credentials to continue")


if __name__ == "__main__":
    main()
