# AI Agent Architecture - Specialized Model Assignments

## Overview

The AI Coding Agent system now uses **5 specialized AI models** orchestrated across **6 distinct agent roles** for optimal performance and task-specific expertise.

## 🤖 Agent Roles & Model Assignments

### 1. **Architect Agent** 🏗️
- **Model**: `mistral:7b-instruct-q4_0`
- **Role**: Master orchestrator and project planner
- **Capabilities**:
  - Project planning and roadmap creation
  - Agent coordination and task delegation
  - Requirements analysis and breakdown
  - High-level architectural decisions
- **Why this model**: Excellent reasoning capabilities for strategic planning

### 2. **Frontend Agent** 🎨
- **Model**: `starcoder2:3b`
- **Role**: UI/UX specialist and React developer
- **Capabilities**:
  - React component generation
  - Responsive design implementation
  - CSS styling and modern frameworks
  - UI/UX best practices
- **Why this model**: Optimized for frontend code generation and web technologies

### 3. **Backend Agent** ⚙️
- **Model**: `yi-coder:1.5b`
- **Role**: API and server-side logic specialist
- **Capabilities**:
  - FastAPI development
  - Database design and ORM
  - Authentication and security
  - Business logic implementation
- **Why this model**: Fast, efficient code generation for backend systems

### 4. **Shell Agent** 💻
- **Model**: `qwen2.5:3b`
- **Role**: System administration and command execution
- **Capabilities**:
  - Command execution and validation
  - File and directory operations
  - Environment setup and configuration
  - Deployment automation
- **Why this model**: Balanced performance for system tasks and quick responses

### 5. **Debug Agent** 🔍
- **Model**: `deepseek-coder:6.7b`
- **Role**: Error detection and code optimization specialist
- **Capabilities**:
  - Error detection and root cause analysis
  - Bug fixing and code optimization
  - Test case generation
  - Code quality assessment
- **Why this model**: Advanced code analysis capabilities for complex debugging

### 6. **Test Agent** 🧪
- **Model**: `qwen2.5:3b`
- **Role**: Testing specialist and quality assurance
- **Capabilities**:
  - Unit test generation with high coverage
  - Integration testing strategies
  - Test automation and CI/CD pipeline testing
  - Test-driven development (TDD) guidance
- **Why this model**: Fast and reliable for generating comprehensive test suites

## 📊 Model Performance Matrix

| Model | Size | Speed | Use Case | Agent(s) |
|-------|------|-------|----------|----------|
| `yi-coder:1.5b` | 866MB | Very Fast (100-500ms) | Quick code generation | Backend Agent |
| `starcoder2:3b` | ~1.7GB | Fast (200-800ms) | Frontend development | Frontend Agent |
| `qwen2.5:3b` | ~1.9GB | Fast (300-1000ms) | System commands, chat, testing | Shell Agent, Test Agent |
| `mistral:7b-instruct-q4_0` | ~4GB | Medium (1-3s) | Strategic planning, docs | Architect Agent |
| `deepseek-coder:6.7b` | ~3.8GB | Slow (2-5s) | Complex debugging | Debug Agent |

## 🎯 Intelligent Model Routing Strategy

### Task-Based Assignment
```
User Request → Architect Agent (Planning) → Specialized Agents (Execution)
                     ↓
    ┌─────────────────┼─────────────────┐
    ▼                 ▼                 ▼
Frontend Agent    Backend Agent    Shell Agent
    ↓                 ↓                 ↓
UI Components    API Endpoints    Commands
                      ▼
                 Debug Agent (If needed)
                      ↓
                 Issue Resolution
```

### Performance Optimization
- **Parallel Processing**: Independent tasks run simultaneously
- **Smart Caching**: Repeated queries cached by model
- **Load Balancing**: Distribute tasks across available models
- **Fallback Strategy**: Default to mistral if specialized model unavailable

## 🚀 Setup Instructions

### 1. Install Ollama Models
```bash
# Install all required models
ollama pull yi-coder:1.5b          # Backend Agent (866MB)
ollama pull starcoder2:3b          # Frontend Agent (~1.7GB)
ollama pull qwen2.5:3b             # Shell Agent (~1.9GB)
ollama pull mistral:7b-instruct-q4_0  # Architect Agent (~4GB)
ollama pull deepseek-coder:6.7b    # Debug Agent (~3.8GB)
```

### 2. Environment Configuration
```bash
# Copy and configure environment variables
cp .env.example .env

# Edit .env with your settings
# All agent models are pre-configured
```

### 3. Verify Installation
```bash
# Run the model overview script
python show_models.py

# Test agent configuration
python -c "from src.ai_coding_agent.agents import AGENT_CONFIGS; print('✅ Agents configured:', len(AGENT_CONFIGS))"
```

## 🔧 Configuration Details

### Environment Variables
```bash
# Specialized Agent Models
ARCHITECT_AGENT_MODEL=mistral:7b-instruct-q4_0
FRONTEND_AGENT_MODEL=starcoder2:3b
BACKEND_AGENT_MODEL=yi-coder:1.5b
SHELL_AGENT_MODEL=qwen2.5:3b
DEBUG_AGENT_MODEL=deepseek-coder:6.7b
```

### Agent Temperature Settings
- **Architect**: 0.8 (High creativity for planning)
- **Frontend**: 0.6 (Balanced for UI design)
- **Backend**: 0.5 (Precise for logic)
- **Shell**: 0.3 (Very precise for commands)
- **Debug**: 0.4 (Analytical for debugging)

## 🎪 Workflow Example

### Typical User Request Flow:
1. **User**: "Create a user authentication system"
2. **Architect Agent**: Analyzes requirements, creates plan
3. **Backend Agent**: Creates API endpoints and database models
4. **Frontend Agent**: Builds login/register components
5. **Shell Agent**: Sets up database migrations
6. **Debug Agent**: Reviews code for security issues

### Multi-Agent Collaboration:
- Agents communicate through structured message passing
- Each agent has access to project context and previous work
- Automatic quality checks and cross-validation
- User approval required for major decisions

## 📈 Benefits of This Architecture

✅ **Specialized Expertise**: Each agent optimized for specific tasks
✅ **Performance Optimized**: Right model for the right job
✅ **Scalable**: Easy to add new agents or models
✅ **Cost Effective**: Smaller models for simpler tasks
✅ **Quality Focused**: Specialized models produce better results

## 🔮 Future Enhancements

- **Testing Agent**: Dedicated model for test generation
- **Documentation Agent**: Specialized in writing docs
- **Security Agent**: Focused on security analysis
- **Performance Agent**: Code optimization specialist

---

**Total System Specs**:
- 🤖 **5 Specialized Agents**
- 🧠 **5 AI Models**
- 💾 **~12GB Total Size**
- ⚡ **Optimized Performance**
