"""
Simple Supabase Authentication Test.

This test script validates the Supabase Auth integration and configuration.
"""

import asyncio
import json
from datetime import datetime

from ai_coding_agent.config import settings
from ai_coding_agent.services.supabase_auth import SupabaseAuthService
from ai_coding_agent.models.user import UserCreate


async def test_supabase_configuration():
    """Test Supabase configuration and connection."""
    print("🔍 Testing Supabase Configuration...")

    try:
        # Check if Supabase credentials are configured
        supabase_url = settings.supabase.url
        supabase_key = settings.supabase.anon_key

        print(f"   SUPABASE_URL: {'✓ Set' if supabase_url else '✗ Missing'}")
        print(f"   SUPABASE_ANON_KEY: {'✓ Set' if supabase_key else '✗ Missing'}")

        if not supabase_url or not supabase_key:
            print("⚠️  Supabase credentials not configured in environment")
            print("   Please set SUPABASE_URL and SUPABASE_ANON_KEY in your .env file")
            return False

        print("✅ Supabase credentials configured")
        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def test_auth_service_initialization():
    """Test Supabase Auth service initialization."""
    print("\n🔍 Testing Auth Service Initialization...")

    try:
        service = SupabaseAuthService()
        print("✅ Supabase Auth service initialized successfully")
        return service

    except Exception as e:
        print(f"❌ Auth service initialization failed: {e}")
        return None


async def test_user_registration_flow():
    """Test user registration with Supabase Auth service."""
    print("\n🔍 Testing User Registration Flow...")

    try:
        service = SupabaseAuthService()

        # Create test user data
        test_email = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com"
        test_password = "TestPassword123!"
        test_username = f"testuser_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        print(f"   Test email: {test_email}")
        print(f"   Test username: {test_username}")

        # Test user registration
        result = await service.register_user(
            email=test_email,
            password=test_password,
            user_data={
                "username": test_username,
                "full_name": "Test User"
            }
        )

        if result:
            print("✅ User registration successful")
            print(f"   User ID: {result.get('user', {}).get('id', 'N/A')}")
            print(f"   Email confirmed: {result.get('user', {}).get('email_confirmed_at') is not None}")
            return result
        else:
            print("❌ User registration failed")
            return None

    except Exception as e:
        print(f"❌ Registration test failed: {e}")
        return None


async def test_user_login_flow():
    """Test user login with Supabase Auth service."""
    print("\n🔍 Testing User Login Flow...")

    try:
        service = SupabaseAuthService()

        # Use existing test credentials (assuming registration worked)
        test_email = "<EMAIL>"  # Replace with actual test email
        test_password = "TestPassword123!"

        result = await service.login_user(
            email=test_email,
            password=test_password
        )

        if result:
            print("✅ User login successful")
            print(f"   Access token: {'*' * 10}...")
            print(f"   Token type: {result.get('token_type', 'N/A')}")
            print(f"   Expires in: {result.get('expires_in', 'N/A')} seconds")
            return result
        else:
            print("❌ User login failed")
            return None

    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return None


async def test_token_refresh():
    """Test token refresh functionality."""
    print("\n🔍 Testing Token Refresh...")

    try:
        service = SupabaseAuthService()

        # This would need a valid refresh token from a previous login
        print("⚠️  Token refresh test requires valid refresh token")
        print("   (Skipping for now - implement after successful login test)")
        return True

    except Exception as e:
        print(f"❌ Token refresh test failed: {e}")
        return False


async def test_password_reset():
    """Test password reset functionality."""
    print("\n🔍 Testing Password Reset...")

    try:
        service = SupabaseAuthService()

        test_email = "<EMAIL>"  # Replace with actual test email

        result = await service.reset_password(test_email)

        if result:
            print("✅ Password reset email sent successfully")
            return True
        else:
            print("❌ Password reset failed")
            return False

    except Exception as e:
        print(f"❌ Password reset test failed: {e}")
        return False


async def run_supabase_auth_tests():
    """Run all Supabase authentication tests."""
    print("🚀 Starting Supabase Authentication Tests")
    print("=" * 50)

    results = {}

    # Test 1: Configuration
    results['configuration'] = await test_supabase_configuration()

    if not results['configuration']:
        print("\n❌ Cannot proceed without proper Supabase configuration")
        return results

    # Test 2: Service initialization
    service = await test_auth_service_initialization()
    results['service_init'] = service is not None

    if not results['service_init']:
        print("\n❌ Cannot proceed without service initialization")
        return results

    # Test 3: User registration
    registration_result = await test_user_registration_flow()
    results['registration'] = registration_result is not None

    # Test 4: User login (only if we have credentials)
    results['login'] = await test_user_login_flow() is not None

    # Test 5: Token refresh
    results['token_refresh'] = await test_token_refresh()

    # Test 6: Password reset
    results['password_reset'] = await test_password_reset()

    # Print summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print("=" * 50)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")

    passed = sum(1 for r in results.values() if r)
    total = len(results)

    print(f"\n📊 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Supabase Auth is ready.")
    elif passed > 0:
        print("⚠️  Some tests passed. Check Supabase project setup.")
    else:
        print("❌ Tests failed. Verify Supabase configuration.")

    print("\n📝 Next Steps:")
    if not results['configuration']:
        print("   1. Set up Supabase project at https://supabase.com")
        print("   2. Add SUPABASE_URL and SUPABASE_ANON_KEY to .env file")
    elif not results['registration']:
        print("   1. Check Supabase Auth settings")
        print("   2. Enable email authentication in Supabase dashboard")
    elif not results['login']:
        print("   1. Confirm user email in Supabase dashboard")
        print("   2. Check email confirmation settings")

    return results


async def main():
    """Main test runner."""
    await run_supabase_auth_tests()


if __name__ == "__main__":
    asyncio.run(main())
