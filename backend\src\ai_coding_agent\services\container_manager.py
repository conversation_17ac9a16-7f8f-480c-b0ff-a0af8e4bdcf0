"""
Container Management Service for User Project Isolation.

This service implements the container-per-user model as required by the
architectural rules. Each user gets their own isolated container environment
for running their projects with proper resource limits and security.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import json

try:
    import docker
    from docker.errors import APIError, ContainerError, ImageNotFound
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    docker = None
    APIError = Exception
    ContainerError = Exception
    ImageNotFound = Exception

from pydantic import BaseModel, Field
from ..config import settings

logger = logging.getLogger(__name__)


class ProjectType(str, Enum):
    """Supported project types for user containers."""
    REACT = "react"
    NEXTJS = "nextjs"
    PYTHON = "python"
    STATIC = "static"
    NODEJS = "nodejs"


class ContainerStatus(str, Enum):
    """Container status states."""
    CREATING = "creating"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    REMOVING = "removing"


class UserContainer(BaseModel):
    """User container information."""
    user_id: str
    container_id: str
    container_name: str
    project_type: ProjectType
    status: ContainerStatus
    port: Optional[int] = None
    preview_url: Optional[str] = None
    created_at: datetime
    last_accessed: datetime
    resource_limits: Dict[str, Any] = Field(default_factory=dict)
    environment: Dict[str, str] = Field(default_factory=dict)


class ContainerConfig(BaseModel):
    """Container configuration for different project types."""
    image: str
    ports: Dict[str, Any]
    environment: Dict[str, str]
    volumes: Dict[str, Dict[str, str]]
    working_dir: str = "/app"
    command: Optional[List[str]] = None
    mem_limit: str = "512m"
    cpu_quota: int = 50000  # 0.5 CPU


class UserContainerManager:
    """
    Manages isolated containers for user projects.
    
    Implements the container-per-user model with proper security,
    resource limits, and lifecycle management.
    """

    def __init__(self):
        """Initialize the container manager."""
        if not DOCKER_AVAILABLE:
            raise RuntimeError(
                "Docker SDK not available. Install with: pip install docker>=6.1.0"
            )
        
        try:
            self.client = docker.from_env()
            # Test Docker connection
            self.client.ping()
        except Exception as e:
            raise RuntimeError(f"Failed to connect to Docker daemon: {e}")
        
        # Track active user containers
        self.user_containers: Dict[str, UserContainer] = {}
        
        # Container configurations for different project types
        self.container_configs = self._load_container_configs()
        
        # Network name for user containers
        self.network_name = "ai-coding-user-network"
        self._ensure_network_exists()
        
        logger.info("UserContainerManager initialized successfully")

    def _load_container_configs(self) -> Dict[ProjectType, ContainerConfig]:
        """Load container configurations for different project types."""
        return {
            ProjectType.REACT: ContainerConfig(
                image="node:18-alpine",
                ports={"3000/tcp": None},
                environment={
                    "NODE_ENV": "development",
                    "CHOKIDAR_USEPOLLING": "true"
                },
                volumes={},
                command=["npm", "start"]
            ),
            ProjectType.NEXTJS: ContainerConfig(
                image="node:18-alpine",
                ports={"3000/tcp": None},
                environment={
                    "NODE_ENV": "development",
                    "CHOKIDAR_USEPOLLING": "true"
                },
                volumes={},
                command=["npm", "run", "dev"]
            ),
            ProjectType.PYTHON: ContainerConfig(
                image="python:3.11-slim",
                ports={"8000/tcp": None},
                environment={
                    "PYTHONUNBUFFERED": "1",
                    "PYTHONDONTWRITEBYTECODE": "1"
                },
                volumes={},
                command=["python", "-m", "http.server", "8000"]
            ),
            ProjectType.STATIC: ContainerConfig(
                image="nginx:alpine",
                ports={"80/tcp": None},
                environment={},
                volumes={},
                command=None
            ),
            ProjectType.NODEJS: ContainerConfig(
                image="node:18-alpine",
                ports={"3000/tcp": None},
                environment={
                    "NODE_ENV": "development"
                },
                volumes={},
                command=["node", "index.js"]
            )
        }

    def _ensure_network_exists(self):
        """Ensure the user container network exists."""
        try:
            self.client.networks.get(self.network_name)
        except docker.errors.NotFound:
            # Create isolated network for user containers
            self.client.networks.create(
                self.network_name,
                driver="bridge",
                options={
                    "com.docker.network.bridge.enable_icc": "false",  # Disable inter-container communication
                    "com.docker.network.bridge.enable_ip_masquerade": "true"
                }
            )
            logger.info(f"Created user container network: {self.network_name}")

    async def provision_user_environment(
        self,
        user_id: str,
        project_type: ProjectType,
        project_name: Optional[str] = None
    ) -> UserContainer:
        """
        Create an isolated container environment for a user.
        
        Args:
            user_id: Unique user identifier
            project_type: Type of project (react, python, etc.)
            project_name: Optional project name
            
        Returns:
            UserContainer: Container information
            
        Raises:
            ContainerError: If container creation fails
        """
        if user_id in self.user_containers:
            existing = self.user_containers[user_id]
            if existing.status == ContainerStatus.RUNNING:
                logger.info(f"User {user_id} already has a running container")
                return existing
            else:
                # Clean up old container
                await self.cleanup_user_container(user_id)

        config = self.container_configs[project_type]
        container_name = f"user-{user_id}-{project_type.value}"
        if project_name:
            container_name += f"-{project_name}"

        # Create user data volume
        volume_name = f"user-{user_id}-data"
        try:
            self.client.volumes.get(volume_name)
        except docker.errors.NotFound:
            self.client.volumes.create(volume_name)

        # Configure container
        container_config = {
            "image": config.image,
            "name": container_name,
            "ports": config.ports,
            "environment": {
                "USER_ID": user_id,
                "PROJECT_TYPE": project_type.value,
                **config.environment
            },
            "volumes": {
                volume_name: {"bind": "/app", "mode": "rw"},
                **config.volumes
            },
            "network": self.network_name,
            "mem_limit": config.mem_limit,
            "cpu_quota": config.cpu_quota,
            "detach": True,
            "working_dir": config.working_dir,
            "user": "1000:1000",  # Non-root user for security
            "security_opt": ["no-new-privileges:true"],
            "read_only": False,  # Allow writes to mounted volumes
            "tmpfs": {"/tmp": "noexec,nosuid,size=100m"}  # Secure tmp directory
        }

        if config.command:
            container_config["command"] = config.command

        try:
            # Create and start container
            container = self.client.containers.run(**container_config)
            
            # Get assigned port
            container.reload()
            port_mapping = container.ports.get(list(config.ports.keys())[0])
            port = int(port_mapping[0]["HostPort"]) if port_mapping else None
            
            # Create preview URL
            preview_url = f"http://preview-{user_id}.localhost:{port}" if port else None
            
            # Store container info
            user_container = UserContainer(
                user_id=user_id,
                container_id=container.id,
                container_name=container_name,
                project_type=project_type,
                status=ContainerStatus.RUNNING,
                port=port,
                preview_url=preview_url,
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow(),
                resource_limits={
                    "memory": config.mem_limit,
                    "cpu_quota": config.cpu_quota
                },
                environment=container_config["environment"]
            )
            
            self.user_containers[user_id] = user_container
            
            logger.info(f"Created container for user {user_id}: {container_name}")
            return user_container
            
        except Exception as e:
            logger.error(f"Failed to create container for user {user_id}: {e}")
            raise ContainerError(f"Container creation failed: {str(e)}")

    async def execute_in_container(
        self,
        user_id: str,
        command: Union[str, List[str]],
        working_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a command in the user's container.
        
        Args:
            user_id: User identifier
            command: Command to execute
            working_dir: Working directory for command
            
        Returns:
            Dict with execution results
        """
        if user_id not in self.user_containers:
            raise ValueError(f"No container found for user {user_id}")
        
        user_container = self.user_containers[user_id]
        
        try:
            container = self.client.containers.get(user_container.container_id)
            
            # Update last accessed time
            user_container.last_accessed = datetime.utcnow()
            
            # Execute command
            exec_result = container.exec_run(
                command,
                workdir=working_dir or "/app",
                user="1000:1000"  # Execute as non-root user
            )
            
            return {
                "exit_code": exec_result.exit_code,
                "output": exec_result.output.decode("utf-8"),
                "success": exec_result.exit_code == 0
            }
            
        except Exception as e:
            logger.error(f"Failed to execute command in container for user {user_id}: {e}")
            raise ContainerError(f"Command execution failed: {str(e)}")

    async def get_container_status(self, user_id: str) -> Optional[UserContainer]:
        """Get the current status of a user's container."""
        if user_id not in self.user_containers:
            return None
        
        user_container = self.user_containers[user_id]
        
        try:
            container = self.client.containers.get(user_container.container_id)
            
            # Update status based on container state
            if container.status == "running":
                user_container.status = ContainerStatus.RUNNING
            elif container.status in ["stopped", "exited"]:
                user_container.status = ContainerStatus.STOPPED
            else:
                user_container.status = ContainerStatus.ERROR
                
            return user_container
            
        except docker.errors.NotFound:
            # Container no longer exists
            del self.user_containers[user_id]
            return None
        except Exception as e:
            logger.error(f"Failed to get container status for user {user_id}: {e}")
            user_container.status = ContainerStatus.ERROR
            return user_container

    async def cleanup_user_container(self, user_id: str) -> bool:
        """
        Clean up a user's container and associated resources.
        
        Args:
            user_id: User identifier
            
        Returns:
            bool: True if cleanup successful
        """
        if user_id not in self.user_containers:
            return True
        
        user_container = self.user_containers[user_id]
        
        try:
            # Stop and remove container
            container = self.client.containers.get(user_container.container_id)
            container.stop(timeout=10)
            container.remove()
            
            # Note: We keep the user data volume for persistence
            # Only remove it if explicitly requested
            
            # Remove from tracking
            del self.user_containers[user_id]
            
            logger.info(f"Cleaned up container for user {user_id}")
            return True
            
        except docker.errors.NotFound:
            # Container already gone
            del self.user_containers[user_id]
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup container for user {user_id}: {e}")
            return False

    async def cleanup_inactive_containers(self, max_idle_hours: int = 24) -> int:
        """
        Clean up containers that have been inactive for too long.
        
        Args:
            max_idle_hours: Maximum idle time before cleanup
            
        Returns:
            int: Number of containers cleaned up
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=max_idle_hours)
        cleanup_count = 0
        
        for user_id, container in list(self.user_containers.items()):
            if container.last_accessed < cutoff_time:
                if await self.cleanup_user_container(user_id):
                    cleanup_count += 1
        
        logger.info(f"Cleaned up {cleanup_count} inactive containers")
        return cleanup_count

    def get_all_user_containers(self) -> List[UserContainer]:
        """Get information about all active user containers."""
        return list(self.user_containers.values())

    async def close(self):
        """Close the Docker client connection."""
        if hasattr(self, 'client'):
            self.client.close()


# Global instance
_container_manager = None


def get_container_manager() -> UserContainerManager:
    """Get or create the global container manager instance."""
    global _container_manager
    if _container_manager is None:
        _container_manager = UserContainerManager()
    return _container_manager
