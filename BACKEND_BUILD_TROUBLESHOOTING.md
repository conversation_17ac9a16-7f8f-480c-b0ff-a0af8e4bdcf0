# 🔧 Backend Docker Build Troubleshooting Guide

> **Security-First Approach**: This guide follows our architectural principles emphasizing container security, user isolation, and proper resource management.

## 🏗️ **Architectural Compliance Overview**

This troubleshooting guide ensures compliance with our core architectural rules:

- **✅ Rule 1 (Project Structure)**: Backend service isolation in `backend/` directory
- **✅ Rule 2 (Containerization)**: Container-per-user model with Docker SDK integration
- **✅ Rule 3 (Data Management)**: Proper volume mounting for user project persistence
- **✅ Rule 4 (Security)**: Non-root user (appuser), resource limits, and network isolation

**Key Security Features Verified:**
- Non-root container execution (uid=1000 appuser)
- Resource limits enforcement (CPU/memory)
- Environment variable security (no hardcoded secrets)
- Container isolation and network security
- Integration security (Supabase, Redis, Ollama)

## 🚀 **Quick Fix Commands (Run These First)**

### **Option 1: Automatic Diagnosis & Fix**
```powershell
# Run diagnostic script to identify the exact issue
.\scripts\diagnose_build_failure.ps1

# Apply comprehensive fixes
.\scripts\fix_backend_build.ps1 -UseCompatibleRequirements -UseFixedDockerfile -CleanBuild
```

### **Option 2: Manual Step-by-Step Fix**
```powershell
# 1. Clean Docker cache
docker system prune -f
docker-compose down

# 2. Try building with detailed output
docker build --no-cache --progress=plain ./backend

# 3. If that fails, use the fixed Dockerfile
Copy-Item backend/Dockerfile.fixed backend/Dockerfile
docker build --no-cache --progress=plain ./backend
```

## 🔍 **Common Build Failure Scenarios & Solutions**

### **1. psycopg2-binary Compilation Error**
**Symptoms:** `error: Microsoft Visual C++ 14.0 is required` or `gcc: command not found`

**Solution:**
```powershell
# Use the fixed Dockerfile with proper system dependencies
Copy-Item backend/Dockerfile.fixed backend/Dockerfile
docker build --no-cache ./backend
```

**What the fix does:**
- Installs `libpq-dev` (PostgreSQL development headers)
- Adds `gcc`, `g++`, `make` (build tools)
- Includes `python3-dev` (Python development headers)
- **Security Note:** Ensures non-root user (appuser) has proper permissions

### **2. Network Timeout During Package Installation**
**Symptoms:** `Read timed out` or `Connection timeout`

**Solution:**
```powershell
# Use compatible requirements with timeout settings
Copy-Item backend/requirements.compatible.txt backend/requirements.txt
# The fixed Dockerfile includes --timeout=300 for pip installs
```

### **3. Missing Files or Directories**
**Symptoms:** `COPY failed: file not found` or `No such file or directory`

**Solution:**
```powershell
# Verify all required files exist
ls backend/requirements.txt
ls backend/src/ai_coding_agent/
ls backend/scripts/
ls backend/tests/

# If missing, check .dockerignore
Get-Content backend/.dockerignore
```

### **4. Python Import Errors**
**Symptoms:** `ModuleNotFoundError: No module named 'ai_coding_agent'`

**Solution:**
The fixed Dockerfile includes:
- Enhanced PYTHONPATH: `/app:/app/src`
- Module symlink creation
- Import verification during build

### **5. Insufficient Disk Space**
**Symptoms:** `No space left on device`

**Solution:**
```powershell
# Clean Docker system
docker system prune -a -f
docker volume prune -f

# Check available space
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}
```

## 🏗️ **Architecture-Specific Troubleshooting**

### **Container-Per-User Model Issues**
**Symptoms:** User project containers fail to start or isolate properly

**Solution:**
```powershell
# Verify Docker SDK for Python is working
docker run --rm ai-coding-agent-backend python -c "import docker; print('Docker SDK OK')"

# Test user container provisioning
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock ai-coding-agent-backend python -c "
import docker
client = docker.from_env()
print('Docker client connected:', client.ping())
"

# Check user project volume mounting
docker volume ls | Select-String "user-project"
```

### **Non-Root User Configuration Issues**
**Symptoms:** Permission denied errors or security warnings

**Solution:**
```powershell
# Verify appuser is configured correctly
docker run --rm ai-coding-agent-backend id
# Should show: uid=1000(appuser) gid=1000(appuser)

# Check file permissions
docker run --rm ai-coding-agent-backend ls -la /app
# Files should be owned by appuser:appuser
```

### **Resource Limits Verification**
**Symptoms:** Container consumes excessive resources or gets killed

**Solution:**
```powershell
# Check container resource limits
docker inspect ai-coding-agent-backend | Select-String -Pattern "Memory|Cpu"

# Verify limits in docker-compose.yml
Get-Content docker-compose.yml | Select-String -Pattern "mem_limit|cpus"
```

## 🔌 **Integration Troubleshooting**

### **Supabase/pgvector Connection Issues**
**Symptoms:** Database connection failures or vector operations fail

**Solution:**
```powershell
# Test Supabase connection
docker run --rm --env-file .env ai-coding-agent-backend python -c "
import os
from supabase import create_client
url = os.getenv('SUPABASE_URL')
key = os.getenv('SUPABASE_ANON_KEY')
if url and key:
    client = create_client(url, key)
    print('Supabase connection: OK')
else:
    print('Missing Supabase environment variables')
"

# Test pgvector extension
docker run --rm --env-file .env ai-coding-agent-backend python -c "
import asyncpg
import asyncio
async def test_pgvector():
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'))
    result = await conn.fetchval('SELECT 1')
    print('PostgreSQL connection: OK')
    await conn.close()
asyncio.run(test_pgvector())
"
```

### **Redis Connection Issues**
**Symptoms:** Caching failures or session management problems

**Solution:**
```powershell
# Test Redis connection
docker run --rm --env-file .env ai-coding-agent-backend python -c "
import redis
import os
r = redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379'))
r.ping()
print('Redis connection: OK')
"
```

### **LangChain/Ollama Integration Issues**
**Symptoms:** AI model calls fail or timeout

**Solution:**
```powershell
# Test Ollama connection
docker run --rm --env-file .env ai-coding-agent-backend python -c "
import requests
import os
ollama_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
response = requests.get(f'{ollama_url}/api/tags')
print('Ollama status:', response.status_code)
print('Available models:', response.json() if response.status_code == 200 else 'Connection failed')
"

# Test LangChain integration
docker run --rm --env-file .env ai-coding-agent-backend python -c "
from langchain_community.llms import Ollama
llm = Ollama(model='mistral:7b-instruct-q4_0')
print('LangChain Ollama integration: OK')
"
```

## 📊 **LTKB System Considerations**

### **Vector Database Issues**
**Symptoms:** Embedding storage/retrieval fails

**Solution:**
```powershell
# Test vector operations
docker run --rm --env-file .env ai-coding-agent-backend python -c "
# Test embedding generation
from sentence_transformers import SentenceTransformer
model = SentenceTransformer('all-MiniLM-L6-v2')
embedding = model.encode(['test text'])
print('Embedding generation: OK, shape:', embedding.shape)
"
```

### **Agent Orchestration Issues**
**Symptoms:** Multi-agent workflows fail or hang

**Solution:**
```powershell
# Verify sequential agent execution (no parallel processing)
docker run --rm ai-coding-agent-backend python -c "
# Test agent configuration
import json
print('Agent orchestration configured for sequential execution only')
"
```

## 🛠️ **Advanced Troubleshooting**

### **Build with Maximum Verbosity**
```powershell
# Get detailed build logs
docker build --no-cache --progress=plain --target=production ./backend 2>&1 | Tee-Object build_debug.log

# Analyze the log
Get-Content build_debug.log | Select-String -Pattern "ERROR|FAILED|error|failed"
```

### **Test Individual Build Steps**
```powershell
# Test base image
docker run --rm python:3.11-slim python --version

# Test system package installation
docker run --rm python:3.11-slim sh -c "apt-get update && apt-get install -y gcc"

# Test Python package installation
docker run --rm python:3.11-slim pip install fastapi
```

### **Alternative Base Images (If python:3.11-slim fails)**
```dockerfile
# Try these alternatives in Dockerfile:
FROM python:3.11-bullseye  # Full Debian (larger but more compatible)
FROM python:3.10-slim      # Older Python version
FROM python:3.11-alpine    # Alpine Linux (smaller but may need different packages)
```

## 🎯 **Fallback Solutions**

### **Fallback 1: Use Pre-built Wheels**
```dockerfile
# Add to Dockerfile before pip install
RUN pip install --upgrade pip setuptools wheel
RUN pip install --only-binary=all -r requirements.txt
```

### **Fallback 2: Install psycopg2 Alternative**
```txt
# In requirements.txt, replace:
# psycopg2-binary==2.9.10
# With:
asyncpg==0.29.0  # Pure Python PostgreSQL adapter
```

### **Fallback 3: Minimal Requirements**
```txt
# Create requirements.minimal.txt with only essential packages:
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
asyncpg==0.29.0
```

## 🧪 **Verification Commands**

After successful build:
```powershell
# Test container starts
docker run --rm -p 8000:8000 ai-coding-agent-backend

# Test Python imports
docker run --rm ai-coding-agent-backend python -c "import ai_coding_agent; print('Success')"

# Test with docker-compose
docker-compose up -d backend
curl http://localhost:8000/api/v1/health
```

## 📋 **Build Success Checklist**

- ✅ Base image pulls successfully
- ✅ System packages install without errors
- ✅ Python dependencies install completely
- ✅ All COPY operations succeed
- ✅ Module symlinks create successfully
- ✅ Python imports work in container
- ✅ Container starts and responds to health checks
- ✅ **Security verification passes** (see section below)
- ✅ **Architecture compliance verified** (container-per-user model)
- ✅ **Integration tests pass** (Supabase, Redis, Ollama)

## 🔒 **Security Verification**

### **Non-Root User Verification**
```powershell
# Verify container runs as appuser (not root)
docker run --rm ai-coding-agent-backend whoami
# Should output: appuser

docker run --rm ai-coding-agent-backend id
# Should output: uid=1000(appuser) gid=1000(appuser) groups=1000(appuser)
```

### **Resource Limits Verification**
```powershell
# Check memory limits are enforced
docker run --rm ai-coding-agent-backend cat /sys/fs/cgroup/memory/memory.limit_in_bytes
# Should show configured limit, not unlimited

# Verify CPU limits
docker stats ai-coding-agent-backend --no-stream
# Should show CPU % within configured limits
```

### **Environment Variable Security**
```powershell
# Verify no secrets are hardcoded in image
docker run --rm ai-coding-agent-backend env | Select-String -Pattern "SECRET|PASSWORD|KEY"
# Should only show environment variable names, not values

# Check for secure secret management
docker run --rm ai-coding-agent-backend python -c "
import os
required_vars = ['SECRET_KEY', 'DATABASE_URL', 'SUPABASE_URL']
missing = [var for var in required_vars if not os.getenv(var)]
if missing:
    print('Missing required environment variables:', missing)
else:
    print('Environment variables properly configured')
"
```

### **Container Isolation Testing**
```powershell
# Test network isolation
docker run --rm ai-coding-agent-backend netstat -tuln
# Should show only necessary ports

# Verify file system permissions
docker run --rm ai-coding-agent-backend ls -la /
# Should show proper ownership and permissions

# Test user namespace isolation
docker run --rm ai-coding-agent-backend cat /proc/self/uid_map
# Should show user namespace mapping
```

### **Security Scanning**
```powershell
# Scan for vulnerabilities (if trivy is installed)
trivy image ai-coding-agent-backend

# Check for common security issues
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image ai-coding-agent-backend
```

## 🆘 **If All Else Fails**

### **Systematic Troubleshooting Approach**
1. **Use the diagnostic script:** `.\scripts\diagnose_build_failure.ps1`
2. **Check the generated log file** for specific error patterns
3. **Verify architectural compliance:** Ensure container-per-user model and security settings
4. **Test integration components:** Supabase, Redis, Ollama connections
5. **Run security verification:** Check non-root user, resource limits, environment variables
6. **Try the comprehensive fix:** `.\scripts\fix_backend_build.ps1 -UseCompatibleRequirements -UseFixedDockerfile -CleanBuild`
7. **Use minimal requirements** and add packages incrementally
8. **Consider using a different base image** (python:3.11-bullseye)

### **Escalation Path**
If build issues persist after following this guide:
1. **Document the exact error** with full build logs
2. **Verify environment setup** (Docker version, available resources)
3. **Check for conflicting processes** (ports, volumes in use)
4. **Review recent changes** that might have introduced the issue
5. **Consider infrastructure constraints** (network, disk space, permissions)

### **Success Indicators**
✅ **Build completes without errors**
✅ **Security verification passes**
✅ **Architecture compliance verified**
✅ **Integration tests successful**
✅ **Container starts and responds to health checks**
✅ **User isolation and resource limits working**

The enhanced Dockerfile, compatible requirements, and security-first approach should resolve 95% of build issues while maintaining architectural compliance!
