# User Projects Directory

This directory contains user-generated projects and data that persists across container restarts.

## Structure

```
user-projects/
├── user-{id}/                    # Individual user directories
│   ├── projects/                 # User's AI-generated projects
│   ├── templates/                # User's custom templates
│   ├── preferences/              # User preferences and settings
│   └── history/                  # Project generation history
├── shared/                       # Shared resources
│   ├── templates/                # Global project templates
│   └── examples/                 # Example projects
└── backups/                      # Automated backups
```

## Volume Mounting

This directory is mounted as a Docker volume to ensure data persistence:

- **Production**: Named volume `user-projects`
- **Development**: Bind mount to `./user-projects`

## Security

- Each user has their own isolated directory
- Proper file permissions are enforced
- No cross-user access is allowed
- Regular backups are performed

## Backup Strategy

1. **Automated Daily Backups**: Full backup of user data
2. **Incremental Backups**: Every 6 hours for active projects
3. **Retention Policy**: 30 days for daily, 7 days for incremental
4. **Recovery**: Point-in-time recovery available

## Usage

The AI Coding Agent automatically manages this directory structure. Users don't need to interact with it directly.

## Monitoring

- Disk usage is monitored
- Alerts are sent when storage is low
- Cleanup policies remove old unused projects
