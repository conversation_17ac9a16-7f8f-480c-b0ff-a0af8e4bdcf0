# Database Architecture Quick Start Guide

## 🚀 Quick Setup (New Developers)

```bash
# 1. Clone and setup
git clone <repository>
cd ai-coding-agent
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows

# 2. Install dependencies (now includes Redis and pgvector)
pip install -r requirements.txt

# 3. Setup Supabase and Redis
# - Configure Supabase environment variables
# - Run setup_pgvector.sql in Supabase SQL editor
# - Start Redis: docker-compose up redis

# 4. Verify everything works
python backend/scripts/test_migration.py
```

## 🗄️ Simplified Database Architecture

```
┌─────────────────────────────────┐    ┌─────────────────────────────────┐
│         Supabase                │    │           Redis                 │
│    (PostgreSQL + pgvector)      │    │         (Cache)                 │
├─────────────────────────────────┤    ├─────────────────────────────────┤
│ • User authentication           │    │ • Session management            │
│ • Project data & roadmaps       │    │ • Embedding cache               │
│ • Best practices & knowledge    │    │ • Real-time coordination        │
│ • Vector embeddings (pgvector)  │    │ • Temporary data                │
│ • All application data          │    │ • Agent communication           │
└─────────────────────────────────┘    └─────────────────────────────────┘
```

## 🔧 Environment Setup

```bash
# Configure environment variables
cp .env.example .env
# Edit .env with your Supabase and Redis settings

# Setup pgvector in Supabase
# Run backend/scripts/setup_pgvector.sql in Supabase SQL editor

# Start Redis cache
docker-compose up redis

# Test the setup
python backend/scripts/test_migration.py
```

## 📊 Database Setup

```bash
# Setup pgvector in Supabase (one-time setup)
# 1. Open Supabase SQL editor
# 2. Run: backend/scripts/setup_pgvector.sql

# Test database connections
python backend/scripts/test_migration.py

# Check Redis connection
redis-cli ping  # Should return PONG
```

## 🧪 Testing

```bash
# Test the new architecture
python backend/scripts/test_migration.py

# Test specific components
python -m pytest backend/tests/test_vector_db.py
python -m pytest backend/tests/test_redis_cache.py

# Test full integration
python -m pytest backend/tests/
```

## 🔍 Troubleshooting

### Common Issues

1. **Configuration Errors**: Check Supabase and Redis environment variables
2. **Database Connection Issues**: Verify Supabase credentials and Redis URL
3. **Missing Dependencies**: Run `pip install redis hiredis asyncpg`
4. **pgvector Setup**: Run setup_pgvector.sql in Supabase SQL editor

### Debug Commands

```bash
# Test the new architecture
python backend/scripts/test_migration.py

# Check Redis connection
redis-cli ping

# Check configuration
python -c "from backend.src.ai_coding_agent.config import settings; print(settings.database.mode)"
```

## 📈 Best Practices

1. **Use environment variables** for all configuration
2. **Run tests after configuration changes**
3. **Setup pgvector once** in Supabase SQL editor
4. **Use Redis for caching** to improve performance
5. **Monitor Redis memory usage** in production

## 🏗️ Architecture Benefits

- **Simplified Setup**: Only 2 databases instead of 3
- **Better Performance**: Redis caching + pgvector optimization
- **Easier Scaling**: Cloud-native Supabase + Redis
- **Container-Friendly**: Perfect for container-per-user model
- **Reduced Complexity**: No sync issues between databases
