# AI Coding Agent - Complete Setup Guide

## Quick Start for GPU-Accelerated Performance

This guide will transform your 378-second model response times into 3-8 second responses by properly configuring CUDA acceleration.

### 🚀 One-Command Setup

```bash
# Clone and setup the entire environment
git clone <your-repo>
cd ai-coding-agent
python setup_cuda.py
```

If everything passes, you're ready to go! If not, follow the detailed steps below.

### 📋 Detailed Setup Steps

#### Step 1: Create the Conda Environment

```bash
# Create environment from file
conda env create -f environment.yml

# Activate environment
conda activate ai-coding-agent

# Verify CUDA installation
python -c "import torch; print(f'CUDA Available: {torch.cuda.is_available()}')"
```

#### Step 2: Configure Ollama for GPU

```bash
# Set environment variables (add to your .bashrc or .env)
export OLLAMA_GPU=1
export OLLAMA_GPU_MEMORY="4GB"
export OLLAMA_MAX_LOADED_MODELS=3

# Start Ollama server
ollama serve
```

#### Step 3: Install and Warm Up Models

```bash
# Install required models
ollama pull llama3.2:3b
ollama pull starcoder2:3b
ollama pull deepseek-coder:6.7b-instruct
ollama pull qwen2.5:3b
ollama pull yi-coder:1.5b
ollama pull mistral:7b-instruct-q4_0

# Warm up models for GPU
python verify_models.py
```

#### Step 4: Run Tests

```bash
# Run Phase A2 tests
python run_phase_a2_tests.py

# Run performance benchmark
python benchmark_models.py
```

### 🎯 Expected Performance After Setup

| Model | Before GPU | After GPU | Improvement |
|-------|------------|-----------|-------------|
| yi-coder:1.5b | 378s | 1-3s | 126x faster |
| starcoder2:3b | 300s+ | 2-5s | 60x faster |
| llama3.2:3b | 250s+ | 3-8s | 31x faster |
| qwen2.5:3b | 200s+ | 4-10s | 20x faster |
| deepseek-coder:6.7b | 400s+ | 8-20s | 20x faster |

### 🔧 Troubleshooting

#### If models are still slow:

1. **Check GPU usage during inference:**
   ```bash
   # In one terminal
   watch -n 1 nvidia-smi

   # In another terminal
   ollama run llama3.2:3b "test prompt"
   ```

2. **Verify Ollama GPU detection:**
   ```bash
   ollama info
   # Should show GPU: true
   ```

3. **Check CUDA installation:**
   ```bash
   python setup_cuda.py
   ```

#### If health checks fail:

- Models need 30-60s to load initially (cold start)
- After warmup, responses should be <10s
- Use `verify_models.py` to warm up all models

#### If tests fail:

- Ensure all models are installed: `ollama list`
- Check Ollama server is running: `ollama serve`
- Verify network connectivity: `curl http://localhost:11434/api/tags`

### 📊 Monitoring Performance

Use the provided scripts to monitor performance:

```bash
# Quick system check
python setup_cuda.py

# Model verification and warmup
python verify_models.py

# Performance benchmarking
python benchmark_models.py

# Full orchestrator tests
python run_phase_a2_tests.py
```

### 🎉 Success Indicators

You'll know everything is working when:

1. ✅ `setup_cuda.py` shows 6/6 checks passed
2. ✅ `verify_models.py` shows all models warmed up in <60s
3. ✅ `benchmark_models.py` shows average response times <10s
4. ✅ `run_phase_a2_tests.py` shows all models healthy

### 🔄 Next Steps

Once your GPU setup is working:

1. **Run the orchestrator tests:**
   ```bash
   cd tests
   python -m pytest test_orchestrator.py -v
   ```

2. **Test the API endpoints:**
   ```bash
   python -m uvicorn src.ai_coding_agent.main:app --reload
   curl http://localhost:8000/api/v1/orchestrator/health
   ```

3. **Monitor ongoing performance:**
   ```bash
   # Run daily to ensure performance stays optimal
   python benchmark_models.py
   ```

### 📈 Performance Optimization Tips

1. **Keep models warm:** Run verification script daily
2. **Monitor GPU temperature:** Use `nvidia-smi` to watch thermals
3. **Adjust concurrent requests:** Tune based on your GPU memory
4. **Use model priorities:** Preload your most-used models

### 🚨 Red Flags to Watch For

- Response times suddenly increase to >30s (GPU not being used)
- Health checks consistently failing (Ollama or model issues)
- GPU utilization at 0% during inference (CUDA not working)
- Memory errors (too many models loaded simultaneously)

This setup will give you a production-ready, high-performance multi-agent system that fully utilizes your Quadro P1000 GPU!
