/**
 * Retry mechanisms for failed operations
 */

interface RetryOptions {
  maxAttempts?: number;
  delay?: number;
  backoffFactor?: number;
  maxDelay?: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

const defaultRetryOptions: Required<RetryOptions> = {
  maxAttempts: 3,
  delay: 1000,
  backoffFactor: 2,
  maxDelay: 30000,
  retryCondition: (error) => {
    // Retry on network errors, 5xx errors, but not on 4xx client errors
    if (error?.response?.status) {
      return error.response.status >= 500;
    }
    // Retry on network errors
    return error?.name === 'NetworkError' || error?.code === 'NETWORK_ERROR';
  },
  onRetry: () => {}
};

/**
 * Generic retry function with exponential backoff
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const config = { ...defaultRetryOptions, ...options };
  let lastError: any;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // Check if we should retry
      if (attempt === config.maxAttempts || !config.retryCondition(error)) {
        throw error;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        config.delay * Math.pow(config.backoffFactor, attempt - 1),
        config.maxDelay
      );

      // Call retry callback
      config.onRetry(attempt, error);

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

/**
 * Retry wrapper for API calls
 */
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  return withRetry(apiCall, {
    maxAttempts: 3,
    delay: 1000,
    retryCondition: (error) => {
      // Retry on specific HTTP status codes
      if (error?.response?.status) {
        const status = error.response.status;
        return status >= 500 || status === 408 || status === 429; // Server errors, timeout, rate limit
      }
      return true; // Retry on network errors
    },
    onRetry: (attempt, error) => {
      console.warn(`API call failed, retrying attempt ${attempt}:`, error.message);
    },
    ...options
  });
};

/**
 * Retry wrapper with circuit breaker pattern
 */
class CircuitBreaker {
  private failures = 0;
  private lastFailTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold: number = 5,
    private timeout: number = 60000, // 1 minute
    private monitoringPeriod: number = 10000 // 10 seconds
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
}

export const apiCircuitBreaker = new CircuitBreaker();

/**
 * Retry with circuit breaker for critical API calls
 */
export const retryWithCircuitBreaker = async <T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  return apiCircuitBreaker.execute(() =>
    withRetry(operation, options)
  );
};

/**
 * Queue for managing failed operations to retry later
 */
class RetryQueue {
  private queue: Array<{
    id: string;
    operation: () => Promise<any>;
    options: RetryOptions;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];

  private processing = false;

  add<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const id = Math.random().toString(36).substr(2, 9);
      this.queue.push({ id, operation, options, resolve, reject });

      if (!this.processing) {
        this.process();
      }
    });
  }

  private async process(): Promise<void> {
    this.processing = true;

    while (this.queue.length > 0) {
      const item = this.queue.shift()!;

      try {
        const result = await withRetry(item.operation, item.options);
        item.resolve(result);
      } catch (error) {
        item.reject(error);
      }
    }

    this.processing = false;
  }

  clear(): void {
    this.queue.forEach(item => item.reject(new Error('Queue cleared')));
    this.queue = [];
  }

  size(): number {
    return this.queue.length;
  }
}

export const retryQueue = new RetryQueue();

/**
 * Utility for handling offline scenarios
 */
export const onlineRetry = <T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  return new Promise((resolve, reject) => {
    const executeWhenOnline = async () => {
      if (!navigator.onLine) {
        // Wait for online event
        const handleOnline = async () => {
          window.removeEventListener('online', handleOnline);
          try {
            const result = await withRetry(operation, options);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        };

        window.addEventListener('online', handleOnline);
        return;
      }

      // Already online, execute immediately
      try {
        const result = await withRetry(operation, options);
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };

    executeWhenOnline();
  });
};
