#!/usr/bin/env python3
"""
Docker Multi-Service Setup Verification Script

This script verifies that the restructured AI Coding Agent project
is properly configured for multi-service Docker deployment.
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional

def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_step(step: str):
    """Print a formatted step."""
    print(f"\n📋 {step}")
    print("-" * 40)

def check_file_exists(file_path: str) -> bool:
    """Check if a file exists."""
    exists = Path(file_path).exists()
    status = "✅" if exists else "❌"
    print(f"  {status} {file_path}")
    return exists

def check_directory_structure() -> bool:
    """Verify the project directory structure."""
    print_step("Checking Directory Structure")

    required_files = [
        "docker-compose.yml",
        "docker-compose.dev.yml",
        ".env.example",
        "backend/Dockerfile",
        "backend/.dockerignore",
        "backend/src/ai_coding_agent/__init__.py",
        "frontend/Dockerfile",
        "frontend/.dockerignore",
        "vector-db/Dockerfile",
        "vector-db/.dockerignore",
        "user-projects/README.md",
        "pyproject.toml",
        # Infrastructure configuration files
        "infrastructure/nginx/default.conf",
        "infrastructure/nginx/ssl.conf",
        "infrastructure/ssl/generate-certs.sh",
        "infrastructure/ssl/cert-config.yaml",
        "infrastructure/monitoring/prometheus.yml",
        "infrastructure/monitoring/grafana-dashboard.json",
        "infrastructure/docker/production.yml",
        "vector-db/init/setup-collections.py"
    ]

    all_exist = True
    for file_path in required_files:
        if not check_file_exists(file_path):
            all_exist = False

    return all_exist

def check_import_paths() -> bool:
    """Check that import paths have been updated correctly."""
    print_step("Checking Import Paths")

    # Check pyproject.toml
    try:
        with open("pyproject.toml", "r") as f:
            content = f.read()
            if 'packages = [{include = "ai_coding_agent", from = "backend/src"}]' in content:
                print("  ✅ pyproject.toml package path updated")
            else:
                print("  ❌ pyproject.toml package path not updated")
                return False
    except Exception as e:
        print(f"  ❌ Error reading pyproject.toml: {e}")
        return False

    # Check a sample test file
    test_file = "backend/tests/integration/ai/test_models_import.py"
    if Path(test_file).exists():
        try:
            with open(test_file, "r", encoding="utf-8") as f:
                content = f.read()
                if "from ai_coding_agent.models" in content and "from src.ai_coding_agent" not in content:
                    print("  ✅ Test file import paths updated")
                else:
                    print("  ❌ Test file import paths not updated")
                    return False
        except Exception as e:
            print(f"  ❌ Error reading test file: {e}")
            return False

    return True

def check_docker_compose_config() -> bool:
    """Check docker-compose configuration."""
    print_step("Checking Docker Compose Configuration")

    try:
        # Check if docker-compose is available
        result = subprocess.run(["docker-compose", "--version"],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  ✅ Docker Compose available: {result.stdout.strip()}")
        else:
            print("  ❌ Docker Compose not available")
            return False

        # Validate docker-compose.yml syntax
        result = subprocess.run(["docker-compose", "config"],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("  ✅ docker-compose.yml syntax valid")
        else:
            print(f"  ❌ docker-compose.yml syntax error: {result.stderr}")
            return False

        return True

    except Exception as e:
        print(f"  ❌ Error checking Docker Compose: {e}")
        return False

def check_environment_config() -> bool:
    """Check environment configuration."""
    print_step("Checking Environment Configuration")

    # Check if .env.example exists and has required variables
    if not Path(".env.example").exists():
        print("  ❌ .env.example file missing")
        return False

    required_vars = [
        "SECRET_KEY",
        "CONFIG_ENCRYPTION_KEY",
        "DB_HOST",
        "OLLAMA_HOST",
        "VECTOR_DB_HOST",
        "CHROMA_HOST"
    ]

    try:
        with open(".env.example", "r") as f:
            content = f.read()

        missing_vars = []
        for var in required_vars:
            if var not in content:
                missing_vars.append(var)

        if missing_vars:
            print(f"  ❌ Missing environment variables: {missing_vars}")
            return False
        else:
            print("  ✅ All required environment variables present")

        # Check for Docker service names (updated for new architecture)
        if "postgres" in content and "redis" in content and "ollama" in content:
            print("  ✅ Docker service names configured")
        else:
            print("  ❌ Docker service names not properly configured")
            return False

        return True

    except Exception as e:
        print(f"  ❌ Error reading .env.example: {e}")
        return False

def check_dockerfile_paths() -> bool:
    """Check Dockerfile configurations."""
    print_step("Checking Dockerfile Configurations")

    # Check backend Dockerfile
    try:
        with open("backend/Dockerfile", "r") as f:
            content = f.read()

        if "COPY src/ai_coding_agent/ ./ai_coding_agent/" in content:
            print("  ✅ Backend Dockerfile has correct COPY paths")
        else:
            print("  ❌ Backend Dockerfile COPY paths incorrect")
            return False

        if "multi-stage" in content.lower() or "FROM python:3.11-slim as" in content:
            print("  ✅ Backend Dockerfile uses multi-stage build")
        else:
            print("  ⚠️  Backend Dockerfile could benefit from multi-stage build")

        return True

    except Exception as e:
        print(f"  ❌ Error reading backend Dockerfile: {e}")
        return False

def generate_env_file() -> bool:
    """Generate .env file if it doesn't exist."""
    print_step("Generating Environment File")

    if Path(".env").exists():
        print("  ✅ .env file already exists")
        return True

    try:
        import secrets

        # Generate secure keys
        secret_key = secrets.token_hex(32)
        config_key = secrets.token_hex(32)
        db_password = secrets.token_urlsafe(16)

        env_content = f"""# AI Coding Agent - Generated Environment Configuration
# Generated by verify_docker_setup.py

# Security Configuration
SECRET_KEY={secret_key}
CONFIG_ENCRYPTION_KEY={config_key}

# Application Settings
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=ai_coding_agent
DB_USER=agent
DB_PASSWORD={db_password}

# AI Service Configuration
OLLAMA_HOST=http://ollama:11434
DEFAULT_AI_MODEL=mistral:7b-instruct-q4_0

# Vector Database Configuration
VECTOR_DB_HOST=vector-db
VECTOR_DB_PORT=8000
CHROMA_HOST=0.0.0.0
CHROMA_PORT=8000

# Development Configuration
REACT_APP_API_URL=http://localhost:8000
RELOAD=true

# Feature Flags
ENABLE_LTKB=true
ENABLE_VECTOR_SEARCH=true
ENABLE_AGENT_COLLABORATION=true
"""

        with open(".env", "w") as f:
            f.write(env_content)

        print("  ✅ Generated .env file with secure random keys")
        return True

    except Exception as e:
        print(f"  ❌ Error generating .env file: {e}")
        return False

def check_infrastructure_config() -> bool:
    """Check infrastructure configuration files."""
    print_step("Checking Infrastructure Configuration")

    # Check nginx configurations
    nginx_configs = [
        "infrastructure/nginx/default.conf",
        "infrastructure/nginx/ssl.conf",
        "infrastructure/nginx/load-balancer.conf"
    ]

    for config_file in nginx_configs:
        if not check_file_exists(config_file):
            return False

    # Check SSL configuration
    ssl_configs = [
        "infrastructure/ssl/generate-certs.sh",
        "infrastructure/ssl/cert-config.yaml"
    ]

    for config_file in ssl_configs:
        if not check_file_exists(config_file):
            return False

    # Check monitoring configuration
    monitoring_configs = [
        "infrastructure/monitoring/prometheus.yml",
        "infrastructure/monitoring/grafana-dashboard.json",
        "infrastructure/monitoring/docker-compose.monitoring.yml"
    ]

    for config_file in monitoring_configs:
        if not check_file_exists(config_file):
            return False

    # Check Docker configurations
    docker_configs = [
        "infrastructure/docker/production.yml",
        "infrastructure/docker/scaling.yml"
    ]

    for config_file in docker_configs:
        if not check_file_exists(config_file):
            return False

    print("  ✅ All infrastructure configuration files present")
    return True

def check_vector_db_init() -> bool:
    """Check vector database initialization files."""
    print_step("Checking Vector Database Initialization")

    init_files = [
        "vector-db/init/setup-collections.py",
        "vector-db/init/seed-data.py"
    ]

    for init_file in init_files:
        if not check_file_exists(init_file):
            return False

    # Check if initialization scripts are executable
    try:
        import stat
        for init_file in init_files:
            if Path(init_file).exists():
                file_stat = Path(init_file).stat()
                if not (file_stat.st_mode & stat.S_IEXEC):
                    print(f"  ⚠️  {init_file} is not executable")
    except Exception as e:
        print(f"  ⚠️  Could not check file permissions: {e}")

    print("  ✅ Vector database initialization files present")
    return True

def main():
    """Main verification function."""
    print_header("AI Coding Agent - Docker Setup Verification")

    print("🚀 Verifying multi-service Docker restructuring and infrastructure...")

    # Run all checks
    checks = [
        ("Directory Structure", check_directory_structure),
        ("Import Paths", check_import_paths),
        ("Docker Compose Config", check_docker_compose_config),
        ("Environment Config", check_environment_config),
        ("Dockerfile Paths", check_dockerfile_paths),
        ("Infrastructure Config", check_infrastructure_config),
        ("Vector DB Initialization", check_vector_db_init),
    ]

    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"  ❌ Error in {check_name}: {e}")
            results.append((check_name, False))

    # Generate .env file if needed
    generate_env_file()

    # Summary
    print_header("Verification Summary")

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {check_name}")

    print(f"\n📊 Results: {passed}/{total} checks passed")

    if passed == total:
        print("\n🎉 SUCCESS! Your AI Coding Agent is ready for multi-service Docker deployment!")
        print("\nNext steps:")
        print("1. Run: docker-compose up -d")
        print("2. Wait for all services to start")
        print("3. Test the application at http://localhost:3000")
        print("4. Check API health at http://localhost:8000/api/v1/health")
        return True
    else:
        print(f"\n❌ FAILED! {total - passed} checks failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
