version: '3.8'

# Development override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  backend:
    build:
      target: development
    volumes:
      # Enhanced volume mounts for development (corrected paths)
      - ./backend/src:/app/src
      - ./backend/tests:/app/tests
      - ./backend/config:/app/config
      - ./backend/requirements.txt:/app/requirements.txt
      - ./backend/requirements-dev.txt:/app/requirements-dev.txt
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - RELOAD=true
    command: ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

  frontend:
    build:
      target: development
    volumes:
      # Mount entire source for hot reloading
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./frontend/package.json:/app/package.json
      - ./frontend/package-lock.json:/app/package-lock.json
      - ./frontend/tsconfig.json:/app/tsconfig.json
      - ./frontend/tailwind.config.js:/app/tailwind.config.js
      - ./frontend/postcss.config.js:/app/postcss.config.js
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
    command: ["npm", "start"]
    ports:
      - "3001:3000"  # Development server port (avoiding conflict)
