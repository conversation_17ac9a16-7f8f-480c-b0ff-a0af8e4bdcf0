# JSON Serialization Integration Guide

## 🎯 **Overview**

This guide shows how the bulletproof JSON serializer integrates with our AI Coding Agent project to solve serialization issues across audit trails, dependency logging, and AI response storage.

## ✅ **What This Solves**

### **Before (Problems)**
```python
# ❌ These would break JSON serialization:
audit_data = {
    "timestamp": datetime.now(),           # Not JSON serializable
    "task_id": UUID("f47ac10b-58cc..."),  # Not JSON serializable
    "price": Decimal("99.99"),            # Not JSON serializable
    "user_data": some_pydantic_model,     # Not JSON serializable
    "binary_data": b"some bytes",         # Not JSON serializable
}

# This would crash:
json.dumps(audit_data)  # TypeError: Object of type datetime is not JSON serializable
```

### **After (Solutions)**
```python
# ✅ These work perfectly:
from ai_coding_agent.utils.serialization import serialize_for_json, to_json

audit_data = {
    "timestamp": datetime.now(),           # → "2025-08-15T10:23:00"
    "task_id": UUID("f47ac10b-58cc..."),  # → "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    "price": Decimal("99.99"),            # → 99.99
    "user_data": some_pydantic_model,     # → {"field1": "value1", ...}
    "binary_data": b"some bytes",         # → "some bytes"
}

# This works perfectly:
safe_json = to_json(audit_data)  # ✅ Success!
```

## 🔧 **Integration Points**

### **1. Audit Service (Already Integrated)**
```python
# In audit.py - automatically handles any object:
audit_service.log_action(
    entity_type=AuditEntityType.TASK,
    entity_id=task_id,
    action=AuditAction.UPDATE,
    old_values=complex_old_data,    # Any object type
    new_values=complex_new_data,    # Any object type
    metadata=ai_response_data       # Any object type
)
# ✅ All data automatically serialized safely
```

### **2. Dependency Engine Logging**
```python
# In dependency_engine.py:
from ai_coding_agent.utils.serialization import serialize_dependency_data

# Log complex dependency data safely:
dependency_data = {
    "blocking_deps": blocking_dependencies,
    "ai_predictions": ai_response,
    "cache_stats": cache_statistics,
    "performance_metrics": metrics_data
}

logger.info("Dependency check completed", extra={
    "dependency_data": serialize_dependency_data(dependency_data)
})
```

### **3. AI Orchestrator Integration**
```python
# In ai/orchestrator.py:
from ai_coding_agent.utils.serialization import serialize_ai_response

# Log AI responses safely:
ai_response = await model.generate(prompt)
logger.info("AI response generated", extra={
    "response": serialize_ai_response(ai_response),
    "model_metadata": ai_response.metadata,
    "tokens_used": ai_response.usage
})
```

### **4. Task/Roadmap Changes**
```python
# In roadmap.py:
from ai_coding_agent.utils.serialization import serialize_task_data

# Audit task changes:
audit_service.log_action(
    entity_type=AuditEntityType.TASK,
    entity_id=task.id,
    action=AuditAction.UPDATE,
    old_values=serialize_task_data(old_task),
    new_values=serialize_task_data(updated_task),
    metadata={
        "change_reason": reason,
        "ai_suggestions": ai_suggestions,
        "user_context": user_context
    }
)
```

## 🚀 **Setup Instructions**

### **1. Initialize Auto-Serialization (One-time setup)**
```python
# In main.py or app initialization:
from ai_coding_agent.services.audit import setup_audit_serialization_hooks

# Set up automatic serialization for all audit data
setup_audit_serialization_hooks()

# Now all audit inserts/updates automatically serialize data!
```

### **2. Use in Your Code**
```python
# Import the utilities:
from ai_coding_agent.utils.serialization import (
    serialize_for_json,      # General purpose
    to_json,                 # Convert to JSON string
    prepare_for_logging,     # For logging contexts
    prepare_for_audit,       # For audit contexts
    serialize_dependency_data,  # Dependency-specific
    serialize_ai_response,   # AI response-specific
    serialize_task_data,     # Task/roadmap-specific
)

# Use anywhere you need safe JSON:
safe_data = serialize_for_json(any_complex_object)
json_string = to_json(any_complex_object)
```

## 📊 **Supported Object Types**

| Object Type | Conversion | Example |
|-------------|------------|---------|
| `datetime` | ISO string | `"2025-08-15T10:23:00"` |
| `date` | ISO string | `"2025-08-15"` |
| `UUID` | String | `"f47ac10b-58cc-4372-a567-0e02b2c3d479"` |
| `Decimal` | Number | `99.99` or `100` |
| `bytes` | UTF-8 string | `"decoded string"` |
| `Pydantic v1` | Dict | `{"field": "value"}` |
| `Pydantic v2` | Dict | `{"field": "value"}` |
| `Custom objects` | Dict or string | `{"attr": "value"}` |
| `set` | List | `[1, 2, 3]` |
| `tuple` | Tuple | `[1, 2, 3]` |
| `Nested structures` | Recursively processed | Deep serialization |

## 🛡️ **Error Handling**

The serializer is bulletproof and never crashes:

```python
# Even weird objects work:
weird_object = SomeUnknownClass()
result = serialize_for_json(weird_object)
# Result: "<non-serializable: SomeUnknownClass>" or string representation
```

## 🎯 **Real-World Examples**

### **Dependency Engine Audit**
```python
# Complex dependency check result:
dependency_result = DependencyCheckResult(
    status=DependencyCheckStatus.BLOCKED,
    blocking_dependencies=[
        BlockingDependency(
            dependency_id="task_123",
            dependency_type=DependencyType.TASK,
            current_status=TaskStatus.IN_PROGRESS,
            estimated_completion=datetime.now() + timedelta(hours=2)
        )
    ],
    ai_predictions=["database_setup", "api_integration"],
    cache_stats={"hit_rate": 0.85, "total_checks": 1247}
)

# This just works:
audit_service.log_action(
    entity_type=AuditEntityType.DEPENDENCY,
    entity_id="dep_check_456",
    action=AuditAction.CHECK,
    new_values=dependency_result,  # ✅ Automatically serialized
    metadata={"performance_ms": 45.2}
)
```

### **AI Response Logging**
```python
# Complex AI response with metadata:
ai_response = {
    "generated_text": "Create user authentication system",
    "model": "qwen2.5:3b",
    "timestamp": datetime.now(),
    "usage": {"tokens": 150, "cost": Decimal("0.0023")},
    "metadata": some_pydantic_model,
    "raw_response": complex_response_object
}

# Safe logging:
logger.info("AI prediction completed", extra={
    "ai_data": serialize_ai_response(ai_response)
})
```

## 🔄 **Migration from Existing Code**

### **Before**
```python
# Old way (limited, error-prone):
try:
    json_data = json.dumps(data)
except TypeError:
    # Handle each type manually...
    pass
```

### **After**
```python
# New way (bulletproof):
json_data = to_json(data)  # Always works!
```

## 📈 **Benefits**

1. **Never Breaks**: Handles any Python object safely
2. **Zero Configuration**: Works out of the box
3. **Automatic**: SQLAlchemy hooks handle audit data automatically
4. **Performance**: Efficient recursive processing
5. **Comprehensive**: Handles Pydantic v1/v2, custom objects, nested structures
6. **Future-Proof**: Handles new object types gracefully

## 🎉 **Result**

With this integration, our AI Coding Agent project now has:
- ✅ Bulletproof audit trails that never break
- ✅ Safe dependency engine logging
- ✅ Reliable AI response storage
- ✅ Robust task/roadmap change tracking
- ✅ Future-proof serialization for any new features

**No more JSON serialization errors, ever!** 🎯
