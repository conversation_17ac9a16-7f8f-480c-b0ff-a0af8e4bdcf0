"""
Test routes to verify route registration works.
"""

from fastapi import APIRouter

# Create a test router
test_router = APIRouter(
    prefix="/api/v1/test",
    tags=["test"]
)

@test_router.get("/simple")
async def test_simple():
    """Simple test route."""
    return {"message": "Test route working"}

@test_router.get("/auth-check")
async def test_auth_check():
    """Test auth check route."""
    return {"status": "ok", "message": "Test auth check working"}

@test_router.get("/get-port")
async def test_get_port():
    """Test port resolution route."""
    return {"port": 3000, "message": "Test port resolution working"}
