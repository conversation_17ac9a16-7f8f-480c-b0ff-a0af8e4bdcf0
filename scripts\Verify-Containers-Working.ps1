# PowerShell Container Verification Script for AI Coding Agent
# This script verifies that all essential files are properly included in containers
# Usage: .\scripts\Verify-Containers-Working.ps1

Write-Host "AI Coding Agent - Container File Verification" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Function to check if container is running
function Test-ContainerRunning {
    param([string]$ContainerName)
    
    try {
        $result = docker-compose ps --services --filter "status=running" 2>$null
        if ($result -contains $ContainerName) {
            Write-Host "[OK] $ContainerName container is running" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[ERROR] $ContainerName container is not running" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[ERROR] Error checking $ContainerName container status" -ForegroundColor Red
        return $false
    }
}

# Function to verify file exists in container
function Test-FileInContainer {
    param(
        [string]$ContainerName,
        [string]$FilePath,
        [string]$Description
    )
    
    try {
        $result = docker-compose exec -T $ContainerName test -f $FilePath 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "[OK] $Description : $FilePath" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[MISSING] $Description : $FilePath" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[ERROR] Error checking $Description : $FilePath" -ForegroundColor Red
        return $false
    }
}

# Function to verify directory exists in container
function Test-DirectoryInContainer {
    param(
        [string]$ContainerName,
        [string]$DirectoryPath,
        [string]$Description
    )
    
    try {
        $result = docker-compose exec -T $ContainerName test -d $DirectoryPath 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "[OK] $Description : $DirectoryPath" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[MISSING] $Description : $DirectoryPath" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[ERROR] Error checking $Description : $DirectoryPath" -ForegroundColor Red
        return $false
    }
}

Write-Host ""
Write-Host "Checking Container Status" -ForegroundColor Yellow
Write-Host "------------------------" -ForegroundColor Yellow

# Check if Docker is running
try {
    docker version >$null 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] Docker is not running. Please start Docker Desktop." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "[ERROR] Docker is not installed or not in PATH." -ForegroundColor Red
    exit 1
}

# Check if containers are running
$backendRunning = Test-ContainerRunning "backend"
$frontendRunning = Test-ContainerRunning "frontend"

Write-Host ""
Write-Host "Backend Container Verification" -ForegroundColor Yellow
Write-Host "------------------------------" -ForegroundColor Yellow

if ($backendRunning) {
    # Verify backend file structure
    Test-DirectoryInContainer "backend" "/app" "App root directory"
    Test-DirectoryInContainer "backend" "/app/ai_coding_agent" "Main application code"
    Test-DirectoryInContainer "backend" "/app/config" "Configuration directory"
    Test-DirectoryInContainer "backend" "/app/scripts" "Scripts directory"
    Test-DirectoryInContainer "backend" "/app/tests" "Tests directory"
    
    # Verify key files
    Test-FileInContainer "backend" "/app/requirements.txt" "Requirements file"
    Test-FileInContainer "backend" "/app/ai_coding_agent/main.py" "Main application file"
    Test-FileInContainer "backend" "/app/ai_coding_agent/__init__.py" "Package init file"
    Test-FileInContainer "backend" "/app/scripts/test_migration.py" "Migration test script"
    Test-FileInContainer "backend" "/app/scripts/setup_pgvector.sql" "pgvector setup script"
    
    # Verify service files
    Test-FileInContainer "backend" "/app/ai_coding_agent/services/vector_db.py" "Vector DB service"
    Test-FileInContainer "backend" "/app/ai_coding_agent/services/redis_cache.py" "Redis cache service"
    Test-FileInContainer "backend" "/app/ai_coding_agent/config.py" "Configuration module"
    
    # Check Python dependencies
    Write-Host ""
    Write-Host "Checking Python Dependencies:" -ForegroundColor Blue
    
    $dependencies = @("redis", "supabase", "langchain", "asyncpg")
    
    foreach ($dep in $dependencies) {
        try {
            docker-compose exec -T backend python -c "import $dep; print('[OK] $dep installed')" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "[OK] $dep installed" -ForegroundColor Green
            } else {
                Write-Host "[ERROR] $dep not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "[ERROR] $dep not installed" -ForegroundColor Red
        }
    }
} else {
    Write-Host "[WARNING] Backend container not running - skipping verification" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Frontend Container Verification" -ForegroundColor Yellow
Write-Host "-------------------------------" -ForegroundColor Yellow

if ($frontendRunning) {
    # Verify frontend file structure
    Test-DirectoryInContainer "frontend" "/usr/share/nginx/html" "Nginx web root"
    Test-FileInContainer "frontend" "/usr/share/nginx/html/index.html" "Main HTML file"
    Test-FileInContainer "frontend" "/etc/nginx/conf.d/default.conf" "Nginx configuration"
    
    # Check if static assets exist
    Write-Host ""
    Write-Host "Checking Frontend Assets:" -ForegroundColor Blue
    
    try {
        $jsFiles = docker-compose exec -T frontend find /usr/share/nginx/html -name "*.js" 2>$null
        if ($jsFiles) {
            Write-Host "[OK] JavaScript files found" -ForegroundColor Green
        } else {
            Write-Host "[ERROR] No JavaScript files found" -ForegroundColor Red
        }
    } catch {
        Write-Host "[ERROR] Error checking JavaScript files" -ForegroundColor Red
    }
    
    try {
        $cssFiles = docker-compose exec -T frontend find /usr/share/nginx/html -name "*.css" 2>$null
        if ($cssFiles) {
            Write-Host "[OK] CSS files found" -ForegroundColor Green
        } else {
            Write-Host "[ERROR] No CSS files found" -ForegroundColor Red
        }
    } catch {
        Write-Host "[ERROR] Error checking CSS files" -ForegroundColor Red
    }
} else {
    Write-Host "[WARNING] Frontend container not running - skipping verification" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Quick Functionality Tests" -ForegroundColor Yellow
Write-Host "------------------------" -ForegroundColor Yellow

if ($backendRunning) {
    Write-Host "Testing backend health endpoint..."
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/health" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "[OK] Backend health check passed" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Backend health check returned status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[WARNING] Backend health check failed (may still be starting)" -ForegroundColor Yellow
    }
}

if ($frontendRunning) {
    Write-Host "Testing frontend accessibility..."
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "[OK] Frontend accessible on port 3000" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Frontend returned status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[WARNING] Frontend not accessible on port 3000" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "Verification Complete!" -ForegroundColor Cyan
Write-Host ""
Write-Host "Services running:" -ForegroundColor Cyan
Write-Host "- Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "- Frontend App: http://localhost:3000" -ForegroundColor White
Write-Host "- PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "- Redis: localhost:6379" -ForegroundColor White
Write-Host ""
Write-Host "=============================================" -ForegroundColor Cyan
