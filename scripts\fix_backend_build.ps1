# Backend Docker Build Fix Script
# This script applies fixes based on common build failure scenarios

param(
    [switch]$UseCompatibleRequirements = $false,
    [switch]$UseFixedDockerfile = $false,
    [switch]$CleanBuild = $false
)

Write-Host "🔧 Backend Docker Build Fix Script" -ForegroundColor Cyan
Write-Host "==================================="

function Write-Step {
    param([string]$Message)
    Write-Host "📋 $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

# Step 1: Clean up if requested
if ($CleanBuild) {
    Write-Step "Cleaning Docker build cache and containers"
    
    # Stop and remove existing containers
    docker-compose down 2>$null
    
    # Remove backend images
    docker images | Select-String "ai-coding-agent-backend" | ForEach-Object {
        $imageId = ($_ -split '\s+')[2]
        docker rmi $imageId -f 2>$null
    }
    
    # Clean build cache
    docker builder prune -f 2>$null
    
    Write-Success "Docker cleanup completed"
}

# Step 2: Backup original files
Write-Step "Backing up original files"

if (Test-Path "backend/Dockerfile.original") {
    Write-Warning "Backup already exists, skipping backup"
} else {
    Copy-Item "backend/Dockerfile" "backend/Dockerfile.original"
    Write-Success "Original Dockerfile backed up"
}

if (Test-Path "backend/requirements.txt.original") {
    Write-Warning "Requirements backup already exists, skipping backup"
} else {
    Copy-Item "backend/requirements.txt" "backend/requirements.txt.original"
    Write-Success "Original requirements.txt backed up"
}

# Step 3: Apply fixes based on parameters
if ($UseFixedDockerfile) {
    Write-Step "Applying fixed Dockerfile"
    
    if (Test-Path "backend/Dockerfile.fixed") {
        Copy-Item "backend/Dockerfile.fixed" "backend/Dockerfile"
        Write-Success "Fixed Dockerfile applied"
    } else {
        Write-Error "Fixed Dockerfile not found!"
        exit 1
    }
}

if ($UseCompatibleRequirements) {
    Write-Step "Applying compatible requirements"
    
    if (Test-Path "backend/requirements.compatible.txt") {
        Copy-Item "backend/requirements.compatible.txt" "backend/requirements.txt"
        Write-Success "Compatible requirements applied"
    } else {
        Write-Error "Compatible requirements file not found!"
        exit 1
    }
}

# Step 4: Test build
Write-Step "Testing backend build"

Write-Host "Building backend container (this may take several minutes)..." -ForegroundColor Yellow

$buildStart = Get-Date
try {
    $buildOutput = docker build --no-cache --progress=plain --target=production ./backend 2>&1
    $buildEnd = Get-Date
    $buildDuration = $buildEnd - $buildStart
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Build completed successfully in $($buildDuration.TotalMinutes.ToString('F1')) minutes"
        
        # Test the built image
        Write-Step "Testing built image"
        $testOutput = docker run --rm ai-coding-agent-backend python -c "import ai_coding_agent; print('Module import successful')" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Image test passed - Python imports working"
        } else {
            Write-Warning "Image built but Python import test failed"
            Write-Host "Test output: $testOutput" -ForegroundColor Gray
        }
        
    } else {
        Write-Error "Build failed after $($buildDuration.TotalMinutes.ToString('F1')) minutes"
        
        # Show last 10 lines of build output
        Write-Host "`nLast 10 lines of build output:" -ForegroundColor Yellow
        $buildOutput | Select-Object -Last 10 | ForEach-Object { Write-Host $_ -ForegroundColor Gray }
        
        # Suggest next steps
        Write-Host "`nSuggested next steps:" -ForegroundColor Yellow
        Write-Host "1. Run: .\scripts\diagnose_build_failure.ps1" -ForegroundColor Gray
        Write-Host "2. Try: .\scripts\fix_backend_build.ps1 -UseCompatibleRequirements -UseFixedDockerfile" -ForegroundColor Gray
        Write-Host "3. Check the full build log above for specific error messages" -ForegroundColor Gray
        
        exit 1
    }
    
} catch {
    Write-Error "Build command failed: $($_.Exception.Message)"
    exit 1
}

# Step 5: Restore original files if requested
$restore = Read-Host "`nRestore original files? (y/N)"
if ($restore -eq 'y' -or $restore -eq 'Y') {
    Write-Step "Restoring original files"
    
    if (Test-Path "backend/Dockerfile.original") {
        Copy-Item "backend/Dockerfile.original" "backend/Dockerfile"
        Write-Success "Original Dockerfile restored"
    }
    
    if (Test-Path "backend/requirements.txt.original") {
        Copy-Item "backend/requirements.txt.original" "backend/requirements.txt"
        Write-Success "Original requirements.txt restored"
    }
}

Write-Host "`n🎉 Build fix script completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. If build succeeded, run: docker-compose up -d" -ForegroundColor Gray
Write-Host "2. Test the application: curl http://localhost:8000/api/v1/health" -ForegroundColor Gray
Write-Host "3. If issues persist, check the diagnostic logs" -ForegroundColor Gray
