#!/usr/bin/env python3
"""
Test the enhanced models configuration with health routing and task-specific settings.
"""

import sys
import os
import asyncio
from pathlib import Path

# Import path is handled by pyproject.toml configuration

from ai_coding_agent.utils.models import get_models_config, EnhancedModelsConfig


def test_models_config_loading():
    """Test loading and validating the enhanced models configuration."""
    print("🧪 Testing Enhanced Models Configuration")
    print("=" * 50)

    try:
        config = get_models_config()

        print("✅ Configuration loaded successfully")
        print(f"📁 Config path: {config.config_path}")

        # Test health routing settings
        health_config = config.config.get("health_routing", {})
        print(f"🏥 Health routing enabled: {health_config.get('auto_fallback_on_failure', False)}")
        print(f"🎯 Health threshold: {health_config.get('health_score_threshold', 0.8)}")

        # Test task timeouts
        task_timeouts = config.config.get("task_timeouts", {})
        print(f"⏱️  Task timeouts configured: {len(task_timeouts)} task types")

        for task_type, timeout in task_timeouts.items():
            print(f"   {task_type}: {timeout}s")

        # Test model quality overrides
        quality_overrides = config.config.get("model_quality_overrides", {})
        print(f"🎛️  Quality overrides: {len(quality_overrides)} models")

        for model, overrides in quality_overrides.items():
            print(f"   {model}: {len(overrides)} custom thresholds")

        print("\n🔍 Testing Agent Model Selection:")

        # Test different agent routing scenarios
        test_cases = [
            ("architect", "planning"),
            ("frontend", "code_completion"),
            ("backend", "code_generation"),
            ("shell", "system_commands"),
            ("debug", "debugging"),
            ("test", "unit_testing")
        ]

        for agent, task_type in test_cases:
            model = config.get_model_for_agent(agent, task_type)
            timeout = config.get_task_timeout(task_type)
            thresholds = config.get_quality_thresholds(model)

            print(f"🤖 {agent.title()} Agent ({task_type}):")
            print(f"   Model: {model}")
            print(f"   Timeout: {timeout}s")
            print(f"   Quality threshold: {thresholds.get('minimum_confidence', 0.7)}")

        print("\n🏥 Testing Health Tracking:")

        # Test health tracking
        test_model = "mistral:7b-instruct-q4_0"

        # Record some successes and failures
        config.record_model_success(test_model)
        print(f"✅ Recorded success for {test_model}")

        config.record_model_failure(test_model)
        print(f"❌ Recorded failure for {test_model}")

        health_status = config.get_health_status()
        if test_model in health_status:
            status = health_status[test_model]
            print(f"📊 {test_model} health:")
            print(f"   Score: {status['health_score']:.2f}")
            print(f"   Healthy: {status['is_healthy']}")
            print(f"   Failures: {status['failure_count']}")

        print("\n🎯 Configuration Test Summary:")
        print("✅ Enhanced routing configuration loaded")
        print("✅ Health tracking system operational")
        print("✅ Task-specific timeouts configured")
        print("✅ Model quality overrides working")
        print("✅ Agent model selection functional")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_fallback_scenarios():
    """Test model fallback scenarios."""
    print("\n🔄 Testing Fallback Scenarios")
    print("=" * 30)

    config = get_models_config()

    # Simulate a model failure
    test_model = "yi-coder:1.5b"
    print(f"🧪 Simulating failures for {test_model}")

    # Record multiple failures to trigger unhealthy status
    for i in range(4):  # Exceed max_consecutive_failures
        config.record_model_failure(test_model)
        print(f"   Failure {i+1} recorded")

    # Test if the system falls back correctly
    print(f"\n🔍 Testing frontend agent routing after {test_model} failures:")

    # Frontend agent should fallback from yi-coder to starcoder2
    model = config.get_model_for_agent("frontend", "code_completion")
    print(f"Selected model: {model}")

    if model != test_model:
        print("✅ Fallback system working correctly")
    else:
        print("⚠️  Fallback may not be working as expected")

    # Check health status
    health_status = config.get_health_status()
    if test_model in health_status:
        status = health_status[test_model]
        print(f"📊 {test_model} status after failures:")
        print(f"   Healthy: {status['is_healthy']}")
        print(f"   Health Score: {status['health_score']:.2f}")


if __name__ == "__main__":
    print("🚀 Enhanced Models Configuration Test Suite")
    print("=" * 60)

    success = test_models_config_loading()

    if success:
        test_fallback_scenarios()
        print("\n🎉 All tests completed successfully!")
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)
