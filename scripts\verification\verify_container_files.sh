#!/bin/bash

# Container File Verification Script for AI Coding Agent
# This script verifies that all essential files are properly included in containers

set -e

echo "🔍 AI Coding Agent - Container File Verification"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if container is running
check_container_running() {
    local container_name=$1
    if docker-compose ps | grep -q "$container_name.*Up"; then
        echo -e "${GREEN}✅ $container_name container is running${NC}"
        return 0
    else
        echo -e "${RED}❌ $container_name container is not running${NC}"
        return 1
    fi
}

# Function to verify file exists in container
verify_file_in_container() {
    local container_name=$1
    local file_path=$2
    local description=$3
    
    if docker-compose exec -T "$container_name" test -f "$file_path" 2>/dev/null; then
        echo -e "${GREEN}✅ $description: $file_path${NC}"
        return 0
    else
        echo -e "${RED}❌ Missing $description: $file_path${NC}"
        return 1
    fi
}

# Function to verify directory exists in container
verify_dir_in_container() {
    local container_name=$1
    local dir_path=$2
    local description=$3
    
    if docker-compose exec -T "$container_name" test -d "$dir_path" 2>/dev/null; then
        echo -e "${GREEN}✅ $description: $dir_path${NC}"
        return 0
    else
        echo -e "${RED}❌ Missing $description: $dir_path${NC}"
        return 1
    fi
}

# Function to list directory contents in container
list_container_directory() {
    local container_name=$1
    local dir_path=$2
    local description=$3
    
    echo -e "${BLUE}📁 $description ($dir_path):${NC}"
    if docker-compose exec -T "$container_name" ls -la "$dir_path" 2>/dev/null; then
        return 0
    else
        echo -e "${RED}❌ Cannot access directory: $dir_path${NC}"
        return 1
    fi
}

echo ""
echo "🐳 Checking Container Status"
echo "----------------------------"

# Check if containers are running
backend_running=false
frontend_running=false

if check_container_running "backend"; then
    backend_running=true
fi

if check_container_running "frontend"; then
    frontend_running=true
fi

echo ""
echo "🔧 Backend Container Verification"
echo "--------------------------------"

if [ "$backend_running" = true ]; then
    # Verify backend file structure
    verify_dir_in_container "backend" "/app" "App root directory"
    verify_dir_in_container "backend" "/app/ai_coding_agent" "Main application code"
    verify_dir_in_container "backend" "/app/config" "Configuration directory"
    verify_dir_in_container "backend" "/app/scripts" "Scripts directory"
    verify_dir_in_container "backend" "/app/tests" "Tests directory"
    
    # Verify key files
    verify_file_in_container "backend" "/app/requirements.txt" "Requirements file"
    verify_file_in_container "backend" "/app/ai_coding_agent/main.py" "Main application file"
    verify_file_in_container "backend" "/app/ai_coding_agent/__init__.py" "Package init file"
    verify_file_in_container "backend" "/app/scripts/test_migration.py" "Migration test script"
    verify_file_in_container "backend" "/app/scripts/setup_pgvector.sql" "pgvector setup script"
    
    # Verify service files
    verify_file_in_container "backend" "/app/ai_coding_agent/services/vector_db.py" "Vector DB service"
    verify_file_in_container "backend" "/app/ai_coding_agent/services/redis_cache.py" "Redis cache service"
    verify_file_in_container "backend" "/app/ai_coding_agent/config.py" "Configuration module"
    
    # List key directories
    echo ""
    list_container_directory "backend" "/app" "App root"
    echo ""
    list_container_directory "backend" "/app/ai_coding_agent" "Main application"
    echo ""
    list_container_directory "backend" "/app/scripts" "Scripts"
    
    # Check Python dependencies
    echo ""
    echo -e "${BLUE}📦 Checking Python Dependencies:${NC}"
    echo "Redis:"
    docker-compose exec -T backend python -c "import redis; print('✅ Redis installed')" 2>/dev/null || echo "❌ Redis not installed"
    echo "Supabase:"
    docker-compose exec -T backend python -c "import supabase; print('✅ Supabase installed')" 2>/dev/null || echo "❌ Supabase not installed"
    echo "LangChain:"
    docker-compose exec -T backend python -c "import langchain; print('✅ LangChain installed')" 2>/dev/null || echo "❌ LangChain not installed"
    echo "AsyncPG (for pgvector):"
    docker-compose exec -T backend python -c "import asyncpg; print('✅ AsyncPG installed')" 2>/dev/null || echo "❌ AsyncPG not installed"
    
else
    echo -e "${YELLOW}⚠️ Backend container not running - skipping verification${NC}"
fi

echo ""
echo "🌐 Frontend Container Verification"
echo "---------------------------------"

if [ "$frontend_running" = true ]; then
    # Verify frontend file structure
    verify_dir_in_container "frontend" "/usr/share/nginx/html" "Nginx web root"
    verify_file_in_container "frontend" "/usr/share/nginx/html/index.html" "Main HTML file"
    verify_file_in_container "frontend" "/etc/nginx/conf.d/default.conf" "Nginx configuration"
    
    # List web root contents
    echo ""
    list_container_directory "frontend" "/usr/share/nginx/html" "Web root contents"
    
    # Check if static assets exist
    echo ""
    echo -e "${BLUE}📦 Checking Frontend Assets:${NC}"
    if docker-compose exec -T frontend find /usr/share/nginx/html -name "*.js" | head -5; then
        echo -e "${GREEN}✅ JavaScript files found${NC}"
    else
        echo -e "${RED}❌ No JavaScript files found${NC}"
    fi
    
    if docker-compose exec -T frontend find /usr/share/nginx/html -name "*.css" | head -5; then
        echo -e "${GREEN}✅ CSS files found${NC}"
    else
        echo -e "${RED}❌ No CSS files found${NC}"
    fi
    
else
    echo -e "${YELLOW}⚠️ Frontend container not running - skipping verification${NC}"
fi

echo ""
echo "💾 Volume Mount Verification"
echo "---------------------------"

if [ "$backend_running" = true ]; then
    # Check volume mounts
    verify_dir_in_container "backend" "/app/logs" "Logs directory"
    verify_dir_in_container "backend" "/app/user-projects" "User projects directory"
    verify_dir_in_container "backend" "/app/uploads" "Uploads directory"
    
    # Check volume permissions
    echo ""
    echo -e "${BLUE}🔐 Checking Volume Permissions:${NC}"
    docker-compose exec -T backend ls -la /app/ | grep -E "(logs|user-projects|uploads)"
fi

echo ""
echo "🧪 Quick Functionality Tests"
echo "---------------------------"

if [ "$backend_running" = true ]; then
    echo "Testing migration script:"
    if docker-compose exec -T backend python scripts/test_migration.py --quick 2>/dev/null; then
        echo -e "${GREEN}✅ Migration test passed${NC}"
    else
        echo -e "${YELLOW}⚠️ Migration test failed (may need configuration)${NC}"
    fi
    
    echo ""
    echo "Testing Redis connection:"
    if docker-compose exec -T backend python -c "import redis; r=redis.Redis(host='redis', port=6379); print('Redis ping:', r.ping())" 2>/dev/null; then
        echo -e "${GREEN}✅ Redis connection successful${NC}"
    else
        echo -e "${YELLOW}⚠️ Redis connection failed${NC}"
    fi
fi

if [ "$frontend_running" = true ]; then
    echo ""
    echo "Testing frontend accessibility:"
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
        echo -e "${GREEN}✅ Frontend accessible on port 3000${NC}"
    else
        echo -e "${YELLOW}⚠️ Frontend not accessible on port 3000${NC}"
    fi
fi

echo ""
echo "=================================================="
echo "🎯 Verification Complete!"
echo ""
echo "If you see any ❌ errors above, run the following commands:"
echo ""
echo "1. Rebuild containers with all files:"
echo "   docker-compose down"
echo "   docker-compose build --no-cache"
echo "   docker-compose up -d"
echo ""
echo "2. Check specific issues:"
echo "   docker-compose logs backend"
echo "   docker-compose logs frontend"
echo ""
echo "3. Verify file copying manually:"
echo "   docker-compose exec backend ls -la /app/"
echo "   docker-compose exec frontend ls -la /usr/share/nginx/html/"
echo "=================================================="
