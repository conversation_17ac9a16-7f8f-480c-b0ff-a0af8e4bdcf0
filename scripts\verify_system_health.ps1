# AI Coding Agent - System Health Verification Script
# Performs comprehensive verification of all system components

param(
    [switch]$Detailed,
    [switch]$SecurityOnly,
    [switch]$IntegrationOnly
)

Write-Host "🔍 AI Coding Agent - System Health Verification" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

$results = @{
    "Docker Build" = $false
    "Security Compliance" = $false
    "Integration Tests" = $false
    "Service Health" = $false
}

# Function to test Docker build
function Test-DockerBuild {
    Write-Host "`n🐳 Testing Docker Build..." -ForegroundColor Yellow
    
    try {
        # Test production build
        $buildOutput = docker build --target=production -t ai-coding-agent-test ./backend 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker build successful" -ForegroundColor Green
            
            # Test module import
            $importTest = docker run --rm ai-coding-agent-test python -c "import ai_coding_agent; print('SUCCESS')" 2>&1
            if ($importTest -match "SUCCESS") {
                Write-Host "✅ Module import successful" -ForegroundColor Green
                return $true
            } else {
                Write-Host "❌ Module import failed: $importTest" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ Docker build failed" -ForegroundColor Red
            if ($Detailed) {
                Write-Host "Build output: $buildOutput" -ForegroundColor Gray
            }
            return $false
        }
    } catch {
        Write-Host "❌ Docker build error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test security compliance
function Test-SecurityCompliance {
    Write-Host "`n🔒 Testing Security Compliance..." -ForegroundColor Yellow
    
    try {
        # Test non-root user
        $userTest = docker run --rm ai-coding-agent-test whoami 2>&1
        if ($userTest -match "appuser") {
            Write-Host "✅ Container runs as non-root user (appuser)" -ForegroundColor Green
            
            # Test user ID
            $idTest = docker run --rm ai-coding-agent-test id 2>&1
            if ($idTest -match "uid=1000.*appuser") {
                Write-Host "✅ Correct user ID (1000) and group" -ForegroundColor Green
                
                # Test file permissions
                $permTest = docker run --rm ai-coding-agent-test ls -la /app 2>&1
                if ($permTest -match "appuser.*appuser") {
                    Write-Host "✅ File permissions correctly set" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "❌ File permissions incorrect" -ForegroundColor Red
                    if ($Detailed) {
                        Write-Host "Permissions: $permTest" -ForegroundColor Gray
                    }
                    return $false
                }
            } else {
                Write-Host "❌ Incorrect user ID or group" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ Container running as root user" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Security test error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test service integration
function Test-ServiceIntegration {
    Write-Host "`n🔌 Testing Service Integration..." -ForegroundColor Yellow
    
    try {
        # Check if services are running
        $services = docker-compose ps --services 2>&1
        if ($services -match "backend") {
            Write-Host "✅ Docker Compose services configured" -ForegroundColor Green
            
            # Test health endpoint
            try {
                $healthResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/health" -TimeoutSec 10
                if ($healthResponse.status -eq "healthy") {
                    Write-Host "✅ Backend health endpoint responding" -ForegroundColor Green
                    Write-Host "   Version: $($healthResponse.version)" -ForegroundColor Gray
                    Write-Host "   Environment: $($healthResponse.environment)" -ForegroundColor Gray
                    Write-Host "   Uptime: $($healthResponse.uptime) seconds" -ForegroundColor Gray
                    return $true
                } else {
                    Write-Host "❌ Backend health check failed" -ForegroundColor Red
                    return $false
                }
            } catch {
                Write-Host "⚠️  Backend not running or not accessible" -ForegroundColor Yellow
                Write-Host "   Try: docker-compose up -d backend" -ForegroundColor Gray
                return $false
            }
        } else {
            Write-Host "❌ Docker Compose not configured properly" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Integration test error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test environment configuration
function Test-EnvironmentConfig {
    Write-Host "`n⚙️  Testing Environment Configuration..." -ForegroundColor Yellow
    
    try {
        if (Test-Path ".env") {
            Write-Host "✅ .env file exists" -ForegroundColor Green
            
            # Check for required variables
            $envContent = Get-Content ".env" -Raw
            $requiredVars = @("SECRET_KEY", "CONFIG_ENCRYPTION_KEY", "SUPABASE_URL", "SUPABASE_ANON_KEY")
            $missingVars = @()
            
            foreach ($var in $requiredVars) {
                if ($envContent -notmatch "$var=") {
                    $missingVars += $var
                }
            }
            
            if ($missingVars.Count -eq 0) {
                Write-Host "✅ All required environment variables present" -ForegroundColor Green
                return $true
            } else {
                Write-Host "❌ Missing environment variables: $($missingVars -join ', ')" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ .env file not found" -ForegroundColor Red
            Write-Host "   Copy .env.example to .env and configure" -ForegroundColor Gray
            return $false
        }
    } catch {
        Write-Host "❌ Environment config error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
try {
    # Environment check (always run)
    $results["Environment Config"] = Test-EnvironmentConfig
    
    if (-not $IntegrationOnly) {
        # Docker build test
        $results["Docker Build"] = Test-DockerBuild
        
        # Security compliance test
        if ($results["Docker Build"]) {
            $results["Security Compliance"] = Test-SecurityCompliance
        }
    }
    
    if (-not $SecurityOnly) {
        # Integration tests
        $results["Integration Tests"] = Test-ServiceIntegration
    }
    
    # Summary
    Write-Host "`n📊 Test Results Summary" -ForegroundColor Cyan
    Write-Host "=" * 30 -ForegroundColor Cyan
    
    $passedTests = 0
    $totalTests = $results.Count
    
    foreach ($test in $results.GetEnumerator()) {
        if ($test.Value) {
            Write-Host "✅ $($test.Key)" -ForegroundColor Green
            $passedTests++
        } else {
            Write-Host "❌ $($test.Key)" -ForegroundColor Red
        }
    }
    
    $successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
    Write-Host "`nOverall Success Rate: $successRate% ($passedTests/$totalTests)" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })
    
    if ($successRate -eq 100) {
        Write-Host "`n🎉 All systems operational! Ready for production." -ForegroundColor Green
    } elseif ($successRate -ge 80) {
        Write-Host "`n⚠️  Most systems operational. Address failing tests." -ForegroundColor Yellow
    } else {
        Write-Host "`n🚨 Multiple system failures. Review configuration." -ForegroundColor Red
    }
    
} catch {
    Write-Host "`n💥 Script execution error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Cleanup test image
try {
    docker rmi ai-coding-agent-test -f 2>&1 | Out-Null
} catch {
    # Ignore cleanup errors
}

Write-Host "`n✅ System health verification complete!" -ForegroundColor Cyan
