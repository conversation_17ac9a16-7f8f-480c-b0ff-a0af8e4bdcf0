"""
Test configuration and fixtures for AI Coding Agent.

This module provides test configuration, database setup,
and common fixtures for testing.
"""

import os
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

# Set test environment before importing app
os.environ["ENVIRONMENT"] = "testing"
os.environ["SECRET_KEY"] = "test_secret_key_that_is_at_least_32_characters_long"
os.environ["DB_PASSWORD"] = "test_password"

from ai_coding_agent.main import create_app
from ai_coding_agent.models import Base, get_db


# Test database configuration - now using Supabase for all tests
# SQLite removed as per architectural requirements

from unittest.mock import AsyncMock, MagicMock
from src.ai_coding_agent.services.supabase import SupabaseService


def create_mock_supabase():
    """Create a mock Supabase service for testing."""
    mock_supabase = MagicMock(spec=SupabaseService)

    # Mock async methods
    mock_supabase.create_record = AsyncMock()
    mock_supabase.get_record = AsyncMock()
    mock_supabase.update_record = AsyncMock()
    mock_supabase.delete_record = AsyncMock()
    mock_supabase.list_records = AsyncMock()
    mock_supabase.get_connection_status = MagicMock(return_value={
        "connected": True,
        "url": "mock://supabase.test",
        "has_data": True,
        "error": None
    })

    return mock_supabase


@pytest.fixture(scope="function")
def mock_supabase():
    """Provide a mock Supabase service for testing."""
    return create_mock_supabase()


@pytest.fixture(scope="function")
def test_db():
    """
    Provide a test database session.

    Note: This now uses mock Supabase instead of SQLite.
    For integration tests with real Supabase, use the test environment.
    """
    return create_mock_supabase()


@pytest.fixture(scope="function")
def app():
    """Create a test FastAPI application."""
    test_app = create_app()
    test_app.dependency_overrides[get_db] = override_get_db
    return test_app


@pytest.fixture(scope="function")
def client(app, test_db):
    """Create a test client for the FastAPI application."""
    with TestClient(app) as test_client:
        yield test_client


# Orchestrator test fixtures
@pytest.fixture
def basic_orchestrator_config():
    """Basic configuration for orchestrator testing."""
    return {
        "providers": {
            "ollama": {
                "host": "http://localhost:11434",
                "models": {
                    "test-model-fast": {
                        "role": "test",
                        "agents": ["test_agent"],
                        "description": "Fast test model",
                        "performance": "fast"
                    },
                    "test-model-quality": {
                        "role": "test",
                        "agents": ["test_agent"],
                        "description": "High quality test model",
                        "performance": "high_quality"
                    }
                }
            }
        },
        "routing": {
            "test_agent": {
                "primary": "test-model-fast",
                "secondary": "test-model-quality",
                "fallback": "test-model-fast"
            }
        },
        "load_balancing": {
            "strategy": "round_robin",
            "health_weight": 0.4,
            "performance_weight": 0.3,
            "availability_weight": 0.3
        },
        "model_analytics": {
            "track_performance": True,
            "track_quality_scores": True
        },
        "performance_settings": {
            "request_timeout": 30,
            "retry_attempts": 2,
            "backoff_factor": 1.5
        },
        "quality_thresholds": {
            "minimum_confidence": 0.7,
            "context_relevance": 0.8
        }
    }


@pytest.fixture
def mock_successful_ollama_response():
    """Mock a successful Ollama API response."""
    return {
        "response": """
        Here's a complete solution:

        ```python
        def example_function():
            return "Hello, World!"
        ```

        This function demonstrates a simple implementation.
        Next steps: Add error handling and tests.
        Dependencies: python >= 3.8
        """
    }


@pytest.fixture
def mock_task_context():
    """Create a mock task context for testing."""
    from ai_coding_agent.orchestrator import TaskContext
    return TaskContext(
        project_id="test_project_123",
        user_id="test_user_456",
        session_id="test_session_789",
        ltkb_context="Sample LTKB knowledge context",
        stpm_context="Sample project memory context"
    )


@pytest.fixture
def orchestrator_test_tasks():
    """Sample tasks for testing orchestrator functionality."""
    return {
        "simple": "Fix a typo in the documentation",
        "moderate": "Create a React component for user authentication",
        "complex": "Design a microservices architecture with authentication and authorization",
        "expert": "Implement a distributed system with eventual consistency, CQRS, and event sourcing patterns"
    }
