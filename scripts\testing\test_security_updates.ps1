# Test Security Updates Script
# This script rebuilds containers with security updates and tests functionality

param(
    [switch]$CleanBuild = $false,
    [switch]$SkipTests = $false,
    [switch]$Verbose = $false
)

Write-Host "AI Coding Agent - Security Updates Test Script" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

# Function to log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Check if Docker is running
Write-Log "Checking Docker status..."
try {
    docker version | Out-Null
    Write-Log "Docker is running" "SUCCESS"
} catch {
    Write-Log "Docker is not running. Please start Docker Desktop." "ERROR"
    exit 1
}

# Clean build if requested
if ($CleanBuild) {
    Write-Log "Performing clean build..."
    Write-Log "Stopping existing containers..."
    docker-compose down

    Write-Log "Removing old images..."
    docker image prune -f
    docker system prune -f

    Write-Log "Pulling latest base images..."
    docker pull python:3.13-slim
    docker pull redis:7.4-alpine
    docker pull pgvector/pgvector:pg17
    docker pull nginx:alpine
}

# Build containers with security updates
Write-Log "Building containers with security updates..."
try {
    docker-compose build --no-cache backend
    Write-Log "Backend container built successfully" "SUCCESS"
} catch {
    Write-Log "Failed to build backend container" "ERROR"
    exit 1
}

# Start services
Write-Log "Starting services..."
try {
    docker-compose up -d
    Write-Log "Services started" "SUCCESS"
} catch {
    Write-Log "Failed to start services" "ERROR"
    exit 1
}

# Wait for services to be ready
Write-Log "Waiting for services to be ready..."
Start-Sleep -Seconds 30

# Test service health
Write-Log "Testing service health..."

# Test backend health
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/health" -Method GET -TimeoutSec 10
    if ($response) {
        Write-Log "Backend health check passed" "SUCCESS"
    }
} catch {
    Write-Log "Backend health check failed: $_" "ERROR"
}

# Test Redis connection
try {
    $redisTest = docker exec ai-coding-agent-redis redis-cli ping
    if ($redisTest -eq "PONG") {
        Write-Log "Redis connection test passed" "SUCCESS"
    }
} catch {
    Write-Log "Redis connection test failed: $_" "ERROR"
}

# Test PostgreSQL connection
try {
    $pgTest = docker exec ai-coding-agent-postgres pg_isready -U agent -d ai_coding_agent
    if ($pgTest -like "*accepting connections*") {
        Write-Log "PostgreSQL connection test passed" "SUCCESS"
    }
} catch {
    Write-Log "PostgreSQL connection test failed: $_" "ERROR"
}

# Test NGINX
try {
    $nginxTest = Invoke-RestMethod -Uri "http://localhost:80/health" -Method GET -TimeoutSec 10
    if ($nginxTest) {
        Write-Log "NGINX health check passed" "SUCCESS"
    }
} catch {
    Write-Log "NGINX health check failed: $_" "WARN"
    Write-Log "This is expected if NGINX is not fully configured yet" "INFO"
}

if (-not $SkipTests) {
    # Test container management API
    Write-Log "Testing container management API..."
    try {
        $containerHealth = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/containers/health" -Method GET -TimeoutSec 10
        if ($containerHealth.status -eq "healthy") {
            Write-Log "Container management service is healthy" "SUCCESS"
        }
    } catch {
        Write-Log "Container management API test failed: $_" "WARN"
        Write-Log "This may be expected if authentication is required" "INFO"
    }
}

# Security scan (if available)
Write-Log "Checking for security vulnerabilities..."
try {
    Write-Log "Scanning backend image..."
    docker scout cves ai-coding-agent-backend 2>$null
    Write-Log "Security scan completed - check output above" "INFO"
} catch {
    Write-Log "Docker Scout not available - skipping security scan" "WARN"
}

# Show container status
Write-Log "Container status:"
docker-compose ps

Write-Log "Security updates test completed!" "SUCCESS"
Write-Log "Next steps:" "INFO"
Write-Log "1. Review any security scan results above" "INFO"
Write-Log "2. Test container provisioning functionality" "INFO"
Write-Log "3. Test NGINX subdomain routing" "INFO"
Write-Log "4. Verify user isolation and security" "INFO"

if ($Verbose) {
    Write-Log "Detailed container information:" "INFO"
    docker-compose logs --tail=50
}
