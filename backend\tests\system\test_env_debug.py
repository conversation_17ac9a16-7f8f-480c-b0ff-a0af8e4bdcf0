#!/usr/bin/env python3
"""Debug the .env parsing issue."""

import os
from pathlib import Path
from pydantic import Field
from pydantic_settings import BaseSettings

class TestSettings(BaseSettings):
    """Test settings to debug the CORS origins issue."""

    cors_origins: list[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="CORS origins",
    )

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8"}

def main():
    """Test the settings loading."""
    try:
        print("Current working directory:", os.getcwd())
        print("Checking if .env exists:", Path(".env").exists())

        if Path(".env").exists():
            print("\n.env file contents (first 20 lines):")
            with open(".env", "r", encoding="utf-8") as f:
                for i, line in enumerate(f, 1):
                    if i <= 20:
                        print(f"{i:2d}: {repr(line.rstrip())}")
                    else:
                        break

        print("\nTrying to load settings...")
        settings = TestSettings()
        print("Success! CORS origins:", settings.cors_origins)

    except Exception as e:
        print(f"Error: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
