import React from 'react';
import { Link } from 'react-router-dom';
import { useHealthCheck } from '../hooks/useQueries';

const HomePage: React.FC = () => {
  const { data: healthData, isLoading: healthLoading, error: healthError } = useHealthCheck();

  return (
    <div className="max-w-4xl mx-auto text-center">
      {/* System Status Banner */}
      <div className="mb-6">
        <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
          healthLoading ? 'bg-yellow-100 text-yellow-800' :
          healthError ? 'bg-red-100 text-red-800' :
          healthData ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          <div className={`w-2 h-2 rounded-full mr-2 ${
            healthLoading ? 'bg-yellow-400 animate-pulse' :
            healthError ? 'bg-red-400' :
            healthData ? 'bg-green-400' : 'bg-gray-400'
          }`}></div>
          System Status: {
            healthLoading ? 'Checking...' :
            healthError ? 'Offline' :
            healthData ? 'Online' : 'Unknown'
          }
        </div>
      </div>
      <div className="mb-12">
        <h1 className="text-5xl font-bold text-gray-900 mb-6">
          AI Coding Agent
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Your intelligent coding companion powered by advanced AI technology
        </p>
        <div className="flex justify-center space-x-4">
          <Link
            to="/register"
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg text-lg font-medium"
          >
            Get Started
          </Link>
          <Link
            to="/login"
            className="border border-primary-600 text-primary-600 hover:bg-primary-50 px-8 py-3 rounded-lg text-lg font-medium"
          >
            Sign In
          </Link>
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-8 mb-12">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-primary-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">User Management</h3>
          <p className="text-gray-600">
            Secure authentication and user profile management
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-primary-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">AI Code Analysis</h3>
          <p className="text-gray-600">
            Advanced AI agents for code review and optimization
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-primary-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">Project Dashboard</h3>
          <p className="text-gray-600">
            Comprehensive project management and monitoring
          </p>
        </div>
      </div>

      <div className="bg-primary-50 p-8 rounded-lg">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Ready to enhance your coding workflow?
        </h2>
        <p className="text-lg text-gray-600 mb-6">
          Join thousands of developers who are already using AI to write better code faster.
        </p>
        <Link
          to="/register"
          className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg text-lg font-medium inline-block"
        >
          Start Your Free Trial
        </Link>
      </div>
    </div>
  );
};

export default HomePage;
