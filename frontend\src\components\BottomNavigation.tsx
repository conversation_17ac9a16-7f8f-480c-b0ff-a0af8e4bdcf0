import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, LayoutDashboard, User, Settings, Palette } from 'lucide-react';
import { useAuth } from '../contexts/AppContext';
import { useIsTouchDevice } from '../utils/responsive';
import { hapticFeedback, touchFriendlyProps } from '../utils/touch';
import { ariaUtils } from '../utils/accessibility';

/**
 * Bottom Navigation Component for mobile devices
 * Provides quick access to main app sections
 */

interface NavItem {
  label: string;
  path: string;
  icon: React.ComponentType<any>;
  requireAuth?: boolean;
  badge?: number;
}

interface BottomNavigationProps {
  className?: string;
  showLabels?: boolean;
}

const navItems: NavItem[] = [
  {
    label: 'Home',
    path: '/',
    icon: Home,
    requireAuth: false,
  },
  {
    label: 'Dashboard',
    path: '/dashboard',
    icon: LayoutDashboard,
    requireAuth: true,
  },
  {
    label: 'Profile',
    path: '/profile',
    icon: User,
    requireAuth: true,
  },
  {
    label: 'Settings',
    path: '/settings',
    icon: Settings,
    requireAuth: true,
  },
  {
    label: 'Showcase',
    path: '/showcase',
    icon: Palette,
    requireAuth: false,
  },
];

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  className = '',
  showLabels = true,
}) => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const isTouch = useIsTouchDevice();

  const filteredNavItems = navItems.filter(item =>
    !item.requireAuth || isAuthenticated
  );

  const handleNavClick = () => {
    if (isTouch) hapticFeedback.light();
  };

  return (
    <nav
      className={`
        fixed bottom-0 left-0 right-0 z-50
        bg-white dark:bg-gray-900
        border-t border-gray-200 dark:border-gray-700
        shadow-lg lg:hidden
        ${className}
      `}
      role="navigation"
      aria-label="Bottom navigation"
    >
      <div className="flex">
        {filteredNavItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          const itemId = `bottom-nav-${item.path.replace('/', '')}`;

          return (
            <Link
              key={item.path}
              to={item.path}
              onClick={handleNavClick}
              className={`
                flex-1 flex flex-col items-center justify-center
                py-2 px-1 relative
                ${isActive
                  ? 'text-blue-600 dark:text-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }
                focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500
                transition-colors duration-150
                ${touchFriendlyProps.touchFeedback}
              `}
              style={{
                ...touchFriendlyProps.minTouchTarget,
                minHeight: '60px', // Slightly taller for bottom nav
              }}
              aria-label={item.label}
              aria-current={isActive ? 'page' : undefined}
              id={itemId}
              {...ariaUtils.button}
            >
              {/* Active indicator */}
              {isActive && (
                <div
                  className="
                    absolute top-0 left-1/2 transform -translate-x-1/2
                    w-6 h-0.5 bg-blue-600 dark:bg-blue-400 rounded-full
                  "
                  aria-hidden="true"
                />
              )}

              {/* Icon container */}
              <div className="relative">
                <Icon
                  className={`
                    w-6 h-6 transition-transform duration-150
                    ${isActive ? 'scale-110' : 'scale-100'}
                  `}
                  aria-hidden="true"
                />

                {/* Badge */}
                {item.badge && item.badge > 0 && (
                  <span
                    className="
                      absolute -top-2 -right-2
                      bg-red-500 text-white text-xs
                      rounded-full w-5 h-5
                      flex items-center justify-center
                      font-medium
                    "
                    aria-label={`${item.badge} notifications`}
                  >
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>

              {/* Label */}
              {showLabels && (
                <span
                  className={`
                    text-xs mt-1 font-medium leading-none
                    ${isActive ? 'opacity-100' : 'opacity-80'}
                    transition-opacity duration-150
                  `}
                >
                  {item.label}
                </span>
              )}
            </Link>
          );
        })}
      </div>

      {/* Safe area padding for devices with home indicator */}
      <div className="h-safe pb-safe" />
    </nav>
  );
};

export default BottomNavigation;
