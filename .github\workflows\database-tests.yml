# GitHub Actions Workflow for Database Testing
name: Database Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-databases:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Setup test environment
      run: |
        python config_manager.py switch testing
        python dev_setup.py

    - name: Run database tests
      run: |
        python test_all_databases.py

    - name: Check migrations
      run: |
        python migration_manager.py status
        python migration_manager.py pending local
