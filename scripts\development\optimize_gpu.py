"""
GPU Optimization Script for NVIDIA Quadro P1000

This script configures Ollama to use the full 4GB VRAM of the NVIDIA Quadro P1000
for maximum performance with our 5 AI models.
"""

import subprocess
import os
import json
import asyncio
import aiohttp
import time


def check_gpu_status():
    """Check NVIDIA GPU status and VRAM availability."""
    print("🔍 Checking NVIDIA GPU status...")

    try:
        # Check nvidia-smi
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected!")
            print(result.stdout)
        else:
            print("❌ nvidia-smi failed")
            return False

        # Check CUDA availability
        try:
            result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ CUDA toolkit detected!")
                print(result.stdout)
            else:
                print("⚠️ CUDA toolkit not found - may impact performance")
        except FileNotFoundError:
            print("⚠️ nvcc not found - CUDA toolkit may not be installed")

        return True

    except FileNotFoundError:
        print("❌ nvidia-smi not found - NVIDIA drivers may not be installed")
        return False


def configure_ollama_gpu():
    """Configure Ollama environment variables for GPU optimization."""
    print("\n🚀 Configuring Ollama for GPU acceleration...")

    # Set environment variables for optimal GPU usage
    gpu_config = {
        'OLLAMA_GPU_LAYERS': '99',  # Use GPU for all layers
        'OLLAMA_VRAM_BUDGET': '3584',  # Use 3.5GB of 4GB VRAM (leave 512MB for system)
        'OLLAMA_CUDA_VISIBLE_DEVICES': '0',  # Use first GPU
        'OLLAMA_NUM_PARALLEL': '2',  # Allow 2 parallel requests max
        'OLLAMA_MAX_LOADED_MODELS': '2',  # Keep max 2 models loaded
        'OLLAMA_FLASH_ATTENTION': '1',  # Enable flash attention for efficiency
        'OLLAMA_CONCURRENT_REQUESTS': '2',  # Limit concurrent requests
    }

    print("Setting GPU optimization environment variables:")
    for key, value in gpu_config.items():
        os.environ[key] = value
        print(f"  {key}={value}")

    # Create a batch file for permanent configuration (Windows)
    batch_content = """@echo off
echo Setting Ollama GPU optimization for NVIDIA Quadro P1000...
set OLLAMA_GPU_LAYERS=99
set OLLAMA_VRAM_BUDGET=3584
set OLLAMA_CUDA_VISIBLE_DEVICES=0
set OLLAMA_NUM_PARALLEL=2
set OLLAMA_MAX_LOADED_MODELS=2
set OLLAMA_FLASH_ATTENTION=1
set OLLAMA_CONCURRENT_REQUESTS=2

echo GPU optimization configured!
echo Starting Ollama with GPU acceleration...
ollama serve
"""

    with open('start_ollama_gpu.bat', 'w') as f:
        f.write(batch_content)

    print("✅ Created start_ollama_gpu.bat for permanent GPU configuration")


def restart_ollama_with_gpu():
    """Restart Ollama with GPU configuration."""
    print("\n🔄 Restarting Ollama with GPU configuration...")

    try:
        # Stop existing Ollama process
        print("Stopping Ollama...")
        subprocess.run(['taskkill', '/F', '/IM', 'ollama.exe'],
                      capture_output=True, check=False)
        time.sleep(3)

        # Start Ollama with GPU config
        print("Starting Ollama with GPU acceleration...")
        process = subprocess.Popen(['start_ollama_gpu.bat'], shell=True)
        time.sleep(5)  # Give it time to start

        print("✅ Ollama restarted with GPU configuration")
        return True

    except Exception as e:
        print(f"❌ Failed to restart Ollama: {e}")
        return False


async def test_gpu_performance():
    """Test performance of models with GPU acceleration."""
    print("\n⚡ Testing GPU-accelerated model performance...")

    # Required models for our AI agents
    test_models = [
        'yi-coder:1.5b',
        'mistral:7b-instruct-q4_0',
        'qwen2.5:3b',
        'starcoder2:3b',
        'deepseek-coder:6.7b-instruct-q4_0'
    ]

    results = {}

    for model in test_models:
        print(f"\n🧪 Testing {model}...")

        start_time = time.time()

        try:
            payload = {
                "model": model,
                "prompt": "Hello! Generate a simple Python function.",
                "stream": False,
                "options": {
                    "num_predict": 50,  # Short response for speed test
                    "temperature": 0.7
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'http://localhost:11434/api/generate',
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        elapsed = time.time() - start_time

                        results[model] = {
                            'status': 'success',
                            'response_time': elapsed,
                            'response_length': len(result.get('response', '')),
                            'total_duration': result.get('total_duration', 0) / 1e9,  # Convert to seconds
                            'load_duration': result.get('load_duration', 0) / 1e9,
                            'prompt_eval_duration': result.get('prompt_eval_duration', 0) / 1e9,
                            'eval_duration': result.get('eval_duration', 0) / 1e9
                        }

                        print(f"  ✅ Success: {elapsed:.2f}s")
                        print(f"  📝 Response length: {len(result.get('response', ''))} chars")
                        print(f"  ⚡ Load time: {result.get('load_duration', 0) / 1e9:.2f}s")
                        print(f"  🧠 Eval time: {result.get('eval_duration', 0) / 1e9:.2f}s")

                    else:
                        results[model] = {
                            'status': 'error',
                            'error': f'HTTP {response.status}',
                            'response_time': time.time() - start_time
                        }
                        print(f"  ❌ Failed: HTTP {response.status}")

        except Exception as e:
            results[model] = {
                'status': 'error',
                'error': str(e),
                'response_time': time.time() - start_time
            }
            print(f"  ❌ Failed: {e}")

    return results


def analyze_gpu_performance(results):
    """Analyze GPU performance results."""
    print("\n📊 GPU Performance Analysis:")
    print("=" * 60)

    total_models = len(results)
    successful_models = sum(1 for r in results.values() if r['status'] == 'success')

    print(f"Models tested: {total_models}")
    print(f"Successful: {successful_models}")
    print(f"Failed: {total_models - successful_models}")

    if successful_models > 0:
        successful_results = {k: v for k, v in results.items() if v['status'] == 'success'}

        avg_response_time = sum(r['response_time'] for r in successful_results.values()) / successful_models
        avg_load_time = sum(r.get('load_duration', 0) for r in successful_results.values()) / successful_models
        avg_eval_time = sum(r.get('eval_duration', 0) for r in successful_results.values()) / successful_models

        print(f"\n⚡ Average Performance (GPU-accelerated):")
        print(f"  Response time: {avg_response_time:.2f}s")
        print(f"  Load time: {avg_load_time:.2f}s")
        print(f"  Evaluation time: {avg_eval_time:.2f}s")

        print(f"\n🏆 Model Performance Ranking:")
        sorted_models = sorted(successful_results.items(),
                             key=lambda x: x[1]['response_time'])

        for i, (model, result) in enumerate(sorted_models, 1):
            print(f"  {i}. {model}: {result['response_time']:.2f}s")

        # Performance improvement estimation
        print(f"\n📈 Expected Performance Improvements with GPU:")
        print(f"  - Load times should be 2-5x faster")
        print(f"  - Evaluation times should be 3-10x faster")
        print(f"  - Overall response times should be 50-80% faster")

    # Check for issues
    failed_models = {k: v for k, v in results.items() if v['status'] == 'error'}
    if failed_models:
        print(f"\n❌ Failed Models:")
        for model, result in failed_models.items():
            print(f"  {model}: {result['error']}")


def optimize_model_quantization():
    """Provide recommendations for model quantization on 4GB VRAM."""
    print("\n🎛️ Model Quantization Recommendations for 4GB VRAM:")
    print("=" * 60)

    recommendations = {
        'yi-coder:1.5b': {
            'vram_usage': '~1.2GB',
            'recommendation': 'Use as-is (Q4_0 quantization is optimal)',
            'priority': 'High - fast inference'
        },
        'mistral:7b-instruct-q4_0': {
            'vram_usage': '~3.2GB',
            'recommendation': 'Keep Q4_0 quantization, primary model',
            'priority': 'High - versatile model'
        },
        'qwen2.5:3b': {
            'vram_usage': '~2.1GB',
            'recommendation': 'Use as-is, good balance',
            'priority': 'Medium - specialized tasks'
        },
        'starcoder2:3b': {
            'vram_usage': '~2.0GB',
            'recommendation': 'Use as-is, efficient for code generation',
            'priority': 'Medium - code tasks'
        },
        'deepseek-coder:6.7b-instruct-q4_0': {
            'vram_usage': '~3.8GB',
            'recommendation': 'Consider Q3_K_M for lower VRAM usage',
            'priority': 'Low - complex tasks only'
        }
    }

    print("Model recommendations for optimal 4GB VRAM usage:")
    for model, info in recommendations.items():
        print(f"\n{model}:")
        print(f"  VRAM usage: {info['vram_usage']}")
        print(f"  Recommendation: {info['recommendation']}")
        print(f"  Priority: {info['priority']}")

    print(f"\n💡 Strategy for 4GB VRAM:")
    print(f"  - Load max 2 models simultaneously")
    print(f"  - Keep yi-coder + mistral as primary pair")
    print(f"  - Swap other models as needed")
    print(f"  - Use OLLAMA_MAX_LOADED_MODELS=2")


async def main():
    """Main optimization workflow."""
    print("🚀 NVIDIA Quadro P1000 GPU Optimization for AI Coding Agent")
    print("=" * 70)

    # Step 1: Check GPU status
    if not check_gpu_status():
        print("❌ GPU not properly configured. Please install NVIDIA drivers.")
        return

    # Step 2: Configure Ollama for GPU
    configure_ollama_gpu()

    # Step 3: Restart Ollama with GPU configuration
    if not restart_ollama_with_gpu():
        print("❌ Failed to restart Ollama. Please run start_ollama_gpu.bat manually.")
        return

    # Step 4: Wait for Ollama to fully start
    print("⏳ Waiting for Ollama to fully initialize...")
    await asyncio.sleep(10)

    # Step 5: Test GPU performance
    results = await test_gpu_performance()

    # Step 6: Analyze results
    analyze_gpu_performance(results)

    # Step 7: Provide optimization recommendations
    optimize_model_quantization()

    print(f"\n✅ GPU optimization complete!")
    print(f"📝 To permanently use GPU acceleration, run: start_ollama_gpu.bat")
    print(f"🔧 Monitor GPU usage with: nvidia-smi")


if __name__ == "__main__":
    asyncio.run(main())
