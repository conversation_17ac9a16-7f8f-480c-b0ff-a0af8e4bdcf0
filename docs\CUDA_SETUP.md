# AI Coding Agent - CUDA Configuration

## GPU Optimization Settings

This configuration file contains GPU-specific optimizations for the AI Coding Agent multi-agent system.

### Recommended Ollama GPU Settings

Add these to your Ollama configuration or environment:

```bash
# Enable GPU acceleration
export OLLAMA_GPU=1

# Set GPU memory allocation (adjust for your Quadro P1000)
export OLLAMA_GPU_MEMORY="4GB"

# Enable GPU compute optimization
export OLLAMA_COMPUTE_CAPABILITY="6.1"

# Optimize for multi-model loading
export OLLAMA_MAX_LOADED_MODELS=3

# Set number of GPU layers (adjust per model)
export OLLAMA_NUM_GPU_LAYERS=32
```

### Model-Specific GPU Configurations

#### For 3B Models (yi-coder:1.5b, starcoder2:3b, llama3.2:3b)
- **GPU Layers**: 32 (full GPU acceleration)
- **Context Window**: Optimize for 4K-8K tokens
- **Batch Size**: 8-16 for optimal throughput

#### For 6.7B Models (deepseek-coder:6.7b-instruct)
- **GPU Layers**: 28-30 (hybrid GPU/CPU if memory limited)
- **Context Window**: Optimize for 8K-16K tokens
- **Batch Size**: 4-8 for stability

#### For 7B Models (mistral:7b-instruct)
- **GPU Layers**: 24-28 (monitor memory usage)
- **Context Window**: Standard 4K tokens
- **Batch Size**: 4-6 for memory efficiency

### Performance Optimization

#### Cold Start Optimization
```json
{
  "cold_start_optimization": {
    "preload_priority_models": [
      "starcoder2:3b",
      "yi-coder:1.5b",
      "llama3.2:3b"
    ],
    "warmup_on_startup": true,
    "parallel_warmup": false,
    "warmup_prompt": "Hello"
  }
}
```

#### GPU Memory Management
```json
{
  "gpu_memory_management": {
    "enable_memory_pooling": true,
    "max_memory_per_model": "1.5GB",
    "memory_growth": true,
    "allow_memory_growth": true,
    "gpu_memory_fraction": 0.8
  }
}
```

### Monitoring Configuration

#### GPU Metrics
- **GPU Utilization**: Target 70-90% during inference
- **Memory Usage**: Keep under 80% of available VRAM
- **Temperature**: Monitor for thermal throttling
- **Response Times**: <5s for 3B models, <15s for 6.7B+ models

#### Performance Alerts
```json
{
  "performance_alerts": {
    "slow_response_threshold": 30,
    "memory_usage_threshold": 0.9,
    "gpu_utilization_threshold": 0.95,
    "temperature_threshold": 80
  }
}
```

### Troubleshooting GPU Issues

#### Common Problems and Solutions

1. **Models falling back to CPU**
   - Check: `nvidia-smi` shows GPU activity during inference
   - Solution: Verify CUDA_VISIBLE_DEVICES and Ollama GPU settings

2. **Slow cold starts (>60s)**
   - Check: GPU driver version compatibility
   - Solution: Implement model preloading and warmup

3. **Memory errors with larger models**
   - Check: Available VRAM vs model requirements
   - Solution: Reduce GPU layers or use model quantization

4. **Inconsistent performance**
   - Check: GPU thermal throttling
   - Solution: Improve cooling or reduce concurrent models

#### Diagnostic Commands

```bash
# Check GPU status
nvidia-smi

# Monitor GPU usage during inference
watch -n 1 nvidia-smi

# Check Ollama GPU detection
ollama info

# Test model loading time
time ollama run llama3.2:3b "test"

# Monitor system resources
htop
```

### Environment Variables

Create a `.env` file with these GPU optimizations:

```env
# GPU Configuration
OLLAMA_GPU=1
OLLAMA_GPU_MEMORY=4GB
OLLAMA_MAX_LOADED_MODELS=3
OLLAMA_NUM_GPU_LAYERS=32

# Performance Tuning
OLLAMA_FLASH_ATTENTION=1
OLLAMA_HOST=0.0.0.0:11434
OLLAMA_KEEP_ALIVE=5m

# Memory Management
OLLAMA_MAX_QUEUE=512
OLLAMA_RUNNERS_DIR=/tmp/ollama_runners
```

### Conda Environment with GPU Support

The `environment.yml` file includes:
- CUDA toolkit via conda-forge
- PyTorch with CUDA support
- GPU-accelerated libraries
- Monitoring tools

### Testing GPU Setup

Use the provided scripts:

1. **setup_cuda.py** - Complete system verification
2. **verify_models.py** - Model warmup and testing
3. **benchmark_models.py** - Performance monitoring

Expected performance with proper GPU setup:
- **yi-coder:1.5b**: 1-3 seconds
- **starcoder2:3b**: 2-5 seconds
- **llama3.2:3b**: 3-8 seconds
- **qwen2.5:3b**: 4-10 seconds
- **deepseek-coder:6.7b**: 8-20 seconds

### Integration with Orchestrator

The enhanced orchestrator automatically:
- Detects GPU availability
- Adjusts timeouts for GPU warmup
- Monitors performance metrics
- Provides GPU-specific health checks
- Optimizes load balancing for GPU memory

This configuration ensures your Quadro P1000 is fully utilized for optimal multi-agent performance.
