import React, { useState, useEffect } from 'react';
import {
  Settings,
  Cloud,
  HardDrive,
  Check,
  X,
  RefreshCw,
  Save,
  AlertCircle,
  Info
} from 'lucide-react';

interface ModelProvider {
  name: string;
  type: 'local' | 'cloud';
  host?: string;
  api_key?: string;
  models: string[];
  status: 'online' | 'offline' | 'unknown' | 'error';
}

interface AgentModelConfig {
  agent_name: string;
  primary_model: string;
  secondary_model?: string;
  fallback_model?: string;
  provider: string;
}

interface CloudProviderConfig {
  provider_name: string;
  api_key: string;
  base_url?: string;
  enabled: boolean;
}

const ModelConfigAdmin: React.FC = () => {
  const [providers, setProviders] = useState<Record<string, ModelProvider>>({});
  const [agentConfigs, setAgentConfigs] = useState<AgentModelConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [showCloudConfig, setShowCloudConfig] = useState(false);
  const [cloudConfig, setCloudConfig] = useState<CloudProviderConfig>({
    provider_name: 'openai',
    api_key: '',
    enabled: true
  });

  const agentDescriptions = {
    architect: 'Project planning, orchestration, and high-level decision making',
    frontend: 'UI/UX development, React components, and frontend logic',
    backend: 'API development, business logic, and server-side code',
    shell: 'Command execution, system tasks, and deployment scripts',
    debug: 'Issue detection, debugging, and error analysis',
    test: 'Unit testing, test strategy, and quality assurance'
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load available models
      const providersResponse = await fetch('/api/v1/admin/models/available');
      const providersData = await providersResponse.json();
      setProviders(providersData);

      // Load current configuration
      const configResponse = await fetch('/api/v1/admin/models/current-config');
      const configData = await configResponse.json();
      setAgentConfigs(configData.agent_configs || []);

    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to load configuration' });
    } finally {
      setLoading(false);
    }
  };

  const updateAgentModel = (agentName: string, field: string, value: string) => {
    setAgentConfigs(prev =>
      prev.map(config =>
        config.agent_name === agentName
          ? { ...config, [field]: value }
          : config
      )
    );
  };

  const saveConfiguration = async () => {
    try {
      setSaving(true);

      const response = await fetch('/api/v1/admin/models/update-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_configs: agentConfigs,
          provider_configs: {}
        })
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Configuration saved successfully!' });
      } else {
        throw new Error('Failed to save configuration');
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save configuration' });
    } finally {
      setSaving(false);
    }
  };

  const configureCloudProvider = async () => {
    try {
      const response = await fetch('/api/v1/admin/providers/cloud/configure', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(cloudConfig)
      });

      if (response.ok) {
        setMessage({ type: 'success', text: `${cloudConfig.provider_name} configured successfully!` });
        setShowCloudConfig(false);
        loadData(); // Reload to get updated provider status
      } else {
        throw new Error('Failed to configure provider');
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to configure cloud provider' });
    }
  };

  const testModelConnection = async (modelName: string, provider: string) => {
    try {
      const response = await fetch(`/api/v1/admin/models/test-connection?model_name=${modelName}&provider=${provider}`, {
        method: 'POST'
      });
      const result = await response.json();

      setMessage({
        type: result.success ? 'success' : 'error',
        text: result.message
      });
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to test model connection' });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <Check className="text-green-500" />;
      case 'offline': return <X className="text-red-500" />;
      case 'error': return <AlertCircle className="text-red-500" />;
      default: return <Info className="text-yellow-500" />;
    }
  };

  const getProviderIcon = (type: string) => {
    return type === 'local' ?
      <HardDrive className="text-blue-500" /> :
      <Cloud className="text-purple-500" />;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="animate-spin text-2xl text-blue-500" />
        <span className="ml-2">Loading configuration...</span>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Settings className="mr-2" />
            AI Model Configuration
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Configure which AI models to use for each agent role
          </p>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => setShowCloudConfig(true)}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center"
          >
            <Cloud className="mr-2" />
            Add Cloud Provider
          </button>

          <button
            onClick={loadData}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center"
          >
            <RefreshCw className="mr-2" />
            Refresh
          </button>

          <button
            onClick={saveConfiguration}
            disabled={saving}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
          >
            <Save className="mr-2" />
            {saving ? 'Saving...' : 'Save Configuration'}
          </button>
        </div>
      </div>

      {/* Status Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {message.text}
          <button
            onClick={() => setMessage(null)}
            className="ml-2 text-sm underline"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Providers Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(providers).map(([key, provider]) => (
          <div key={key} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                {getProviderIcon(provider.type)}
                <h3 className="ml-2 font-semibold">{provider.name}</h3>
              </div>
              {getStatusIcon(provider.status)}
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {provider.type === 'local' ? 'Local Models' : 'Cloud API'}
            </p>

            <div className="text-sm">
              <span className="font-medium">{provider.models.length}</span> models available
            </div>

            {provider.type === 'local' && provider.host && (
              <div className="text-xs text-gray-500 mt-1">
                {provider.host}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Agent Configuration */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold">Agent Model Assignment</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Assign specific models to each AI agent role
          </p>
        </div>

        <div className="p-4 space-y-4">
          {agentConfigs.map((config) => (
            <div key={config.agent_name} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-medium capitalize">{config.agent_name} Agent</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {agentDescriptions[config.agent_name as keyof typeof agentDescriptions]}
                  </p>
                </div>

                <button
                  onClick={() => testModelConnection(config.primary_model, config.provider)}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Test Connection
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Primary Model</label>
                  <select
                    value={config.primary_model}
                    onChange={(e) => updateAgentModel(config.agent_name, 'primary_model', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                  >
                    <option value="">Select model...</option>
                    {Object.entries(providers).map(([providerKey, provider]) =>
                      provider.models.map(model => (
                        <option key={`${providerKey}-${model}`} value={model}>
                          {model} ({provider.name})
                        </option>
                      ))
                    )}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Secondary Model</label>
                  <select
                    value={config.secondary_model || ''}
                    onChange={(e) => updateAgentModel(config.agent_name, 'secondary_model', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                  >
                    <option value="">None</option>
                    {Object.entries(providers).map(([providerKey, provider]) =>
                      provider.models.map(model => (
                        <option key={`${providerKey}-${model}`} value={model}>
                          {model} ({provider.name})
                        </option>
                      ))
                    )}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Fallback Model</label>
                  <select
                    value={config.fallback_model || ''}
                    onChange={(e) => updateAgentModel(config.agent_name, 'fallback_model', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                  >
                    <option value="">None</option>
                    {Object.entries(providers).map(([providerKey, provider]) =>
                      provider.models.map(model => (
                        <option key={`${providerKey}-${model}`} value={model}>
                          {model} ({provider.name})
                        </option>
                      ))
                    )}
                  </select>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Cloud Provider Configuration Modal */}
      {showCloudConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Configure Cloud Provider</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Provider</label>
                <select
                  value={cloudConfig.provider_name}
                  onChange={(e) => setCloudConfig(prev => ({ ...prev, provider_name: e.target.value }))}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                >
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="google">Google</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">API Key</label>
                <input
                  type="password"
                  value={cloudConfig.api_key}
                  onChange={(e) => setCloudConfig(prev => ({ ...prev, api_key: e.target.value }))}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                  placeholder="Enter API key..."
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={cloudConfig.enabled}
                  onChange={(e) => setCloudConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                  className="mr-2"
                />
                <label className="text-sm">Enable this provider</label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCloudConfig(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={configureCloudProvider}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Configure
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelConfigAdmin;
