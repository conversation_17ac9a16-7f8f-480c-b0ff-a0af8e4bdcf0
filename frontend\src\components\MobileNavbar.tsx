import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Menu, ArrowLeft, Search, MoreVertical } from 'lucide-react';
import { useAuth } from '../contexts/AppContext';
import { useIsTouchDevice } from '../utils/responsive';
import { hapticFeedback, touchFriendlyProps } from '../utils/touch';
import { ariaUtils } from '../utils/accessibility';
import ThemeToggle from './ThemeToggle';

interface MobileNavbarProps {
  onMenuClick?: () => void;
  showBackButton?: boolean;
  title?: string;
  actions?: React.ReactNode;
  searchEnabled?: boolean;
  onSearchClick?: () => void;
}

const MobileNavbar: React.FC<MobileNavbarProps> = ({
  onMenuClick,
  showBackButton = false,
  title,
  actions,
  searchEnabled = false,
  onSearchClick,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user, logout } = useAuth();
  const isTouch = useIsTouchDevice();
  const [showDropdown, setShowDropdown] = React.useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    if (isTouch) hapticFeedback.medium();
    logout();
    navigate('/');
  };

  const handleBackClick = () => {
    if (isTouch) hapticFeedback.light();
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  const handleMenuClick = () => {
    if (isTouch) hapticFeedback.light();
    onMenuClick?.();
  };

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get page title based on route
  const getPageTitle = () => {
    if (title) return title;

    switch (location.pathname) {
      case '/':
        return 'AI Coding Agent';
      case '/dashboard':
        return 'Dashboard';
      case '/profile':
        return 'Profile';
      case '/settings':
        return 'Settings';
      case '/login':
        return 'Login';
      case '/register':
        return 'Register';
      case '/showcase':
        return 'Component Showcase';
      default:
        return 'AI Coding Agent';
    }
  };

  return (
    <nav
      className="
        bg-white dark:bg-gray-900 shadow-lg border-b border-gray-200 dark:border-gray-700
        sticky top-0 z-40 lg:hidden
      "
      role="banner"
      aria-label="Main navigation"
    >
      <div className="px-4 sm:px-6">
        <div className="flex items-center justify-between h-16">
          {/* Left section */}
          <div className="flex items-center space-x-2">
            {showBackButton ? (
              <button
                onClick={handleBackClick}
                className={`
                  p-2 rounded-md text-gray-700 dark:text-gray-300
                  hover:bg-gray-100 dark:hover:bg-gray-800
                  focus:outline-none focus:ring-2 focus:ring-blue-500
                  ${touchFriendlyProps.touchFeedback}
                `}
                style={touchFriendlyProps.minTouchTarget}
                aria-label="Go back"
                {...ariaUtils.button}
              >
                <ArrowLeft className="w-6 h-6" aria-hidden="true" />
              </button>
            ) : onMenuClick ? (
              <button
                onClick={handleMenuClick}
                className={`
                  p-2 rounded-md text-gray-700 dark:text-gray-300
                  hover:bg-gray-100 dark:hover:bg-gray-800
                  focus:outline-none focus:ring-2 focus:ring-blue-500
                  ${touchFriendlyProps.touchFeedback}
                `}
                style={touchFriendlyProps.minTouchTarget}
                aria-label="Open navigation menu"
                aria-expanded={false}
                {...ariaUtils.button}
              >
                <Menu className="w-6 h-6" aria-hidden="true" />
              </button>
            ) : null}

            {/* Title */}
            <h1 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white truncate">
              {getPageTitle()}
            </h1>
          </div>

          {/* Right section */}
          <div className="flex items-center space-x-2">
            {/* Search button */}
            {searchEnabled && (
              <button
                onClick={onSearchClick}
                className={`
                  p-2 rounded-md text-gray-700 dark:text-gray-300
                  hover:bg-gray-100 dark:hover:bg-gray-800
                  focus:outline-none focus:ring-2 focus:ring-blue-500
                  ${touchFriendlyProps.touchFeedback}
                `}
                style={touchFriendlyProps.minTouchTarget}
                aria-label="Search"
                {...ariaUtils.button}
              >
                <Search className="w-5 h-5" aria-hidden="true" />
              </button>
            )}

            {/* Theme toggle */}
            <ThemeToggle />

            {/* Custom actions */}
            {actions}

            {/* User menu */}
            {isAuthenticated ? (
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => setShowDropdown(!showDropdown)}
                  className={`
                    p-2 rounded-md text-gray-700 dark:text-gray-300
                    hover:bg-gray-100 dark:hover:bg-gray-800
                    focus:outline-none focus:ring-2 focus:ring-blue-500
                    ${touchFriendlyProps.touchFeedback}
                  `}
                  style={touchFriendlyProps.minTouchTarget}
                  aria-label="User menu"
                  aria-expanded={showDropdown}
                  aria-haspopup="menu"
                  {...ariaUtils.button}
                >
                  <MoreVertical className="w-5 h-5" aria-hidden="true" />
                </button>

                {/* User dropdown */}
                {showDropdown && (
                  <div
                    className="
                      absolute right-0 top-full mt-2 w-56
                      bg-white dark:bg-gray-800 rounded-md shadow-lg
                      border border-gray-200 dark:border-gray-700
                      py-2 z-50
                    "
                    role="menu"
                    aria-label="User menu"
                  >
                    <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {user?.username}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {user?.email}
                      </p>
                    </div>

                    <Link
                      to="/profile"
                      className="
                        block px-4 py-3 text-sm text-gray-700 dark:text-gray-300
                        hover:bg-gray-100 dark:hover:bg-gray-700
                        focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700
                      "
                      style={touchFriendlyProps.minTouchTarget}
                      role="menuitem"
                      onClick={() => {
                        setShowDropdown(false);
                        if (isTouch) hapticFeedback.light();
                      }}
                    >
                      Profile
                    </Link>

                    <Link
                      to="/settings"
                      className="
                        block px-4 py-3 text-sm text-gray-700 dark:text-gray-300
                        hover:bg-gray-100 dark:hover:bg-gray-700
                        focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700
                      "
                      style={touchFriendlyProps.minTouchTarget}
                      role="menuitem"
                      onClick={() => {
                        setShowDropdown(false);
                        if (isTouch) hapticFeedback.light();
                      }}
                    >
                      Settings
                    </Link>

                    <button
                      onClick={() => {
                        setShowDropdown(false);
                        handleLogout();
                      }}
                      className="
                        w-full text-left px-4 py-3 text-sm text-red-600 dark:text-red-400
                        hover:bg-red-50 dark:hover:bg-red-900/20
                        focus:outline-none focus:bg-red-50 dark:focus:bg-red-900/20
                      "
                      style={touchFriendlyProps.minTouchTarget}
                      role="menuitem"
                    >
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/login"
                  className="
                    px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300
                    hover:text-blue-600 dark:hover:text-blue-400
                    focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md
                  "
                  style={touchFriendlyProps.minTouchTarget}
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="
                    px-4 py-2 text-sm font-medium text-white
                    bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600
                    rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500
                    transition-colors duration-150
                  "
                  style={touchFriendlyProps.minTouchTarget}
                >
                  Register
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default MobileNavbar;
