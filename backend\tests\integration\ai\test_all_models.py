"""
Comprehensive test for all 5 Ollama LLMs required for Phase 4.
Tests each model individually with agent-specific tasks.
"""

import asyncio
import sys
import os

# Import path is handled by pyproject.toml configuration

async def test_all_agent_models():
    """Test all 5 required models with their respective agents."""
    print("🧪 Testing All 5 Agent Models Individually\n")

    try:
        from ai_coding_agent.services.ai.providers.ollama import OllamaProvider
        from ai_coding_agent.services.ai.base import ChatRequest, ChatMessage
        from ai_coding_agent.agents import AgentRole, AGENT_CONFIGS

        provider = OllamaProvider()

        # Define test tasks for each agent
        agent_tests = [
            {
                "agent": AgentRole.ARCHITECT,
                "model": AGENT_CONFIGS[AgentRole.ARCHITECT].model,
                "task": "Plan a simple web application with authentication"
            },
            {
                "agent": AgentRole.FRONTEND,
                "model": AGENT_CONFIGS[AgentRole.FRONTEND].model,
                "task": "Create a React component for a login form"
            },
            {
                "agent": Agent<PERSON><PERSON>.BACKEND,
                "model": AGENT_CONFIGS[AgentRole.BACKEND].model,
                "task": "Write a FastAPI endpoint for user registration"
            },
            {
                "agent": Agent<PERSON>ole.SHELL,
                "model": AGENT_CONFIGS[AgentRole.SHELL].model,
                "task": "Write a bash script to set up a Python virtual environment"
            },
            {
                "agent": AgentRole.DEBUG,
                "model": AGENT_CONFIGS[AgentRole.DEBUG].model,
                "task": "Debug this Python error: AttributeError: 'NoneType' object has no attribute 'split'"
            }
        ]

        results = []

        for i, test in enumerate(agent_tests, 1):
            print(f"🔍 Test {i}/5: {test['agent'].value.title()} Agent")
            print(f"   Model: {test['model']}")
            print(f"   Task: {test['task']}")

            try:
                # Create chat request
                messages = [ChatMessage(role="user", content=test['task'])]
                request = ChatRequest(
                    messages=messages,
                    agent_role=test['agent']
                )

                # Execute chat
                response = await provider.chat(request)

                # Check response
                success = response.content and len(response.content) > 50

                if success:
                    print(f"   ✅ Success - Response: {len(response.content)} chars")
                    print(f"   📝 Preview: {response.content[:100]}...")
                    if response.metadata:
                        print(f"   ⏱️ Response time: {response.metadata.get('response_time_ms', 'unknown')}ms")
                else:
                    print(f"   ❌ Failed - Short or empty response")

                results.append({
                    'agent': test['agent'].value,
                    'model': test['model'],
                    'success': success,
                    'response_length': len(response.content) if response.content else 0,
                    'response_time': response.metadata.get('response_time_ms') if response.metadata else None
                })

            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                results.append({
                    'agent': test['agent'].value,
                    'model': test['model'],
                    'success': False,
                    'error': str(e)
                })

            print()  # Empty line for readability

        # Summary
        successful_tests = sum(1 for r in results if r['success'])
        print("=" * 60)
        print(f"🎯 Test Summary: {successful_tests}/5 models working")
        print("=" * 60)

        for result in results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['agent'].title()} Agent ({result['model']})")
            if result['success']:
                print(f"   Response: {result['response_length']} chars in {result.get('response_time', '?')}ms")
            else:
                print(f"   Error: {result.get('error', 'Unknown error')}")

        await provider.close()
        return successful_tests == 5

    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

async def test_model_health():
    """Test health of all required models."""
    print("🏥 Testing Model Health\n")

    try:
        from ai_coding_agent.services.ai.providers.ollama import OllamaProvider

        provider = OllamaProvider()
        health = await provider.health_check()

        print(f"Overall Status: {health.status.value}")
        print(f"Provider: {health.provider_name}")
        print(f"Models tested: {len(health.models)}")
        print()

        healthy_count = 0
        for model_health in health.models:
            status_icon = "✅" if model_health.status.value == "healthy" else "❌"
            print(f"{status_icon} {model_health.model}")
            print(f"   Status: {model_health.status.value}")
            if model_health.latency_ms:
                print(f"   Latency: {model_health.latency_ms}ms")
            if model_health.error:
                print(f"   Error: {model_health.error}")

            if model_health.status.value == "healthy":
                healthy_count += 1
            print()

        await provider.close()

        print(f"🎯 Health Summary: {healthy_count}/{len(health.models)} models healthy")
        return healthy_count == len(health.models)

    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

async def main():
    """Run comprehensive model tests."""
    print("🚀 Comprehensive 5-Model Test Suite")
    print("🎯 Testing Phase 4: AI Integration Foundation")
    print("=" * 60)
    print()

    # Test model health first
    health_ok = await test_model_health()

    if not health_ok:
        print("⚠️  Some models are unhealthy, but continuing with functional tests...")
        print()

    # Test all models functionally
    all_models_ok = await test_all_agent_models()

    if all_models_ok:
        print("\n🎉 SUCCESS: All 5 Ollama LLMs are working perfectly!")
        print("✅ Phase 4: AI Integration Foundation is COMPLETE")
    else:
        print("\n⚠️  Some models failed testing. Check Ollama installation.")
        print("💡 You may need to restart Ollama or pull missing models.")

    return all_models_ok

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
