#!/usr/bin/env python3
"""
Test script for hybrid database configuration.
"""

def test_hybrid_db():
    """Test the hybrid database setup."""
    print("🧪 Testing Hybrid Database Configuration")

    try:
        from src.ai_coding_agent.models.base import get_hybrid_db_manager
        print("✅ Successfully imported get_hybrid_db_manager")

        manager = get_hybrid_db_manager()
        print("✅ Manager created successfully")

        # Test configuration
        print(f"📊 Mode: {manager.mode}")
        print(f"📋 Local tables: {manager.local_tables}")
        print(f"☁️ Supabase tables: {manager.supabase_tables}")

        # Test connection status
        status = manager.get_connection_status()
        print("\n🔍 Connection Status:")
        for key, value in status.items():
            print(f"  {key}: {value}")

        # Test table routing
        print("\n🎯 Table Routing Test:")
        test_tables = ["projects", "roadmaps", "best_practices", "tech_stack_metadata"]
        for table in test_tables:
            local = manager.is_local_table(table)
            supabase = manager.is_supabase_table(table)
            location = "Local" if local else "Supabase" if supabase else "Unknown"
            print(f"  {table}: {location}")

        print("\n✅ Hybrid database test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_hybrid_db()
