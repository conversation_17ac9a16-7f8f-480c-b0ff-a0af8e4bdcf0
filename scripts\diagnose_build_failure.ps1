# Backend Docker Build Diagnostic Script
# This script captures detailed build information to identify the exact failure

Write-Host "🔍 Backend Docker Build Diagnostics" -ForegroundColor Cyan
Write-Host "===================================="

# Create logs directory
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs"
}

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = "logs/build_diagnosis_$timestamp.log"

Write-Host "📋 Capturing build diagnostics..." -ForegroundColor Yellow

# System information
Write-Host "`n1. System Information" -ForegroundColor Green
"=== SYSTEM INFO ===" | Out-File $logFile
"Date: $(Get-Date)" | Out-File $logFile -Append
"Docker Version:" | Out-File $logFile -Append
docker --version | Out-File $logFile -Append
"Docker Compose Version:" | Out-File $logFile -Append
docker-compose --version | Out-File $logFile -Append
"Available Disk Space:" | Out-File $logFile -Append
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}}, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}} | Out-File $logFile -Append

# Docker system info
Write-Host "`n2. Docker System Information" -ForegroundColor Green
"`n=== DOCKER SYSTEM INFO ===" | Out-File $logFile -Append
docker system df | Out-File $logFile -Append
docker system info | Out-File $logFile -Append

# Check if files exist
Write-Host "`n3. File Existence Check" -ForegroundColor Green
"`n=== FILE EXISTENCE CHECK ===" | Out-File $logFile -Append
$requiredFiles = @(
    "backend/Dockerfile",
    "backend/requirements.txt", 
    "backend/requirements-dev.txt",
    "backend/src/ai_coding_agent/__init__.py",
    "backend/src/ai_coding_agent/main.py"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        "✅ $file exists" | Out-File $logFile -Append
    } else {
        "❌ $file MISSING" | Out-File $logFile -Append
    }
}

# Check .dockerignore
Write-Host "`n4. Docker Context Check" -ForegroundColor Green
"`n=== DOCKER CONTEXT CHECK ===" | Out-File $logFile -Append
if (Test-Path "backend/.dockerignore") {
    "Backend .dockerignore contents:" | Out-File $logFile -Append
    Get-Content "backend/.dockerignore" | Out-File $logFile -Append
} else {
    "No backend/.dockerignore file found" | Out-File $logFile -Append
}

# Attempt build with detailed output
Write-Host "`n5. Attempting Build with Detailed Logs" -ForegroundColor Green
"`n=== BUILD ATTEMPT ===" | Out-File $logFile -Append

Write-Host "Building backend container with detailed output..." -ForegroundColor Yellow
Write-Host "This may take several minutes..." -ForegroundColor Yellow

try {
    # Build with maximum verbosity
    $buildOutput = docker build --no-cache --progress=plain --target=production ./backend 2>&1
    $buildOutput | Out-File $logFile -Append
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build succeeded!" -ForegroundColor Green
        "BUILD RESULT: SUCCESS" | Out-File $logFile -Append
    } else {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        "BUILD RESULT: FAILED" | Out-File $logFile -Append
    }
} catch {
    Write-Host "❌ Build command failed to execute!" -ForegroundColor Red
    "BUILD RESULT: COMMAND FAILED - $($_.Exception.Message)" | Out-File $logFile -Append
}

# Check for common error patterns
Write-Host "`n6. Error Pattern Analysis" -ForegroundColor Green
"`n=== ERROR PATTERN ANALYSIS ===" | Out-File $logFile -Append

$logContent = Get-Content $logFile -Raw
$commonErrors = @{
    "psycopg2" = "PostgreSQL adapter compilation issue"
    "gcc.*not found" = "Missing C compiler"
    "No space left" = "Insufficient disk space"
    "network timeout" = "Network connectivity issue"
    "permission denied" = "File permission issue"
    "requirements.txt.*not found" = "Missing requirements file"
    "COPY failed" = "File copy operation failed"
    "pip.*failed" = "Python package installation failed"
}

foreach ($pattern in $commonErrors.Keys) {
    if ($logContent -match $pattern) {
        "🔍 DETECTED: $($commonErrors[$pattern]) (pattern: $pattern)" | Out-File $logFile -Append
    }
}

Write-Host "`n📄 Diagnostic Results" -ForegroundColor Cyan
Write-Host "====================="
Write-Host "Full diagnostic log saved to: $logFile" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review the log file for specific error messages" -ForegroundColor Gray
Write-Host "2. Look for the ERROR PATTERN ANALYSIS section" -ForegroundColor Gray
Write-Host "3. Run the appropriate fix script based on the detected issues" -ForegroundColor Gray
Write-Host ""

# Display last 20 lines of build output for immediate review
Write-Host "Last 20 lines of build output:" -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor Yellow
$buildLines = $buildOutput | Select-Object -Last 20
$buildLines | ForEach-Object { Write-Host $_ -ForegroundColor Gray }

Write-Host "`nDiagnostic complete! Check $logFile for full details." -ForegroundColor Green
