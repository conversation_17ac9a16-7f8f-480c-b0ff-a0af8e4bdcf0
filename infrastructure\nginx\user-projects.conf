# AI Coding Agent - User Project Subdomain Configuration
# Handles routing for user project previews on unique subdomains

# Rate limiting zones for user projects
limit_req_zone $binary_remote_addr zone=user_project_limit:10m rate=20r/s;

# Map to extract user ID from subdomain
map $host $user_id {
    ~^preview-(?<uid>[^.]+)\.localhost$ $uid;
    ~^preview-(?<uid>[^.]+)\.ai-coding-agent\.local$ $uid;
    default "";
}

# User project preview server block
server {
    listen 80;
    server_name ~^preview-(?<user_id>[^.]+)\.(localhost|ai-coding-agent\.local)$;

    # Security headers for user projects
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Rate limiting for user projects
    limit_req zone=user_project_limit burst=50 nodelay;

    # Client upload size limit
    client_max_body_size 50M;

    # Timeout settings
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;

    # Log user project access
    access_log /var/log/nginx/user-projects.log combined;
    error_log /var/log/nginx/user-projects-error.log warn;

    # Authentication check - verify user has access to this project
    auth_request /auth-check;

    location = /auth-check {
        internal;
        proxy_pass http://backend:8000/api/v1/containers/auth-check;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_header X-User-ID $user_id;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # Cache auth responses for performance
        proxy_cache_valid 200 5m;
        proxy_cache_valid 401 403 1m;
    }

    # Dynamic port resolution endpoint
    location = /get-port {
        internal;
        proxy_pass http://backend:8000/api/v1/containers/get-port;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-User-ID $user_id;
    }

    # Proxy to user container based on extracted user_id
    location / {
        # Validate user_id is present
        if ($user_id = "") {
            return 404;
        }

        # Set upstream based on user container port mapping
        # This will be dynamically managed by the container manager
        set $upstream_port 3000;  # Default port, will be dynamic

        # Proxy to user container
        proxy_pass http://host.docker.internal:$upstream_port;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-User-ID $user_id;
        proxy_cache_bypass $http_upgrade;

        # Handle connection errors gracefully
        proxy_intercept_errors on;
        error_page 502 503 504 = @user_container_error;
    }

    # WebSocket support for user projects (hot reload, etc.)
    location /ws {
        if ($user_id = "") {
            return 404;
        }

        set $upstream_port 3000;  # Default port, will be dynamic

        proxy_pass http://host.docker.internal:$upstream_port;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-User-ID $user_id;
        proxy_read_timeout 86400;
    }

    # Handle user container errors
    location @user_container_error {
        return 503 "User project container is not available. Please check if your project is running.";
        add_header Content-Type text/plain;
    }

    # Health check for user projects
    location /health {
        access_log off;
        return 200 "User project proxy healthy\n";
        add_header Content-Type text/plain;
    }
}

# API endpoint for managing user project routing
server {
    listen 80;
    server_name localhost ai-coding-agent.local;

    # User project management API (internal use)
    location /api/v1/containers/routing/ {
        # Only allow internal access from backend
        allow **********/16;  # Docker network
        deny all;

        # Proxy to backend for container port management
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Wildcard server block for undefined subdomains
server {
    listen 80 default_server;
    server_name _;

    # Return 404 for undefined subdomains
    location / {
        return 404 "Subdomain not found";
        add_header Content-Type text/plain;
    }
}
