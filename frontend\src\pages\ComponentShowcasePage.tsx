import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import {
  Skeleton,
  CardSkeleton,
  TableSkeleton,
  ListSkeleton,
  FormSkeleton,
  ProgressBar,
  CircularProgress,
  Alert,
  Toast,
  Badge,
  Card,
  Tooltip,
  Dropdown,
  MultiSelect
} from '../components/ui';
import { validateEmail, validatePassword, sanitizeInput } from '../utils/validation';
import { retryApiCall } from '../utils/retry';

const ComponentShowcasePage: React.FC = () => {
  const { theme } = useTheme();
  const [progress, setProgress] = useState(65);
  const [showSkeletons, setShowSkeletons] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [selectedOption, setSelectedOption] = useState('');
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [testInput, setTestInput] = useState('');
  const [validationResult, setValidationResult] = useState<string>('');

  const dropdownOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4 (Disabled)', disabled: true }
  ];

  const handleInputValidation = () => {
    const sanitized = sanitizeInput(testInput);
    const isValidEmail = validateEmail(testInput);
    const passwordValidation = validatePassword(testInput);

    if (isValidEmail) {
      setValidationResult('✅ Valid email address');
    } else if (passwordValidation.isValid) {
      setValidationResult('✅ Strong password');
    } else {
      setValidationResult(`❌ Sanitized: "${sanitized}"`);
    }
  };

  const testRetryMechanism = async () => {
    try {
      await retryApiCall(async () => {
        // Simulate a failing API call that eventually succeeds
        if (Math.random() > 0.7) {
          throw new Error('Simulated network error');
        }
        return { success: true };
      });
      setValidationResult('✅ Retry mechanism succeeded');
    } catch (error) {
      setValidationResult('❌ Retry mechanism failed after attempts');
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Component Showcase
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Phase 2-3 implementation: Security features, Enhanced UI components, and Code splitting demo
        </p>
        <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
          <p className="text-blue-800 dark:text-blue-200 text-sm">
            Current theme: <span className="font-semibold capitalize">{theme}</span> |
            This page is lazy-loaded via React.lazy and Suspense
          </p>
        </div>
      </div>

      {/* New UI Components */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Enhanced UI Components
        </h2>

        {/* Alerts and Notifications */}
        <Card className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Alerts & Notifications
          </h3>
          <div className="space-y-4">
            <Alert type="success" title="Success!" message="Your changes have been saved successfully." />
            <Alert type="error" title="Error!" message="There was a problem processing your request." />
            <Alert type="warning" title="Warning!" message="This action cannot be undone." />
            <Alert type="info" message="This is an informational message without a title." />

            <div className="flex gap-2">
              <button
                onClick={() => setShowToast(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
              >
                Show Toast
              </button>
            </div>
          </div>
        </Card>

        {/* Badges and Cards */}
        <div className="grid lg:grid-cols-2 gap-6 mb-6">
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Badges
            </h3>
            <div className="space-y-3">
              <div className="flex flex-wrap gap-2">
                <Badge variant="default">Default</Badge>
                <Badge variant="success">Success</Badge>
                <Badge variant="error">Error</Badge>
                <Badge variant="warning">Warning</Badge>
                <Badge variant="info">Info</Badge>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge size="sm" variant="success">Small</Badge>
                <Badge size="md" variant="info">Medium</Badge>
                <Badge size="lg" variant="warning">Large</Badge>
              </div>
            </div>
          </Card>

          <Card hover>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Tooltips
            </h3>
            <div className="space-y-4">
              <div className="flex gap-4">
                <Tooltip content="This is a top tooltip" position="top">
                  <button className="bg-gray-600 text-white px-3 py-1 rounded">Top</button>
                </Tooltip>
                <Tooltip content="This is a bottom tooltip" position="bottom">
                  <button className="bg-gray-600 text-white px-3 py-1 rounded">Bottom</button>
                </Tooltip>
                <Tooltip content="This is a left tooltip" position="left">
                  <button className="bg-gray-600 text-white px-3 py-1 rounded">Left</button>
                </Tooltip>
                <Tooltip content="This is a right tooltip" position="right">
                  <button className="bg-gray-600 text-white px-3 py-1 rounded">Right</button>
                </Tooltip>
              </div>
            </div>
          </Card>
        </div>

        {/* Dropdowns */}
        <Card className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Dropdown Components
          </h3>
          <div className="grid lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Single Select
              </label>
              <Dropdown
                options={dropdownOptions}
                value={selectedOption}
                onChange={setSelectedOption}
                placeholder="Choose an option..."
              />
              {selectedOption && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Selected: {dropdownOptions.find(opt => opt.value === selectedOption)?.label}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Multi Select
              </label>
              <MultiSelect
                options={dropdownOptions}
                values={selectedOptions}
                onChange={setSelectedOptions}
                placeholder="Choose multiple options..."
                maxSelections={3}
              />
              {selectedOptions.length > 0 && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Selected: {selectedOptions.join(', ')}
                </p>
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Security Features */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Security Features
        </h2>
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Input Validation & XSS Protection
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Test Input (Try entering an email, password, or malicious script)
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={testInput}
                  onChange={(e) => setTestInput(e.target.value)}
                  placeholder="Enter test input..."
                  className="flex-1 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-white"
                />
                <button
                  onClick={handleInputValidation}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
                >
                  Validate
                </button>
              </div>
              {validationResult && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {validationResult}
                </p>
              )}
            </div>
            <div>
              <button
                onClick={testRetryMechanism}
                className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md"
              >
                Test Retry Mechanism
              </button>
            </div>
          </div>
        </Card>
      </div>

      {/* Original Progress and Skeleton sections */}
      <div className="grid lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Interactive Controls
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Progress: {progress}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                onChange={(e) => setProgress(Number(e.target.value))}
                className="w-full"
              />
            </div>
            <div>
              <button
                onClick={() => setShowSkeletons(!showSkeletons)}
                className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                {showSkeletons ? 'Hide' : 'Show'} Skeleton Loaders
              </button>
            </div>
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Progress Indicators
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Linear Progress
              </label>
              <ProgressBar progress={progress} showLabel />
            </div>
            <div className="flex justify-center">
              <CircularProgress progress={progress} size={100} />
            </div>
          </div>
        </Card>
      </div>

      {/* Skeleton Loaders Demo */}
      {showSkeletons && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Skeleton Loaders
          </h2>

          <div className="grid gap-6 mb-6">
            <Card>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Basic Skeletons</h3>
              <div className="space-y-3">
                <Skeleton width="w-3/4" height="h-6" />
                <Skeleton variant="text" lines={3} />
                <div className="flex items-center space-x-4">
                  <Skeleton variant="circular" width="w-12" height="h-12" />
                  <div className="flex-1">
                    <Skeleton width="w-1/2" height="h-4" className="mb-2" />
                    <Skeleton width="w-full" height="h-3" />
                  </div>
                </div>
              </div>
            </Card>

            <div className="grid lg:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Card Skeleton</h3>
                <CardSkeleton showImage showActions />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Form Skeleton</h3>
                <FormSkeleton fields={3} />
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">List Skeleton</h3>
              <ListSkeleton items={4} showAvatar />
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Table Skeleton</h3>
              <TableSkeleton rows={3} columns={4} />
            </div>
          </div>
        </div>
      )}

      {/* Feature Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 p-6 rounded-lg">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Phase 2-3 Features Completed ✅
        </h2>
        <div className="grid md:grid-cols-4 gap-6 text-sm">
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🌓 Dark/Light Theme</h4>
            <ul className="text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Context-based theme management</li>
              <li>• LocalStorage persistence</li>
              <li>• System preference detection</li>
              <li>• Smooth animations</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">⚡ Enhanced Loading</h4>
            <ul className="text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Modular skeleton components</li>
              <li>• Contextual loading states</li>
              <li>• Progress indicators</li>
              <li>• Reduced perceived load times</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🔒 Security Features</h4>
            <ul className="text-gray-600 dark:text-gray-400 space-y-1">
              <li>• XSS protection & input sanitization</li>
              <li>• Enhanced error boundaries</li>
              <li>• Retry mechanisms</li>
              <li>• HTTPS enforcement</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🎨 UI Components</h4>
            <ul className="text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Alert & notification system</li>
              <li>• Badge & card components</li>
              <li>• Dropdown & multi-select</li>
              <li>• Tooltip system</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Toast Component */}
      {/* Toast Component */}
      <Toast
        visible={showToast}
        type="success"
        title="Success!"
        message="This is a toast notification that auto-dismisses after 5 seconds."
        onDismiss={() => setShowToast(false)}
      />
    </div>
  );
};

export default ComponentShowcasePage;
