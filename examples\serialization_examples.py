"""
Real-world examples of how the JSON serializer works with your AI Coding Agent project.
Run these examples to see the serializer in action.
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal
from uuid import uuid4, UUID
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Import your project's serialization utilities
from ai_coding_agent.utils.serialization import (
    serialize_for_json,
    to_json,
    serialize_dependency_data,
    serialize_ai_response,
    serialize_task_data
)

# Import your project models (these would be your actual models)
try:
    from ai_coding_agent.models import (
        Task, DependencyCheckResult, DependencyCheckStatus,
        BlockingDependency, DependencyType, TaskStatus
    )
except ImportError:
    # Mock classes for demonstration
    class Task:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class DependencyCheckResult:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)


def example_1_basic_serialization():
    """Example 1: Basic object serialization"""
    print("🔧 Example 1: Basic Object Serialization")

    # Complex data that would normally break JSON
    complex_data = {
        "timestamp": datetime.now(),
        "task_id": uuid4(),
        "price": Decimal("99.99"),
        "binary_data": b"some binary data",
        "nested": {
            "date": datetime.now().date(),
            "more_uuid": uuid4()
        }
    }

    print("Original data types:")
    for key, value in complex_data.items():
        print(f"  {key}: {type(value).__name__} = {value}")

    # Serialize safely
    safe_data = serialize_for_json(complex_data)
    json_string = to_json(complex_data, indent=2)

    print("\nSerialized data:")
    print(json_string)
    print("✅ Success - no errors!\n")


def example_2_dependency_engine_logging():
    """Example 2: Dependency Engine Complex Logging"""
    print("🔧 Example 2: Dependency Engine Logging")

    # Simulate complex dependency check result
    dependency_result = {
        "check_id": str(uuid4()),
        "timestamp": datetime.now(),
        "task_id": "task_123",
        "status": "BLOCKED",
        "blocking_dependencies": [
            {
                "dependency_id": "dep_456",
                "type": "TASK",
                "current_status": "IN_PROGRESS",
                "estimated_completion": datetime.now() + timedelta(hours=2),
                "metadata": {
                    "priority": "high",
                    "assigned_to": uuid4(),
                    "created_at": datetime.now()
                }
            }
        ],
        "ai_predictions": ["database_setup", "api_integration"],
        "cache_stats": {
            "hit_rate": Decimal("0.85"),
            "total_checks": 1247,
            "last_updated": datetime.now()
        },
        "performance_metrics": {
            "check_duration_ms": Decimal("45.2"),
            "cache_lookup_ms": Decimal("2.1"),
            "db_query_ms": Decimal("12.8")
        }
    }

    # This would crash regular json.dumps()
    try:
        regular_json = json.dumps(dependency_result)
        print("❌ This shouldn't work...")
    except TypeError as e:
        print(f"❌ Regular JSON fails: {e}")

    # But our serializer handles it perfectly
    safe_json = serialize_dependency_data(dependency_result)
    json_string = to_json(dependency_result, indent=2)

    print("✅ Our serializer works:")
    print(json_string[:500] + "..." if len(json_string) > 500 else json_string)
    print()


def example_3_ai_response_logging():
    """Example 3: AI Response Logging"""
    print("🔧 Example 3: AI Response Logging")

    # Simulate complex AI response
    ai_response = {
        "request_id": str(uuid4()),
        "timestamp": datetime.now(),
        "model": "qwen2.5:3b",
        "prompt": "Analyze dependencies for user authentication system",
        "response": {
            "generated_text": "Based on the analysis, you'll need...",
            "confidence": Decimal("0.92"),
            "tokens_used": 150,
            "processing_time": Decimal("2.34"),
            "metadata": {
                "model_version": "v2.5.1",
                "temperature": Decimal("0.7"),
                "max_tokens": 500,
                "created_at": datetime.now()
            }
        },
        "usage": {
            "prompt_tokens": 45,
            "completion_tokens": 150,
            "total_tokens": 195,
            "cost": Decimal("0.0023"),
            "billing_id": uuid4()
        },
        "predictions": [
            {
                "dependency": "database_connection",
                "confidence": Decimal("0.95"),
                "estimated_effort": "2 hours"
            },
            {
                "dependency": "authentication_middleware",
                "confidence": Decimal("0.88"),
                "estimated_effort": "4 hours"
            }
        ]
    }

    # Safe AI response logging
    safe_response = serialize_ai_response(ai_response)
    json_string = to_json(ai_response, indent=2)

    print("✅ AI Response safely serialized:")
    print(json_string[:400] + "..." if len(json_string) > 400 else json_string)
    print()


def example_4_audit_trail_integration():
    """Example 4: Audit Trail Integration"""
    print("🔧 Example 4: Audit Trail Integration")

    # Simulate task update with complex data
    old_task_data = {
        "id": str(uuid4()),
        "name": "Setup Authentication",
        "status": "pending",
        "created_at": datetime.now() - timedelta(days=1),
        "assigned_to": uuid4(),
        "metadata": {
            "priority": "high",
            "estimated_hours": Decimal("8.5"),
            "dependencies": ["database", "api_framework"],
            "tags": {"security", "backend", "critical"}
        }
    }

    new_task_data = {
        **old_task_data,
        "status": "in_progress",
        "updated_at": datetime.now(),
        "progress": Decimal("0.25"),
        "time_spent": Decimal("2.5"),
        "notes": "Started with database schema design",
        "metadata": {
            **old_task_data["metadata"],
            "last_activity": datetime.now(),
            "progress_details": {
                "completed": ["schema_design"],
                "in_progress": ["user_model"],
                "pending": ["authentication_logic", "password_hashing"]
            }
        }
    }

    # This is how it would be used in your audit service
    audit_data = {
        "entity_type": "TASK",
        "entity_id": old_task_data["id"],
        "action": "UPDATE",
        "old_values": serialize_task_data(old_task_data),
        "new_values": serialize_task_data(new_task_data),
        "metadata": {
            "changed_by": uuid4(),
            "change_reason": "Progress update",
            "timestamp": datetime.now(),
            "session_id": str(uuid4())
        }
    }

    json_string = to_json(audit_data, indent=2)
    print("✅ Audit trail data safely serialized:")
    print(json_string[:500] + "..." if len(json_string) > 500 else json_string)
    print()


def example_5_error_handling():
    """Example 5: Error Handling - Never Crashes"""
    print("🔧 Example 5: Error Handling")

    # Create some weird objects that would normally break serialization
    class WeirdCustomClass:
        def __init__(self):
            self.data = "some data"
            self.timestamp = datetime.now()

        def __str__(self):
            return f"WeirdCustomClass(data={self.data})"

    class UnserializableClass:
        def __str__(self):
            raise Exception("Can't even stringify this!")

    weird_data = {
        "normal_string": "this is fine",
        "datetime": datetime.now(),
        "uuid": uuid4(),
        "decimal": Decimal("123.45"),
        "custom_object": WeirdCustomClass(),
        "unserializable": UnserializableClass(),
        "nested_weird": {
            "another_custom": WeirdCustomClass(),
            "set_data": {1, 2, 3, "mixed", datetime.now()},
            "tuple_data": (1, datetime.now(), uuid4())
        }
    }

    # Our serializer handles everything gracefully
    safe_data = serialize_for_json(weird_data)
    json_string = to_json(weird_data, indent=2)

    print("✅ Even weird objects work:")
    print(json_string)
    print("🎉 Never crashes - always produces valid JSON!\n")


if __name__ == "__main__":
    print("🚀 AI Coding Agent - JSON Serialization Examples\n")

    example_1_basic_serialization()
    example_2_dependency_engine_logging()
    example_3_ai_response_logging()
    example_4_audit_trail_integration()
    example_5_error_handling()

    print("🎉 All examples completed successfully!")
    print("💡 Your audit trails will never break again!")
