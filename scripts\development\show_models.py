#!/usr/bin/env python3
"""
AI Models Setup and Overview Script

This script provides an overview of all AI models used in the project
and their specific role assignments.
"""

import sys
import os

# Import path is handled by pyproject.toml configuration

from ai_coding_agent.agents import AGENT_CONFIGS, get_model_usage_summary, AgentRole
from ai_coding_agent.config import settings


def print_model_overview():
    """Print a comprehensive overview of all AI models and their assignments."""

    print("🤖 AI Coding Agent - Model Configuration Overview")
    print("=" * 60)
    print()

    # Model specifications
    models_info = {
        "mistral:7b-instruct-q4_0": {
            "size": "~4GB",
            "strengths": "Reasoning, explanations, complex analysis",
            "speed": "Medium (1-3s)",
            "use_case": "Strategic planning and detailed explanations"
        },
        "starcoder2:3b": {
            "size": "~1.7GB",
            "strengths": "Code completion, frontend development",
            "speed": "Fast (200-800ms)",
            "use_case": "Real-time code completion and UI generation"
        },
        "yi-coder:1.5b": {
            "size": "~866MB",
            "strengths": "Fast code generation, lightweight",
            "speed": "Very Fast (100-500ms)",
            "use_case": "Quick code generation and backend logic"
        },
        "qwen2.5:3b": {
            "size": "~1.9GB",
            "strengths": "Balanced performance, quick responses",
            "speed": "Fast (300-1000ms)",
            "use_case": "Chat interactions and system commands"
        },
        "deepseek-coder:6.7b": {
            "size": "~3.8GB",
            "strengths": "Advanced code analysis, debugging",
            "speed": "Slow (2-5s)",
            "use_case": "Complex debugging and code review"
        }
    }

    print("📊 Model Specifications:")
    print("-" * 40)
    for model, info in models_info.items():
        print(f"🔹 {model}")
        print(f"   Size: {info['size']}")
        print(f"   Speed: {info['speed']}")
        print(f"   Strengths: {info['strengths']}")
        print(f"   Use Case: {info['use_case']}")
        print()

    print("🎯 Agent Role Assignments:")
    print("-" * 40)
    for role, config in AGENT_CONFIGS.items():
        print(f"🤖 {role.value.upper()} AGENT")
        print(f"   Model: {config.model}")
        print(f"   Description: {config.description}")
        print(f"   Temperature: {config.temperature}")
        print(f"   Capabilities: {', '.join([str(cap.value) if hasattr(cap, 'value') else str(cap) for cap in config.capabilities])}")
        print()

    print("📈 Model Usage Summary:")
    print("-" * 40)
    model_usage = get_model_usage_summary()
    for model, agents in model_usage.items():
        print(f"🔹 {model}")
        print(f"   Used by: {', '.join([agent.title() + ' Agent' for agent in agents])}")
        print()

    print("⚡ Performance Optimization Strategy:")
    print("-" * 40)
    print("• Fast models (yi-coder, starcoder2, qwen2.5) for real-time tasks")
    print("• Medium models (mistral) for complex reasoning")
    print("• Large models (deepseek-coder) for detailed analysis")
    print("• Intelligent routing based on task complexity")
    print("• Parallel processing for independent tasks")
    print()

    print("🚀 Ollama Setup Commands:")
    print("-" * 40)
    for model in models_info.keys():
        print(f"ollama pull {model}")
    print()

    print("✅ Total Models: 5")
    print("✅ Total Agents: 5")
    print("✅ Estimated Total Size: ~12GB")


if __name__ == "__main__":
    try:
        print_model_overview()
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have set up the environment variables correctly.")
        print("Copy .env.example to .env and configure the required settings.")
