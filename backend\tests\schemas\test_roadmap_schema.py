#!/usr/bin/env python3
"""
Test Roadmap JSON Schema Implementation
Validates the roadmap.json schema and validation service.
"""

import json
import sys
from pathlib import Path

# Import our schema validation service
from src.ai_coding_agent.services.schema_validation import schema_validator


def test_roadmap_schema_validation():
    """Test the roadmap schema validation functionality."""
    print("🔍 Testing Roadmap JSON Schema Implementation...")
    
    # Test 1: Load and validate the sample roadmap
    print("\n1. Testing sample roadmap validation...")
    
    sample_path = Path("examples/sample_roadmap.json")
    if sample_path.exists():
        with open(sample_path, 'r', encoding='utf-8') as f:
            sample_roadmap = json.load(f)
        
        is_valid, errors = schema_validator.validate_roadmap(sample_roadmap)
        
        if is_valid:
            print("   ✅ Sample roadmap is valid!")
        else:
            print("   ❌ Sample roadmap validation failed:")
            for error in errors:
                print(f"      - {error}")
    else:
        print("   ⚠️  Sample roadmap file not found")
    
    # Test 2: Test schema info retrieval
    print("\n2. Testing schema info retrieval...")
    
    schema_info = schema_validator.get_schema_info("roadmap")
    if "error" not in schema_info:
        print(f"   ✅ Schema info retrieved:")
        print(f"      - Title: {schema_info.get('title', 'Unknown')}")
        print(f"      - Description: {schema_info.get('description', 'No description')}")
        print(f"      - Required fields: {schema_info.get('required_fields', [])}")
    else:
        print(f"   ❌ Failed to get schema info: {schema_info['error']}")
    
    # Test 3: Test available schemas listing
    print("\n3. Testing available schemas listing...")
    
    schemas = schema_validator.list_available_schemas()
    print(f"   📋 Available schemas: {schemas}")
    
    # Test 4: Test validation with invalid data
    print("\n4. Testing validation with invalid data...")
    
    invalid_roadmap = {
        "name": "Test Roadmap",
        "version": "1.0.0",
        # Missing required project_id and phases
    }
    
    is_valid, errors = schema_validator.validate_roadmap(invalid_roadmap)
    
    if not is_valid:
        print("   ✅ Invalid roadmap correctly rejected:")
        for error in errors:
            print(f"      - {error}")
    else:
        print("   ❌ Invalid roadmap was incorrectly accepted")
    
    # Test 5: Test validation with minimal valid data
    print("\n5. Testing validation with minimal valid data...")
    
    minimal_roadmap = {
        "project_id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "Minimal Test Roadmap",
        "version": "1.0.0",
        "phases": [
            {
                "id": "phase-001",
                "name": "Test Phase",
                "order_index": 0,
                "steps": [
                    {
                        "id": "step-001",
                        "name": "Test Step",
                        "order_index": 0,
                        "tasks": [
                            {
                                "id": "task-001",
                                "name": "Test Task",
                                "order_index": 0,
                                "assigned_agent": "backend"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    is_valid, errors = schema_validator.validate_roadmap(minimal_roadmap)
    
    if is_valid:
        print("   ✅ Minimal roadmap is valid!")
    else:
        print("   ❌ Minimal roadmap validation failed:")
        for error in errors:
            print(f"      - {error}")
    
    # Test 6: Test version format validation
    print("\n6. Testing version format validation...")
    
    invalid_version_roadmap = minimal_roadmap.copy()
    invalid_version_roadmap["version"] = "invalid-version"
    
    is_valid, errors = schema_validator.validate_roadmap(invalid_version_roadmap)
    
    if not is_valid:
        print("   ✅ Invalid version format correctly rejected:")
        for error in errors:
            if "version" in error.lower():
                print(f"      - {error}")
    else:
        print("   ❌ Invalid version format was incorrectly accepted")
    
    # Test 7: Test agent assignment validation
    print("\n7. Testing agent assignment validation...")
    
    invalid_agent_roadmap = minimal_roadmap.copy()
    invalid_agent_roadmap["phases"][0]["steps"][0]["tasks"][0]["assigned_agent"] = "invalid_agent"
    
    is_valid, errors = schema_validator.validate_roadmap(invalid_agent_roadmap)
    
    if not is_valid:
        print("   ✅ Invalid agent assignment correctly rejected:")
        for error in errors:
            if "agent" in error.lower():
                print(f"      - {error}")
    else:
        print("   ❌ Invalid agent assignment was incorrectly accepted")
    
    # Test 8: Test complex roadmap structure
    print("\n8. Testing complex roadmap structure...")
    
    complex_roadmap = {
        "project_id": "550e8400-e29b-41d4-a716-446655440001",
        "name": "Complex Test Roadmap",
        "version": "2.1.3",
        "description": "A complex roadmap for testing",
        "status": "in_progress",
        "project_metadata": {
            "tech_stack": {
                "frontend": ["React", "TypeScript"],
                "backend": ["FastAPI", "Python"]
            },
            "team_size": 5,
            "priority": "high"
        },
        "phases": [
            {
                "id": "phase-001",
                "name": "Setup Phase",
                "description": "Initial setup",
                "order_index": 0,
                "status": "completed",
                "dependencies": [],
                "estimated_duration": "1 week",
                "steps": [
                    {
                        "id": "step-001",
                        "name": "Environment Setup",
                        "description": "Setup development environment",
                        "order_index": 0,
                        "status": "completed",
                        "dependencies": [],
                        "tasks": [
                            {
                                "id": "task-001",
                                "name": "Initialize Repository",
                                "description": "Create Git repository",
                                "order_index": 0,
                                "status": "completed",
                                "assigned_agent": "shell",
                                "dependencies": [],
                                "estimated_duration": "30 minutes",
                                "artifacts": [
                                    {
                                        "type": "configuration",
                                        "name": ".gitignore",
                                        "path": ".gitignore",
                                        "description": "Git ignore file",
                                        "size": 1024
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    is_valid, errors = schema_validator.validate_roadmap(complex_roadmap)
    
    if is_valid:
        print("   ✅ Complex roadmap is valid!")
    else:
        print("   ❌ Complex roadmap validation failed:")
        for error in errors:
            print(f"      - {error}")
    
    print("\n✅ Roadmap JSON Schema testing completed!")
    
    return True


def test_schema_file_structure():
    """Test that the schema file exists and is properly structured."""
    print("\n🔍 Testing schema file structure...")
    
    schema_path = Path("schemas/roadmap.json")
    
    if not schema_path.exists():
        print("   ❌ Schema file not found at schemas/roadmap.json")
        return False
    
    try:
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        # Check required schema properties
        required_props = ["$schema", "title", "type", "properties", "definitions"]
        missing_props = [prop for prop in required_props if prop not in schema]
        
        if missing_props:
            print(f"   ❌ Schema missing required properties: {missing_props}")
            return False
        
        # Check that main definitions exist
        required_definitions = ["phase", "step", "task", "artifact"]
        missing_definitions = [
            defn for defn in required_definitions 
            if defn not in schema.get("definitions", {})
        ]
        
        if missing_definitions:
            print(f"   ❌ Schema missing required definitions: {missing_definitions}")
            return False
        
        print("   ✅ Schema file structure is valid!")
        print(f"      - Title: {schema.get('title', 'Unknown')}")
        print(f"      - Schema version: {schema.get('$schema', 'Unknown')}")
        print(f"      - Definitions: {list(schema.get('definitions', {}).keys())}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"   ❌ Schema file contains invalid JSON: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ Error reading schema file: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 Starting Roadmap JSON Schema Tests...")
    
    # Test schema file structure
    schema_valid = test_schema_file_structure()
    
    if schema_valid:
        # Test validation functionality
        validation_success = test_roadmap_schema_validation()
        
        if validation_success:
            print("\n🎉 All roadmap schema tests passed!")
            sys.exit(0)
        else:
            print("\n💥 Some validation tests failed!")
            sys.exit(1)
    else:
        print("\n💥 Schema file structure tests failed!")
        sys.exit(1)
