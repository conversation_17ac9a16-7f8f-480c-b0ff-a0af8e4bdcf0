# Shared Project Templates

This directory contains global project templates available to all users.

## Available Templates

### Web Applications
- `fastapi-basic/` - Basic FastAPI application template
- `react-typescript/` - React with TypeScript template
- `fullstack-fastapi-react/` - Complete full-stack template

### AI/ML Projects
- `ml-pipeline/` - Machine learning pipeline template
- `data-analysis/` - Data analysis project template
- `ai-agent/` - AI agent development template

### Microservices
- `docker-microservice/` - Dockerized microservice template
- `kubernetes-app/` - Kubernetes-ready application template

## Template Structure

Each template should include:
- `template.json` - Template metadata and configuration
- `src/` - Source code structure
- `docs/` - Documentation templates
- `tests/` - Test structure and examples
- `README.md` - Template-specific documentation

## Adding New Templates

1. Create a new directory with a descriptive name
2. Include all required files and structure
3. Add template metadata in `template.json`
4. Document the template in its README.md
5. Test the template with the AI agent

## Template Metadata Format

```json
{
  "name": "Template Name",
  "description": "Brief description of the template",
  "version": "1.0.0",
  "author": "AI Coding Agent",
  "tags": ["web", "api", "python"],
  "requirements": {
    "python": ">=3.11",
    "node": ">=18.0.0"
  },
  "structure": {
    "src": "Source code directory",
    "tests": "Test files",
    "docs": "Documentation"
  }
}
```
