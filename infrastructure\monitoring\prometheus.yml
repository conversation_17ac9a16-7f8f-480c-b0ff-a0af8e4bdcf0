# AI Coding Agent - Prometheus Configuration
# Monitoring configuration for all services in the AI coding agent stack

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'ai-coding-agent'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # AI Coding Agent Backend API
  - job_name: 'ai-coding-agent-backend'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 15s
    metrics_path: /api/v1/metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Frontend Nginx metrics
  - job_name: 'ai-coding-agent-frontend'
    static_configs:
      - targets: ['frontend:80']
    scrape_interval: 30s
    metrics_path: /nginx_status
    scrape_timeout: 5s

  # Vector Database (Chroma)
  - job_name: 'vector-db'
    static_configs:
      - targets: ['vector-db:8000']
    scrape_interval: 30s
    metrics_path: /api/v1/heartbeat
    scrape_timeout: 10s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Ollama AI Service
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    scrape_interval: 60s
    metrics_path: /api/tags
    scrape_timeout: 30s

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # cAdvisor (container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # Docker daemon metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['docker-proxy:9323']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx reverse proxy metrics
  - job_name: 'nginx-proxy'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s
    metrics_path: /nginx_status
    scrape_timeout: 5s

  # Custom application metrics
  - job_name: 'ai-agent-orchestrator'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 15s
    metrics_path: /api/v1/orchestrator/metrics
    scrape_timeout: 10s

  - job_name: 'ai-agent-ltkb'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 30s
    metrics_path: /api/v1/ltkb/metrics
    scrape_timeout: 15s

  - job_name: 'ai-agent-roadmap'
    static_configs:
      - targets: ['backend:8000']
    scrape_interval: 30s
    metrics_path: /api/v1/roadmap/metrics
    scrape_timeout: 10s

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint.com/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Remote read configuration
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint.com/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Recording rules for performance optimization
recording_rules:
  - name: ai_coding_agent_aggregations
    interval: 30s
    rules:
      - record: ai_coding_agent:request_rate_5m
        expr: rate(http_requests_total[5m])
      
      - record: ai_coding_agent:error_rate_5m
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
      
      - record: ai_coding_agent:response_time_95p
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

# Alerting rules
alerting_rules:
  - name: ai_coding_agent_alerts
    rules:
      - alert: HighErrorRate
        expr: ai_coding_agent:error_rate_5m > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} for the last 5 minutes"

      - alert: HighResponseTime
        expr: ai_coding_agent:response_time_95p > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space low"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: VectorDBConnectionFailed
        expr: up{job="vector-db"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Vector database connection failed"
          description: "Cannot connect to vector database for more than 2 minutes"

      - alert: OllamaServiceDown
        expr: up{job="ollama"} == 0
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "Ollama AI service is down"
          description: "Ollama service has been down for more than 3 minutes"

      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL has been down for more than 1 minute"
