import React from 'react';
import { useSkipLinks } from '../utils/accessibility';

/**
 * Skip Navigation Links component for keyboard accessibility
 * Provides quick navigation to main content areas
 */

interface SkipLink {
  href: string;
  label: string;
}

interface SkipNavigationProps {
  links?: SkipLink[];
  className?: string;
}

const defaultSkipLinks: SkipLink[] = [
  { href: '#main-content', label: 'Skip to main content' },
  { href: '#navigation', label: 'Skip to navigation' },
  { href: '#sidebar', label: 'Skip to sidebar' },
  { href: '#footer', label: 'Skip to footer' },
];

const SkipNavigation: React.FC<SkipNavigationProps> = ({
  links = defaultSkipLinks,
  className = ''
}) => {
  const { isVisible, skipLinksRef } = useSkipLinks(links);

  if (!isVisible) return null;

  return (
    <div
      ref={skipLinksRef}
      className={`
        fixed top-0 left-0 z-[9999] bg-blue-600 text-white p-2 rounded-br-md
        transform transition-transform duration-200 ease-in-out
        ${isVisible ? 'translate-y-0' : '-translate-y-full'}
        ${className}
      `}
      role="navigation"
      aria-label="Skip navigation links"
    >
      <div className="flex flex-wrap gap-2">
        {links.map((link, index) => (
          <a
            key={`${link.href}-${index}`}
            href={link.href}
            className="
              px-3 py-1 bg-blue-700 hover:bg-blue-800 rounded text-sm font-medium
              focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600
              transition-colors duration-150
            "
            onClick={() => {
              // Smooth scroll to target if it exists
              const target = document.querySelector(link.href);
              if (target) {
                target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                // Set focus to target if it's focusable
                if (target instanceof HTMLElement && target.tabIndex >= 0) {
                  target.focus();
                }
              }
            }}
          >
            {link.label}
          </a>
        ))}
      </div>
    </div>
  );
};

export default SkipNavigation;
