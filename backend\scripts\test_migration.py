#!/usr/bin/env python3
"""
Test script for database migration from Chroma + SQLite3 to pgvector + Redis.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from ai_coding_agent.services.vector_db import VectorDBClient, EmbeddingNamespace
    from ai_coding_agent.services.redis_cache import get_cache
    from ai_coding_agent.config import settings
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the backend directory and have installed dependencies")
    sys.exit(1)


async def test_redis_connection():
    """Test Redis connection and basic operations."""
    print("🔄 Testing Redis connection...")
    
    try:
        cache = await get_cache()
        
        # Test basic operations
        test_key = "test_migration"
        test_value = {"message": "Redis migration test", "timestamp": "2024-01-01"}
        
        # Set value
        success = await cache.set(test_key, test_value, ttl=60)
        if not success:
            print("❌ Failed to set value in Redis")
            return False
        
        # Get value
        retrieved_value = await cache.get(test_key)
        if retrieved_value != test_value:
            print(f"❌ Retrieved value doesn't match: {retrieved_value} != {test_value}")
            return False
        
        # Test session management
        session_id = "test_session_123"
        session_data = {"user_id": "test_user", "project_id": "test_project"}
        
        await cache.set_session(session_id, session_data)
        retrieved_session = await cache.get_session(session_id)
        
        if retrieved_session != session_data:
            print(f"❌ Session data doesn't match: {retrieved_session} != {session_data}")
            return False
        
        # Get stats
        stats = await cache.get_stats()
        print(f"✅ Redis connected successfully!")
        print(f"   Redis version: {stats.get('redis_version', 'unknown')}")
        print(f"   Total keys: {stats.get('total_keys', 0)}")
        print(f"   Used memory: {stats.get('used_memory', '0B')}")
        
        # Cleanup
        await cache.delete(test_key)
        await cache.delete_session(session_id)
        
        return True
        
    except Exception as e:
        print(f"❌ Redis test failed: {e}")
        return False


async def test_pgvector_connection():
    """Test pgvector connection and basic operations."""
    print("🔄 Testing pgvector connection...")
    
    try:
        vector_db = VectorDBClient()
        
        # Test embedding generation (this should still work)
        test_text = "This is a test document for vector similarity search."
        embedding = await vector_db.generate_embedding(test_text, "nomic-embed-text:v1.5")
        
        if not embedding or len(embedding) == 0:
            print("❌ Failed to generate embedding")
            return False
        
        print(f"✅ Generated embedding with {len(embedding)} dimensions")
        
        # Test document addition
        test_doc_id = "test_migration_doc"
        test_content = "This is a test document for the migration from Chroma to pgvector."
        
        try:
            chunks = await vector_db.add_document(
                document_id=test_doc_id,
                content=test_content,
                namespace=EmbeddingNamespace.LTKB,
                metadata={"test": True, "migration": "chroma_to_pgvector"}
            )
            
            if not chunks:
                print("❌ Failed to add document chunks")
                return False
            
            print(f"✅ Added {len(chunks)} document chunks to pgvector")
            
            # Test similarity search
            search_results = await vector_db.search_similar(
                query="test document migration",
                namespace=EmbeddingNamespace.LTKB,
                limit=5
            )
            
            if not search_results:
                print("❌ No search results found")
                return False
            
            print(f"✅ Found {len(search_results)} similar documents")
            for i, result in enumerate(search_results[:2]):
                print(f"   Result {i+1}: similarity={result.similarity_score:.3f}")
            
            # Test document deletion
            deleted_count = await vector_db.delete_document(test_doc_id, EmbeddingNamespace.LTKB)
            print(f"✅ Deleted {deleted_count} document chunks")
            
            # Test collection stats
            stats = await vector_db.get_collection_stats(EmbeddingNamespace.LTKB)
            print(f"✅ Collection stats: {stats}")
            
            return True
            
        except Exception as e:
            print(f"❌ pgvector operations failed: {e}")
            print("   This might be expected if Supabase is not configured yet")
            print("   Run the setup_pgvector.sql script in your Supabase SQL editor first")
            return False
        
    except Exception as e:
        print(f"❌ pgvector test failed: {e}")
        return False


async def test_configuration():
    """Test configuration settings."""
    print("🔄 Testing configuration...")
    
    try:
        # Test database settings
        db_settings = settings.database
        print(f"✅ Database mode: {db_settings.mode}")
        
        # Test Supabase settings
        supabase_settings = settings.supabase
        if supabase_settings.url:
            print(f"✅ Supabase URL configured: {supabase_settings.url[:30]}...")
        else:
            print("⚠️  Supabase URL not configured")
        
        # Test Redis settings
        redis_settings = settings.redis
        print(f"✅ Redis URL: {redis_settings.url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """Run all migration tests."""
    print("🚀 Testing AI Coding Agent Database Migration")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Redis Connection", test_redis_connection),
        ("pgvector Connection", test_pgvector_connection),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("📊 Migration Test Results")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Migration is working correctly.")
        print("\nNext steps:")
        print("1. Install Redis and pgvector dependencies: pip install redis hiredis")
        print("2. Run setup_pgvector.sql in your Supabase SQL editor")
        print("3. Configure environment variables for Supabase and Redis")
    else:
        print("⚠️  Some tests failed. Check the configuration and dependencies.")
        print("\nTroubleshooting:")
        print("1. Make sure Redis is running (docker-compose up redis)")
        print("2. Configure Supabase environment variables")
        print("3. Run setup_pgvector.sql in Supabase SQL editor")
        print("4. Install missing dependencies: pip install redis hiredis asyncpg")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
