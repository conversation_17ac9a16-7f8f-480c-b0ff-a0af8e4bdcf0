#!/usr/bin/env python3
"""
Model Health and Verification Test for Phase A2

This script specifically tests and verifies that all configured models
are available and healthy in Ollama before running the main orchestrator tests.
"""

import asyncio
import sys
import json
import logging
from pathlib import Path

# Import path is handled by pyproject.toml configuration

from ai_coding_agent.orchestrator import EnhancedOrchestrator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_ollama_connection():
    """Test basic connection to Ollama."""
    print("🔌 Testing Ollama connection...")

    orchestrator = EnhancedOrchestrator()

    try:
        # Test basic Ollama API endpoint
        response = await orchestrator.client.get("http://localhost:11434/api/tags")
        if response.status_code == 200:
            models_data = response.json()
            available_models = [model['name'] for model in models_data.get('models', [])]
            print(f"✅ Ollama is running with {len(available_models)} models")
            print(f"Available models: {', '.join(available_models)}")
            return True, available_models
        else:
            print(f"❌ Ollama API returned status {response.status_code}")
            return False, []
    except Exception as e:
        print(f"❌ Failed to connect to Ollama: {e}")
        return False, []
    finally:
        await orchestrator.close()


async def test_configured_models():
    """Test all models configured in the orchestrator."""
    print("\n🧪 Testing configured models...")

    orchestrator = EnhancedOrchestrator()

    # Get all configured models
    configured_models = []
    for provider_config in orchestrator.config.config.get("providers", {}).values():
        configured_models.extend(provider_config.get("models", {}).keys())

    print(f"Configured models: {', '.join(configured_models)}")

    # Test each model individually with detailed logging
    results = {}
    for model in configured_models:
        print(f"\n🔍 Testing {model}...")
        try:
            start_time = asyncio.get_event_loop().time()
            is_healthy = await orchestrator.health_monitor.check_model_health(model, force_check=True)
            end_time = asyncio.get_event_loop().time()

            response_time = end_time - start_time
            results[model] = {
                "healthy": is_healthy,
                "response_time": response_time
            }

            if is_healthy:
                print(f"  ✅ Healthy (response time: {response_time:.2f}s)")
            else:
                print(f"  ❌ Unhealthy (response time: {response_time:.2f}s)")

        except Exception as e:
            print(f"  💥 Error: {e}")
            results[model] = {"healthy": False, "error": str(e)}

    await orchestrator.close()
    return results


async def warm_up_all_models():
    """Warm up all configured models."""
    print("\n🔥 Warming up all models...")

    orchestrator = EnhancedOrchestrator()

    # Get all configured models
    configured_models = []
    for provider_config in orchestrator.config.config.get("providers", {}).values():
        configured_models.extend(provider_config.get("models", {}).keys())

    # Warm up models
    warmup_results = await orchestrator.health_monitor.warm_up_models(configured_models)

    successful_warmups = sum(1 for success in warmup_results.values() if success)
    print(f"\n🎯 Warmup complete: {successful_warmups}/{len(configured_models)} models ready")

    await orchestrator.close()
    return warmup_results


async def test_model_routing():
    """Test model routing with healthy models only."""
    print("\n🎯 Testing model routing...")

    orchestrator = EnhancedOrchestrator()

    # Get healthy models first
    configured_models = []
    for provider_config in orchestrator.config.config.get("providers", {}).values():
        configured_models.extend(provider_config.get("models", {}).keys())

    healthy_models = await orchestrator.health_monitor.get_healthy_models(configured_models, force_check=True)
    print(f"Healthy models for routing: {healthy_models}")

    if not healthy_models:
        print("❌ No healthy models available for routing test")
        await orchestrator.close()
        return False

    # Test routing for different agents
    from ai_coding_agent.orchestrator import TaskType, TaskComplexity

    test_cases = [
        ("architect", TaskType.PLANNING, TaskComplexity.MODERATE),
        ("frontend", TaskType.UI_COMPONENT, TaskComplexity.SIMPLE),
        ("backend", TaskType.API_DESIGN, TaskComplexity.COMPLEX),
        ("shell", TaskType.SCRIPTING, TaskComplexity.SIMPLE),
    ]

    routing_success = True
    for agent, task_type, complexity in test_cases:
        try:
            selected_model = await orchestrator.route_model_by_task(agent, task_type, complexity)
            if selected_model in healthy_models:
                print(f"  ✅ {agent} → {selected_model} (healthy)")
            else:
                print(f"  ⚠️  {agent} → {selected_model} (potentially unhealthy)")
                routing_success = False
        except Exception as e:
            print(f"  ❌ {agent} routing failed: {e}")
            routing_success = False

    await orchestrator.close()
    return routing_success


async def generate_health_report():
    """Generate a comprehensive health report."""
    print("\n📊 Generating health report...")

    # Test connection
    ollama_connected, available_models = await test_ollama_connection()

    # Test configured models
    model_results = await test_configured_models()

    # Generate report
    report = {
        "timestamp": asyncio.get_event_loop().time(),
        "ollama_connected": ollama_connected,
        "available_models": available_models,
        "configured_models": list(model_results.keys()),
        "healthy_models": [model for model, result in model_results.items() if result.get("healthy", False)],
        "unhealthy_models": [model for model, result in model_results.items() if not result.get("healthy", False)],
        "model_details": model_results
    }

    # Print summary
    print(f"\n📋 HEALTH REPORT SUMMARY")
    print(f"Ollama Connection: {'✅ Connected' if ollama_connected else '❌ Failed'}")
    print(f"Available Models: {len(available_models)}")
    print(f"Configured Models: {len(report['configured_models'])}")
    print(f"Healthy Models: {len(report['healthy_models'])}")
    print(f"Unhealthy Models: {len(report['unhealthy_models'])}")

    if report['healthy_models']:
        print(f"✅ Healthy: {', '.join(report['healthy_models'])}")

    if report['unhealthy_models']:
        print(f"❌ Unhealthy: {', '.join(report['unhealthy_models'])}")

    # Save report
    report_file = Path("model_health_report.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"\n💾 Full report saved to: {report_file}")

    return report


async def main():
    """Run comprehensive model health verification."""
    print("🚀 Phase A2 Model Health Verification")
    print("=" * 60)

    try:
        # Generate health report
        report = await generate_health_report()

        # If we have healthy models, do additional tests
        if report['healthy_models']:
            print(f"\n✅ Found {len(report['healthy_models'])} healthy models. Running additional tests...")

            # Warm up models
            await warm_up_all_models()

            # Test routing
            routing_success = await test_model_routing()

            if routing_success:
                print("\n🎉 All tests passed! Your orchestrator is ready for Phase A2 testing.")
                print("\nRecommended next steps:")
                print("1. Run: python run_phase_a2_tests.py")
                print("2. Check the detailed test results")
                print("3. Monitor performance through analytics endpoints")
            else:
                print("\n⚠️ Some routing tests failed. Check model configurations.")

        else:
            print("\n❌ No healthy models found! Troubleshooting steps:")
            print("1. Ensure Ollama is running: ollama serve")
            print("2. Check model availability: ollama list")
            print("3. Try loading a model manually: ollama run llama3.2:3b")
            print("4. Verify network connectivity to localhost:11434")
            print("5. Check the model names in models_config.json match exactly")

    except Exception as e:
        print(f"\n💥 Verification failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
