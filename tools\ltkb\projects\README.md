# LTKB Projects Directory

This directory contains project-specific knowledge bases and memories
for active and completed projects.

## Structure

- `active/` - Currently active projects
- `completed/` - Archived completed projects
- `templates/` - Project templates and starter configs

## Usage

Each project gets its own subdirectory with:
- Project-specific embeddings and context
- Development history and decisions
- Custom configurations and rules
