import React from 'react';
import Skeleton from './Skeleton';

interface CardSkeletonProps {
  showImage?: boolean;
  showActions?: boolean;
  className?: string;
}

export const CardSkeleton: React.FC<CardSkeletonProps> = ({
  showImage = false,
  showActions = false,
  className = '',
}) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}>
      {showImage && (
        <Skeleton width="w-full" height="h-48" className="mb-4" />
      )}

      <Skeleton width="w-3/4" height="h-6" className="mb-3" />
      <Skeleton variant="text" lines={3} height="h-4" className="mb-4" />

      {showActions && (
        <div className="flex space-x-2">
          <Skeleton width="w-20" height="h-8" />
          <Skeleton width="w-24" height="h-8" />
        </div>
      )}
    </div>
  );
};

interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  className?: string;
}

export const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 4,
  className = '',
}) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="grid grid-cols-4 gap-4">
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton key={index} width="w-full" height="h-4" />
          ))}
        </div>
      </div>

      {/* Rows */}
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid grid-cols-4 gap-4">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <Skeleton key={colIndex} width="w-full" height="h-4" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface ListSkeletonProps {
  items?: number;
  showAvatar?: boolean;
  className?: string;
}

export const ListSkeleton: React.FC<ListSkeletonProps> = ({
  items = 5,
  showAvatar = false,
  className = '',
}) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md divide-y divide-gray-200 dark:divide-gray-700 ${className}`}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="p-4 flex items-center space-x-4">
          {showAvatar && (
            <Skeleton variant="circular" width="w-10" height="h-10" />
          )}
          <div className="flex-1">
            <Skeleton width="w-1/2" height="h-4" className="mb-2" />
            <Skeleton width="w-full" height="h-3" />
          </div>
        </div>
      ))}
    </div>
  );
};

interface FormSkeletonProps {
  fields?: number;
  showButtons?: boolean;
  className?: string;
}

export const FormSkeleton: React.FC<FormSkeletonProps> = ({
  fields = 4,
  showButtons = true,
  className = '',
}) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}>
      <div className="space-y-6">
        {Array.from({ length: fields }).map((_, index) => (
          <div key={index}>
            <Skeleton width="w-1/4" height="h-4" className="mb-2" />
            <Skeleton width="w-full" height="h-10" />
          </div>
        ))}

        {showButtons && (
          <div className="flex space-x-3 pt-4">
            <Skeleton width="w-24" height="h-10" />
            <Skeleton width="w-20" height="h-10" />
          </div>
        )}
      </div>
    </div>
  );
};
