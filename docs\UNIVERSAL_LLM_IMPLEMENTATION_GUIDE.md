# Universal LLM Management System - Implementation Guide

**Phase A2: Dynamic Multi-Provider LLM Switching**  
**Estimated Time**: 2-3 weeks  
**Priority**: High - Enables flexible AI model management  

## 🎯 **Overview**

Transform the AI Coding Agent to support dynamic switching between local and cloud LLM providers through an admin dashboard. This enables optimal model selection for each agent role based on task complexity, cost, and performance requirements.

## 📋 **Implementation Roadmap**

### **Week 1: Database & Backend Infrastructure**

#### **Day 1-2: Database Schema Setup**
```sql
-- File: backend/scripts/setup_universal_llm.sql
-- Run in Supabase SQL editor

-- LLM Providers table
CREATE TABLE llm_providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL CHECK (type IN ('local', 'cloud')),
    base_url TEXT,
    api_key_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Model configurations table
CREATE TABLE model_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_role TEXT NOT NULL CHECK (agent_role IN ('architect', 'frontend', 'backend', 'shell', 'issue_fix')),
    provider_id UUID REFERENCES llm_providers(id) ON DELETE CASCADE,
    model_name TEXT NOT NULL,
    display_name TEXT,
    api_key_env_var TEXT,
    custom_config JSONB DEFAULT '{}',
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id)
);

-- Model usage tracking
CREATE TABLE model_usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_config_id UUID REFERENCES model_configurations(id),
    agent_role TEXT NOT NULL,
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    response_time_ms INTEGER,
    cost_usd DECIMAL(10,6),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default providers
INSERT INTO llm_providers (name, type, base_url, api_key_required) VALUES
('ollama', 'local', 'http://ollama:11434', false),
('openai', 'cloud', 'https://api.openai.com/v1', true),
('anthropic', 'cloud', 'https://api.anthropic.com', true),
('openrouter', 'cloud', 'https://openrouter.ai/api/v1', true),
('groq', 'cloud', 'https://api.groq.com/openai/v1', true);

-- Insert default configurations
INSERT INTO model_configurations (agent_role, provider_id, model_name, display_name, is_primary) VALUES
('architect', (SELECT id FROM llm_providers WHERE name = 'ollama'), 'llama3.2:3b', 'Llama 3.2 3B (Local)', true),
('frontend', (SELECT id FROM llm_providers WHERE name = 'ollama'), 'starcoder2:3b', 'StarCoder2 3B (Local)', true),
('backend', (SELECT id FROM llm_providers WHERE name = 'ollama'), 'deepseek-coder:6.7b-instruct', 'DeepSeek Coder 6.7B (Local)', true),
('shell', (SELECT id FROM llm_providers WHERE name = 'ollama'), 'qwen2.5:3b', 'Qwen2.5 3B (Local)', true),
('issue_fix', (SELECT id FROM llm_providers WHERE name = 'ollama'), 'yi-coder:1.5b', 'Yi Coder 1.5B (Local)', true);
```

#### **Day 3-4: Universal LLM Service**
```python
# File: backend/src/ai_coding_agent/services/universal_llm_service.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import asyncio
import httpx
import time
from ..services.supabase import SupabaseService
from ..config import settings

class LLMProvider(ABC):
    """Abstract base class for all LLM providers."""
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate response from the LLM."""
        pass
    
    @abstractmethod
    async def is_available(self) -> bool:
        """Check if the provider is available."""
        pass

class OllamaProvider(LLMProvider):
    def __init__(self, base_url: str, model_name: str):
        self.base_url = base_url
        self.model_name = model_name
    
    async def generate(self, prompt: str, **kwargs) -> str:
        async with httpx.AsyncClient(timeout=300.0) as client:
            response = await client.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    **kwargs
                }
            )
            return response.json()["response"]
    
    async def is_available(self) -> bool:
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/api/tags")
                models = [model["name"] for model in response.json()["models"]]
                return self.model_name in models
        except:
            return False

class OpenAIProvider(LLMProvider):
    def __init__(self, api_key: str, model_name: str):
        self.api_key = api_key
        self.model_name = model_name
        self.base_url = "https://api.openai.com/v1"
    
    async def generate(self, prompt: str, **kwargs) -> str:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers={"Authorization": f"Bearer {self.api_key}"},
                json={
                    "model": self.model_name,
                    "messages": [{"role": "user", "content": prompt}],
                    **kwargs
                }
            )
            return response.json()["choices"][0]["message"]["content"]
    
    async def is_available(self) -> bool:
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(
                    f"{self.base_url}/models",
                    headers={"Authorization": f"Bearer {self.api_key}"}
                )
                return response.status_code == 200
        except:
            return False

class UniversalLLMService:
    def __init__(self):
        self.supabase = SupabaseService()
        self.providers = {
            'ollama': OllamaProvider,
            'openai': OpenAIProvider,
            'anthropic': AnthropicProvider,
            'openrouter': OpenRouterProvider,
            'groq': GroqProvider,
        }
        self._cache = {}
        self._cache_ttl = 300  # 5 minutes
    
    async def get_agent_llm(self, agent_role: str) -> LLMProvider:
        """Get configured LLM provider for specific agent role."""
        cache_key = f"agent_llm_{agent_role}"
        
        # Check cache first
        if cache_key in self._cache:
            cached_time, provider = self._cache[cache_key]
            if time.time() - cached_time < self._cache_ttl:
                return provider
        
        # Get configuration from database
        result = self.supabase.client.table("model_configurations").select(
            "*, llm_providers(*)"
        ).eq("agent_role", agent_role).eq("is_primary", True).eq("is_active", True).single().execute()
        
        if not result.data:
            raise ValueError(f"No model configured for agent role: {agent_role}")
        
        config = result.data
        provider_info = config["llm_providers"]
        
        # Get provider class
        provider_class = self.providers.get(provider_info["name"])
        if not provider_class:
            raise ValueError(f"Unsupported provider: {provider_info['name']}")
        
        # Initialize provider
        if provider_info["api_key_required"]:
            api_key = os.getenv(config["api_key_env_var"])
            if not api_key:
                raise ValueError(f"API key not found: {config['api_key_env_var']}")
            provider = provider_class(api_key=api_key, model_name=config["model_name"])
        else:
            provider = provider_class(
                base_url=provider_info["base_url"], 
                model_name=config["model_name"]
            )
        
        # Cache the provider
        self._cache[cache_key] = (time.time(), provider)
        return provider
    
    async def generate_response(self, agent_role: str, prompt: str, **kwargs) -> str:
        """Generate response using configured LLM for agent role."""
        start_time = time.time()
        
        try:
            llm = await self.get_agent_llm(agent_role)
            response = await llm.generate(prompt, **kwargs)
            
            # Log usage
            await self._log_usage(
                agent_role=agent_role,
                response_time_ms=int((time.time() - start_time) * 1000),
                success=True
            )
            
            return response
        except Exception as e:
            # Log error
            await self._log_usage(
                agent_role=agent_role,
                response_time_ms=int((time.time() - start_time) * 1000),
                success=False,
                error_message=str(e)
            )
            raise
    
    async def _log_usage(self, agent_role: str, response_time_ms: int, success: bool, error_message: str = None):
        """Log model usage for analytics."""
        try:
            self.supabase.client.table("model_usage_logs").insert({
                "agent_role": agent_role,
                "response_time_ms": response_time_ms,
                "success": success,
                "error_message": error_message
            }).execute()
        except Exception as e:
            logger.warning(f"Failed to log usage: {e}")
```

#### **Day 5-7: Admin API Endpoints**
```python
# File: backend/src/ai_coding_agent/routers/admin_llm.py
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any
from ..services.universal_llm_service import UniversalLLMService
from ..services.auth import get_current_admin_user

router = APIRouter(prefix="/admin/llm", tags=["admin-llm"])

@router.get("/providers")
async def get_providers():
    """Get all available LLM providers."""
    service = UniversalLLMService()
    result = service.supabase.client.table("llm_providers").select("*").eq("is_active", True).execute()
    return result.data

@router.get("/providers/{provider_id}/models")
async def get_provider_models(provider_id: str):
    """Get available models for a specific provider."""
    service = UniversalLLMService()
    provider = service.supabase.client.table("llm_providers").select("*").eq("id", provider_id).single().execute()
    
    if not provider.data:
        raise HTTPException(status_code=404, detail="Provider not found")
    
    # Implementation depends on provider type
    # Return list of available models
    
@router.get("/configurations")
async def get_model_configurations():
    """Get current model configurations for all agent roles."""
    service = UniversalLLMService()
    result = service.supabase.client.table("model_configurations").select(
        "*, llm_providers(*)"
    ).eq("is_primary", True).execute()
    return result.data

@router.put("/configurations/{agent_role}")
async def update_agent_model(
    agent_role: str,
    provider_id: str,
    model_name: str,
    current_user = Depends(get_current_admin_user)
):
    """Update model configuration for specific agent role."""
    service = UniversalLLMService()
    
    # Deactivate current primary
    service.supabase.client.table("model_configurations").update({
        "is_primary": False
    }).eq("agent_role", agent_role).eq("is_primary", True).execute()
    
    # Set new primary
    result = service.supabase.client.table("model_configurations").insert({
        "agent_role": agent_role,
        "provider_id": provider_id,
        "model_name": model_name,
        "is_primary": True,
        "created_by": current_user.id
    }).execute()
    
    # Clear cache
    service._cache.pop(f"agent_llm_{agent_role}", None)
    
    return {"message": f"Updated {agent_role} model to {model_name}"}

@router.get("/usage/analytics")
async def get_usage_analytics():
    """Get model usage analytics and cost tracking."""
    service = UniversalLLMService()
    # Implementation for usage analytics
    pass
```

### **Week 2: Frontend Admin Dashboard**

#### **Day 8-10: React Components**
```tsx
// File: frontend/src/components/admin/UniversalModelConfiguration.tsx
import React, { useState, useEffect } from 'react';
import { Card, Select, Button, Badge, Alert } from 'antd';

interface Provider {
  id: string;
  name: string;
  type: 'local' | 'cloud';
  api_key_required: boolean;
}

interface ModelConfig {
  agent_role: string;
  provider_id: string;
  model_name: string;
  display_name: string;
  llm_providers: Provider;
}

export const UniversalModelConfiguration: React.FC = () => {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [configs, setConfigs] = useState<ModelConfig[]>([]);
  const [availableModels, setAvailableModels] = useState<Record<string, string[]>>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadProviders();
    loadConfigurations();
  }, []);

  const loadProviders = async () => {
    const response = await fetch('/api/admin/llm/providers');
    const data = await response.json();
    setProviders(data);
  };

  const loadConfigurations = async () => {
    const response = await fetch('/api/admin/llm/configurations');
    const data = await response.json();
    setConfigs(data);
  };

  const loadModelsForProvider = async (providerId: string) => {
    try {
      const response = await fetch(`/api/admin/llm/providers/${providerId}/models`);
      const models = await response.json();
      setAvailableModels(prev => ({ ...prev, [providerId]: models }));
    } catch (error) {
      console.error('Failed to load models:', error);
    }
  };

  const updateAgentModel = async (agentRole: string, providerId: string, modelName: string) => {
    setLoading(true);
    try {
      await fetch(`/api/admin/llm/configurations/${agentRole}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider_id: providerId, model_name: modelName })
      });
      await loadConfigurations();
    } catch (error) {
      console.error('Failed to update model:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="universal-model-config">
      <h2>🤖 Universal LLM Configuration</h2>
      <p>Configure AI models for each agent role. Mix local and cloud providers for optimal performance and cost.</p>
      
      <div className="agent-configs">
        {configs.map(config => (
          <Card 
            key={config.agent_role} 
            title={`${config.agent_role.toUpperCase()} Agent`}
            className="agent-config-card"
          >
            <div className="config-row">
              <div className="provider-section">
                <label>Provider:</label>
                <Select
                  value={config.provider_id}
                  onChange={(providerId) => {
                    loadModelsForProvider(providerId);
                  }}
                  style={{ width: 200 }}
                >
                  {providers.map(provider => (
                    <Select.Option key={provider.id} value={provider.id}>
                      <Badge 
                        color={provider.type === 'local' ? 'green' : 'blue'} 
                        text={provider.name}
                      />
                      {provider.api_key_required && ' 🔑'}
                    </Select.Option>
                  ))}
                </Select>
              </div>

              <div className="model-section">
                <label>Model:</label>
                <Select
                  value={config.model_name}
                  onChange={(modelName) => updateAgentModel(config.agent_role, config.provider_id, modelName)}
                  style={{ width: 300 }}
                  loading={loading}
                >
                  {availableModels[config.provider_id]?.map(model => (
                    <Select.Option key={model} value={model}>{model}</Select.Option>
                  ))}
                </Select>
              </div>

              <div className="status-section">
                {config.llm_providers.api_key_required && (
                  <Badge 
                    status={config.api_key_env_var ? 'success' : 'error'} 
                    text={config.api_key_env_var ? 'API Key Configured' : 'API Key Missing'}
                  />
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};
```

### **Week 3: Integration & Testing**

#### **Day 11-14: Agent Integration**
- Update all agent classes to use `UniversalLLMService`
- Remove hardcoded model references
- Add fallback mechanisms
- Implement usage tracking

#### **Day 15-21: Testing & Optimization**
- End-to-end testing of model switching
- Performance benchmarking
- Cost optimization features
- Documentation and deployment

## 🎯 **Success Criteria**

- ✅ Admin dashboard allows real-time model switching
- ✅ Support for 5+ LLM providers (local + cloud)
- ✅ Zero-downtime model changes
- ✅ Usage analytics and cost tracking
- ✅ Automatic fallback mechanisms
- ✅ API key management and validation

## 🚀 **Next Steps After Implementation**

1. **Advanced Features**: A/B testing, auto-optimization
2. **Cost Management**: Budget alerts, usage limits
3. **Performance Monitoring**: Response time tracking
4. **Model Marketplace**: Easy addition of new providers

This implementation transforms the AI Coding Agent into a truly flexible, multi-provider AI platform!
