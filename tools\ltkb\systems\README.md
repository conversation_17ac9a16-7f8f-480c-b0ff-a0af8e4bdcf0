# LTKB Systems Directory

This directory contains system templates, patterns, and reusable components
that can be used across multiple projects.

## Structure
- `templates/` - Code templates and scaffolding
- `patterns/` - Common design patterns and best practices
- `components/` - Reusable components and modules
- `workflows/` - Standard development workflows

## Usage
These system resources are embedded into the vector database and can be
retrieved by AI agents during development tasks.
