#!/usr/bin/env python3
"""
Test script for container management API endpoints.

This script tests the container provisioning, status checking, command execution,
and cleanup functionality of the new container management system.
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional

import httpx
import structlog

# Add the backend source to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "backend" / "src"))

logger = structlog.get_logger()

class ContainerAPITester:
    """Test the container management API endpoints."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.auth_token: Optional[str] = None

    async def test_health_endpoint(self) -> bool:
        """Test the container service health endpoint."""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/containers/health")
            if response.status_code == 200:
                data = response.json()
                logger.info("Container service health check", status=data.get("status"),
                           docker_connected=data.get("docker_connected"),
                           active_containers=data.get("active_containers"))
                return data.get("status") == "healthy"
            else:
                logger.error("Health check failed", status_code=response.status_code,
                           response=response.text)
                return False
        except Exception as e:
            logger.error("Health check exception", error=str(e))
            return False

    async def test_backend_health(self) -> bool:
        """Test the main backend health endpoint."""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/health")
            if response.status_code == 200:
                data = response.json()
                logger.info("Backend health check", status=data.get("status"))
                return data.get("status") == "healthy"
            else:
                logger.error("Backend health check failed", status_code=response.status_code)
                return False
        except Exception as e:
            logger.error("Backend health check exception", error=str(e))
            return False

    async def test_container_provisioning_without_auth(self) -> bool:
        """Test container provisioning without authentication (should fail)."""
        try:
            payload = {
                "project_type": "react",
                "project_name": "test-project"
            }
            response = await self.client.post(
                f"{self.base_url}/api/v1/containers/provision",
                json=payload
            )

            if response.status_code == 401:
                logger.info("Container provisioning correctly requires authentication")
                return True
            elif response.status_code == 403:
                logger.info("Container provisioning correctly requires authentication (403 Forbidden)")
                return True
            elif response.status_code == 422:
                logger.info("Container provisioning validation error (expected without auth)")
                return True
            else:
                logger.warning("Unexpected response for unauthenticated request",
                             status_code=response.status_code, response=response.text)
                return False

        except Exception as e:
            logger.error("Container provisioning test exception", error=str(e))
            return False

    async def test_docker_connectivity(self) -> bool:
        """Test if Docker is accessible from the backend container."""
        try:
            # Test if we can access Docker from within the backend container
            response = await self.client.get(f"{self.base_url}/api/v1/containers/health")
            if response.status_code == 200:
                data = response.json()
                docker_connected = data.get("docker_connected", False)
                if docker_connected:
                    logger.info("Docker connectivity confirmed from backend")
                    return True
                else:
                    logger.error("Docker not accessible from backend container")
                    return False
            else:
                logger.error("Could not check Docker connectivity", status_code=response.status_code)
                return False
        except Exception as e:
            logger.error("Docker connectivity test exception", error=str(e))
            return False

    async def test_api_documentation(self) -> bool:
        """Test if the API documentation includes container endpoints."""
        try:
            response = await self.client.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                logger.info("API documentation accessible")
                # Check if container endpoints are documented
                response = await self.client.get(f"{self.base_url}/openapi.json")
                if response.status_code == 200:
                    openapi_spec = response.json()
                    paths = openapi_spec.get("paths", {})
                    container_endpoints = [path for path in paths.keys() if "/containers/" in path]
                    if container_endpoints:
                        logger.info("Container endpoints found in API spec",
                                  endpoints=container_endpoints)
                        return True
                    else:
                        logger.error("No container endpoints found in API spec")
                        return False
                else:
                    logger.error("Could not fetch OpenAPI spec")
                    return False
            else:
                logger.error("API documentation not accessible", status_code=response.status_code)
                return False
        except Exception as e:
            logger.error("API documentation test exception", error=str(e))
            return False

    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all container API tests."""
        results = {}

        logger.info("Starting container API tests")

        # Test 1: Backend health
        logger.info("Test 1: Backend health check")
        results["backend_health"] = await self.test_backend_health()

        # Test 2: Container service health
        logger.info("Test 2: Container service health check")
        results["container_health"] = await self.test_health_endpoint()

        # Test 3: Docker connectivity
        logger.info("Test 3: Docker connectivity")
        results["docker_connectivity"] = await self.test_docker_connectivity()

        # Test 4: API documentation
        logger.info("Test 4: API documentation")
        results["api_documentation"] = await self.test_api_documentation()

        # Test 5: Authentication requirement
        logger.info("Test 5: Authentication requirement")
        results["auth_requirement"] = await self.test_container_provisioning_without_auth()

        return results

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Main test function."""
    # Configure logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    tester = ContainerAPITester()

    try:
        results = await tester.run_all_tests()

        # Print summary
        print("\n" + "="*60)
        print("CONTAINER API TEST SUMMARY")
        print("="*60)

        passed = 0
        total = len(results)

        for test_name, result in results.items():
            status = "PASS" if result else "FAIL"
            print(f"{test_name:25} : {status}")
            if result:
                passed += 1

        print("-"*60)
        print(f"Tests passed: {passed}/{total}")
        print(f"Success rate: {(passed/total)*100:.1f}%")

        if passed == total:
            print("\n✅ All tests passed! Container management system is ready.")
            return 0
        else:
            print(f"\n❌ {total-passed} tests failed. Review the issues above.")
            return 1

    except Exception as e:
        logger.error("Test execution failed", error=str(e))
        return 1
    finally:
        await tester.close()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
