import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useNotifications } from '../contexts/AppContext';
import { useHealthCheck, useDashboardData } from '../hooks/useQueries';
import { CardSkeleton } from '../components/ui';

const DashboardPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { showError } = useNotifications();
  const navigate = useNavigate();

  // Using React Query hooks for data fetching with caching
  const {
    data: healthData,
    isLoading: healthLoading,
    error: healthError
  } = useHealthCheck();

  const {
    data: dashboardData,
    isLoading: dashboardLoading
  } = useDashboardData();

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    if (healthError) {
      showError(healthError instanceof Error ? healthError.message : 'Failed to check system health');
    }
  }, [healthError, showError]);

  if (healthLoading || dashboardLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-9 w-64 rounded-md mb-2"></div>
          <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-5 w-48 rounded-md"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <CardSkeleton />
          <CardSkeleton />
          <CardSkeleton />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <CardSkeleton />
          <CardSkeleton />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Welcome back, {user?.username}!
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Here's an overview of your AI Coding Agent dashboard
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">System Status</p>
              <p className={`text-2xl font-bold capitalize ${
                healthError ? 'text-red-600 dark:text-red-400' :
                healthData?.status === 'healthy' ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'
              }`}>
                {healthError ? 'Offline' : healthData?.status || 'Unknown'}
              </p>
            </div>
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
              healthError ? 'bg-red-100 dark:bg-red-900' :
              healthData?.status === 'healthy' ? 'bg-green-100 dark:bg-green-900' : 'bg-yellow-100 dark:bg-yellow-900'
            }`}>
              <svg className={`w-6 h-6 ${
                healthError ? 'text-red-600 dark:text-red-400' :
                healthData?.status === 'healthy' ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'
              }`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Account Type</p>
              <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                Free Tier
              </p>
            </div>
            <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Projects</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData?.stats?.totalProjects || 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            User Information
          </h3>
          <div className="space-y-3">
            <div>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Username:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{user?.username}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Email:</span>
              <span className="ml-2 text-gray-900 dark:text-white">{user?.email}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Member since:</span>
              <span className="ml-2 text-gray-900 dark:text-white">
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h3>
          <div className="space-y-3">
            <button className="w-full text-left px-4 py-3 bg-primary-50 dark:bg-primary-900 hover:bg-primary-100 dark:hover:bg-primary-800 rounded-lg text-primary-700 dark:text-primary-300 font-medium transition-colors">
              Create New Project
            </button>
            <button className="w-full text-left px-4 py-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg text-gray-700 dark:text-gray-300 font-medium transition-colors">
              View API Documentation
            </button>
            <button className="w-full text-left px-4 py-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg text-gray-700 dark:text-gray-300 font-medium transition-colors">
              Account Settings
            </button>
          </div>
        </div>
      </div>

      <div className="mt-8 bg-primary-50 dark:bg-primary-900 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Getting Started
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Welcome to your AI Coding Agent dashboard! This is Phase 3 of the project roadmap.
          Future phases will include AI agent integration, code analysis features, and advanced project management.
        </p>
        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <li>• ✅ Phase 1: Core Backend (User Auth, Database)</li>
          <li>• ✅ Phase 2: API Development (RESTful endpoints)</li>
          <li>• ✅ Phase 3: Basic Frontend (React, Authentication UI)</li>
          <li>• 🔄 Phase 4: AI Agent Integration (Coming Soon)</li>
          <li>• 🔄 Phase 5: Advanced Features (Coming Soon)</li>
        </ul>
      </div>
    </div>
  );
};

export default DashboardPage;
