"""
Cloud AI provider integrations for flexible model routing.

Supports:
- OpenAI GPT models
- Anthropic Claude models  
- Google Gemini models
- Unified interface for local and cloud models
"""

import json
import httpx
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from abc import ABC, abstractmethod

from pydantic import BaseModel


class CloudModelResponse(BaseModel):
    """Standardized response from cloud AI models."""
    content: str
    model: str
    provider: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class CloudAIProvider(ABC):
    """Abstract base class for cloud AI providers."""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None):
        self.api_key = api_key
        self.base_url = base_url
        self.client = httpx.AsyncClient()
    
    @abstractmethod
    async def generate(self, prompt: str, model: str, **kwargs) -> CloudModelResponse:
        """Generate response from the AI model."""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """Test if the provider is accessible."""
        pass
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


class OpenAIProvider(CloudAIProvider):
    """OpenAI API provider."""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None):
        super().__init__(api_key, base_url or "https://api.openai.com/v1")
        self.client.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    async def generate(self, prompt: str, model: str, **kwargs) -> CloudModelResponse:
        """Generate response using OpenAI API."""
        try:
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json={
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": kwargs.get("max_tokens", 1000),
                    "temperature": kwargs.get("temperature", 0.7)
                },
                timeout=30.0
            )
            
            if response.status_code == 200:
                data = response.json()
                return CloudModelResponse(
                    content=data["choices"][0]["message"]["content"],
                    model=model,
                    provider="openai",
                    usage=data.get("usage"),
                    metadata={"response_id": data.get("id")}
                )
            else:
                raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"OpenAI generation failed: {str(e)}")
    
    async def test_connection(self) -> bool:
        """Test OpenAI API connection."""
        try:
            response = await self.client.get(
                f"{self.base_url}/models",
                timeout=10.0
            )
            return response.status_code == 200
        except:
            return False


class AnthropicProvider(CloudAIProvider):
    """Anthropic Claude API provider."""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None):
        super().__init__(api_key, base_url or "https://api.anthropic.com")
        self.client.headers.update({
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        })
    
    async def generate(self, prompt: str, model: str, **kwargs) -> CloudModelResponse:
        """Generate response using Anthropic API."""
        try:
            response = await self.client.post(
                f"{self.base_url}/v1/messages",
                json={
                    "model": model,
                    "max_tokens": kwargs.get("max_tokens", 1000),
                    "messages": [{"role": "user", "content": prompt}]
                },
                timeout=30.0
            )
            
            if response.status_code == 200:
                data = response.json()
                return CloudModelResponse(
                    content=data["content"][0]["text"],
                    model=model,
                    provider="anthropic",
                    usage=data.get("usage"),
                    metadata={"message_id": data.get("id")}
                )
            else:
                raise Exception(f"Anthropic API error: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"Anthropic generation failed: {str(e)}")
    
    async def test_connection(self) -> bool:
        """Test Anthropic API connection."""
        try:
            # Anthropic doesn't have a simple health check, so we'll try a minimal request
            response = await self.client.post(
                f"{self.base_url}/v1/messages",
                json={
                    "model": "claude-3-haiku-20240307",
                    "max_tokens": 1,
                    "messages": [{"role": "user", "content": "test"}]
                },
                timeout=10.0
            )
            return response.status_code == 200
        except:
            return False


class GoogleProvider(CloudAIProvider):
    """Google Gemini API provider."""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None):
        super().__init__(api_key, base_url or "https://generativelanguage.googleapis.com")
    
    async def generate(self, prompt: str, model: str, **kwargs) -> CloudModelResponse:
        """Generate response using Google Gemini API."""
        try:
            response = await self.client.post(
                f"{self.base_url}/v1/models/{model}:generateContent?key={self.api_key}",
                json={
                    "contents": [{"parts": [{"text": prompt}]}],
                    "generationConfig": {
                        "maxOutputTokens": kwargs.get("max_tokens", 1000),
                        "temperature": kwargs.get("temperature", 0.7)
                    }
                },
                timeout=30.0
            )
            
            if response.status_code == 200:
                data = response.json()
                content = data["candidates"][0]["content"]["parts"][0]["text"]
                return CloudModelResponse(
                    content=content,
                    model=model,
                    provider="google",
                    usage=data.get("usageMetadata"),
                    metadata={"candidate_count": len(data.get("candidates", []))}
                )
            else:
                raise Exception(f"Google API error: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"Google generation failed: {str(e)}")
    
    async def test_connection(self) -> bool:
        """Test Google API connection."""
        try:
            response = await self.client.get(
                f"{self.base_url}/v1/models?key={self.api_key}",
                timeout=10.0
            )
            return response.status_code == 200
        except:
            return False


class UnifiedAIProvider:
    """Unified interface for both local (Ollama) and cloud AI providers."""
    
    def __init__(self):
        self.providers: Dict[str, CloudAIProvider] = {}
        self.ollama_host = "http://localhost:11434"
        self.ollama_client = httpx.AsyncClient()
    
    def add_cloud_provider(self, name: str, provider: CloudAIProvider):
        """Add a cloud provider."""
        self.providers[name] = provider
    
    async def generate(self, prompt: str, model: str, provider: str = "ollama", **kwargs) -> CloudModelResponse:
        """Generate response from specified provider and model."""
        if provider == "ollama":
            return await self._generate_ollama(prompt, model, **kwargs)
        elif provider in self.providers:
            return await self.providers[provider].generate(prompt, model, **kwargs)
        else:
            raise ValueError(f"Unknown provider: {provider}")
    
    async def _generate_ollama(self, prompt: str, model: str, **kwargs) -> CloudModelResponse:
        """Generate response using local Ollama."""
        try:
            response = await self.ollama_client.post(
                f"{self.ollama_host}/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": kwargs.get("temperature", 0.7),
                        "num_predict": kwargs.get("max_tokens", 1000)
                    }
                },
                timeout=60.0
            )
            
            if response.status_code == 200:
                data = response.json()
                return CloudModelResponse(
                    content=data["response"],
                    model=model,
                    provider="ollama",
                    metadata={
                        "eval_count": data.get("eval_count"),
                        "eval_duration": data.get("eval_duration")
                    }
                )
            else:
                raise Exception(f"Ollama error: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"Ollama generation failed: {str(e)}")
    
    async def test_provider(self, provider: str) -> bool:
        """Test if a provider is available."""
        if provider == "ollama":
            try:
                response = await self.ollama_client.get(f"{self.ollama_host}/api/tags", timeout=5.0)
                return response.status_code == 200
            except:
                return False
        elif provider in self.providers:
            return await self.providers[provider].test_connection()
        else:
            return False
    
    async def get_available_models(self, provider: str) -> List[str]:
        """Get available models for a provider."""
        if provider == "ollama":
            try:
                response = await self.ollama_client.get(f"{self.ollama_host}/api/tags", timeout=5.0)
                if response.status_code == 200:
                    data = response.json()
                    return [model["name"] for model in data.get("models", [])]
            except:
                pass
            return []
        else:
            # For cloud providers, return predefined model lists
            model_lists = {
                "openai": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
                "anthropic": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku", "claude-2.1"],
                "google": ["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro"]
            }
            return model_lists.get(provider, [])
    
    async def close(self):
        """Close all providers."""
        await self.ollama_client.aclose()
        for provider in self.providers.values():
            await provider.close()


# Global unified provider instance
unified_provider = UnifiedAIProvider()


async def initialize_cloud_providers():
    """Initialize cloud providers from configuration."""
    config_path = Path(__file__).parent.parent / "models_config.json"
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        providers_config = config.get("providers", {})
        
        # Initialize OpenAI
        if "openai" in providers_config and providers_config["openai"].get("enabled"):
            api_key = providers_config["openai"].get("api_key")
            if api_key:
                openai_provider = OpenAIProvider(api_key)
                unified_provider.add_cloud_provider("openai", openai_provider)
        
        # Initialize Anthropic
        if "anthropic" in providers_config and providers_config["anthropic"].get("enabled"):
            api_key = providers_config["anthropic"].get("api_key")
            if api_key:
                anthropic_provider = AnthropicProvider(api_key)
                unified_provider.add_cloud_provider("anthropic", anthropic_provider)
        
        # Initialize Google
        if "google" in providers_config and providers_config["google"].get("enabled"):
            api_key = providers_config["google"].get("api_key")
            if api_key:
                google_provider = GoogleProvider(api_key)
                unified_provider.add_cloud_provider("google", google_provider)
                
    except Exception as e:
        print(f"Warning: Failed to initialize cloud providers: {e}")


# Initialize providers on module import
import asyncio
try:
    asyncio.create_task(initialize_cloud_providers())
except RuntimeError:
    # Handle case where no event loop is running
    pass
