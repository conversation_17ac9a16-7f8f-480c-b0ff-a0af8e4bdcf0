#!/bin/bash

# Enhanced Container Build and Verification Script
# This script builds containers with comprehensive verification

set -e

echo "🚀 AI Coding Agent - Enhanced Container Build & Verification"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print step headers
print_step() {
    echo ""
    echo -e "${BLUE}🔧 $1${NC}"
    echo "$(printf '%.0s-' {1..50})"
}

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1 successful${NC}"
    else
        echo -e "${RED}❌ $1 failed${NC}"
        exit 1
    fi
}

print_step "Step 1: Pre-Build Verification"
if [ -f "scripts/pre_build_verification.sh" ]; then
    bash scripts/pre_build_verification.sh
    check_success "Pre-build verification"
else
    echo -e "${YELLOW}⚠️ Pre-build verification script not found, continuing...${NC}"
fi

print_step "Step 2: Clean Previous Containers"
echo "Stopping and removing existing containers..."
docker-compose down --remove-orphans 2>/dev/null || true
docker system prune -f --volumes 2>/dev/null || true
check_success "Container cleanup"

print_step "Step 3: Build Backend Container"
echo "Building backend with no cache..."
docker-compose build --no-cache backend
check_success "Backend build"

print_step "Step 4: Build Frontend Container"
echo "Building frontend with no cache..."
docker-compose build --no-cache frontend
check_success "Frontend build"

print_step "Step 5: Build Supporting Services"
echo "Building remaining services..."
docker-compose build --no-cache
check_success "All services build"

print_step "Step 6: Start Services"
echo "Starting all services..."
docker-compose up -d
check_success "Services startup"

print_step "Step 7: Wait for Services to Initialize"
echo "Waiting 30 seconds for services to fully initialize..."
sleep 30

print_step "Step 8: Verify Container Health"
echo "Checking container health status..."
docker-compose ps

print_step "Step 9: Run Comprehensive Verification"
if [ -f "scripts/verify_container_files.sh" ]; then
    bash scripts/verify_container_files.sh
    check_success "Container verification"
else
    echo -e "${YELLOW}⚠️ Container verification script not found${NC}"
fi

print_step "Step 10: Quick Functionality Tests"

# Test backend health endpoint
echo "Testing backend health endpoint..."
if curl -f http://localhost:8000/api/v1/health 2>/dev/null; then
    echo -e "${GREEN}✅ Backend health check passed${NC}"
else
    echo -e "${YELLOW}⚠️ Backend health check failed (may still be starting)${NC}"
fi

# Test frontend accessibility
echo "Testing frontend accessibility..."
if curl -f http://localhost:3000 2>/dev/null; then
    echo -e "${GREEN}✅ Frontend accessibility check passed${NC}"
else
    echo -e "${YELLOW}⚠️ Frontend accessibility check failed (may still be starting)${NC}"
fi

print_step "Build and Verification Complete!"
echo ""
echo -e "${GREEN}🎉 Container build and verification completed successfully!${NC}"
echo ""
echo "Services running:"
echo "- Backend API: http://localhost:8000"
echo "- Frontend App: http://localhost:3000"
echo "- PostgreSQL: localhost:5432"
echo "- Redis: localhost:6379"
echo "- Ollama: http://localhost:11434"
echo ""
echo "Useful commands:"
echo "- View logs: docker-compose logs [service-name]"
echo "- Restart service: docker-compose restart [service-name]"
echo "- Stop all: docker-compose down"
echo "- Rebuild specific service: docker-compose build --no-cache [service-name]"
echo ""
echo "============================================================"
