# PowerShell Enhanced Container Build and Verification Script
# This script builds containers with comprehensive verification
# Usage: .\scripts\Build-And-Verify.ps1

param(
    [switch]$SkipPreCheck,
    [switch]$Quick
)

Write-Host "🚀 AI Coding Agent - Enhanced Container Build & Verification" -ForegroundColor Cyan
Write-Host "============================================================" -ForegroundColor Cyan

# Function to print step headers
function Write-Step {
    param([string]$Message)
    Write-Host ""
    Write-Host "🔧 $Message" -ForegroundColor Blue
    Write-Host ("-" * 50) -ForegroundColor Blue
}

# Function to check command success
function Test-CommandSuccess {
    param(
        [string]$Description,
        [int]$ExitCode = $LASTEXITCODE
    )
    
    if ($ExitCode -eq 0) {
        Write-Host "✅ $Description successful" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Description failed (Exit Code: $ExitCode)" -ForegroundColor Red
        return $false
    }
}

# Check if Docker is available
try {
    docker version >$null 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker is not running. Please start Docker Desktop and try again." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Docker is not installed or not in PATH." -ForegroundColor Red
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/" -ForegroundColor Yellow
    exit 1
}

if (!$SkipPreCheck) {
    Write-Step "Step 1: Pre-Build Verification"
    if (Test-Path "scripts\Verify-PreBuild.ps1") {
        & ".\scripts\Verify-PreBuild.ps1"
        if (!$?) {
            Write-Host "❌ Pre-build verification failed. Fix issues before continuing." -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "⚠️ Pre-build verification script not found, continuing..." -ForegroundColor Yellow
    }
}

Write-Step "Step 2: Clean Previous Containers"
Write-Host "Stopping and removing existing containers..."
try {
    docker-compose down --remove-orphans 2>$null
    Test-CommandSuccess "Container cleanup"
} catch {
    Write-Host "⚠️ Container cleanup had issues, continuing..." -ForegroundColor Yellow
}

Write-Step "Step 3: Build Backend Container"
Write-Host "Building backend with no cache..."
docker-compose build --no-cache backend
if (!(Test-CommandSuccess "Backend build")) {
    Write-Host "❌ Backend build failed. Check the output above for errors." -ForegroundColor Red
    exit 1
}

Write-Step "Step 4: Build Frontend Container"
Write-Host "Building frontend with no cache..."
docker-compose build --no-cache frontend
if (!(Test-CommandSuccess "Frontend build")) {
    Write-Host "❌ Frontend build failed. Check the output above for errors." -ForegroundColor Red
    exit 1
}

Write-Step "Step 5: Build Supporting Services"
Write-Host "Building remaining services..."
docker-compose build --no-cache
Test-CommandSuccess "All services build"

Write-Step "Step 6: Start Services"
Write-Host "Starting all services..."
docker-compose up -d
if (!(Test-CommandSuccess "Services startup")) {
    Write-Host "❌ Services startup failed. Check logs with: docker-compose logs" -ForegroundColor Red
    exit 1
}

Write-Step "Step 7: Wait for Services to Initialize"
Write-Host "Waiting 30 seconds for services to fully initialize..."
Start-Sleep -Seconds 30

Write-Step "Step 8: Verify Container Health"
Write-Host "Checking container health status..."
docker-compose ps

Write-Step "Step 9: Run Comprehensive Verification"
if (Test-Path "scripts\Verify-Containers.ps1") {
    if ($Quick) {
        & ".\scripts\Verify-Containers.ps1" -Quick
    } else {
        & ".\scripts\Verify-Containers.ps1"
    }
    Test-CommandSuccess "Container verification"
} else {
    Write-Host "⚠️ Container verification script not found" -ForegroundColor Yellow
}

Write-Step "Step 10: Quick Functionality Tests"

# Test backend health endpoint
Write-Host "Testing backend health endpoint..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/health" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Backend health check passed" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Backend health check returned status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Backend health check failed (may still be starting)" -ForegroundColor Yellow
}

# Test frontend accessibility
Write-Host "Testing frontend accessibility..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Frontend accessibility check passed" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Frontend returned status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Frontend accessibility check failed (may still be starting)" -ForegroundColor Yellow
}

Write-Step "Build and Verification Complete!"
Write-Host ""
Write-Host "🎉 Container build and verification completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Services running:" -ForegroundColor Cyan
Write-Host "- Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "- Frontend App: http://localhost:3000" -ForegroundColor White
Write-Host "- PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "- Redis: localhost:6379" -ForegroundColor White
Write-Host "- Ollama: http://localhost:11434" -ForegroundColor White
Write-Host ""
Write-Host "Useful commands:" -ForegroundColor Cyan
Write-Host "- View logs: docker-compose logs [service-name]" -ForegroundColor White
Write-Host "- Restart service: docker-compose restart [service-name]" -ForegroundColor White
Write-Host "- Stop all: docker-compose down" -ForegroundColor White
Write-Host "- Rebuild specific service: docker-compose build --no-cache [service-name]" -ForegroundColor White
Write-Host ""
Write-Host "============================================================" -ForegroundColor Cyan
