# AI Coding Agent Backend Container - ROBUST BUILD VERSION
# This Dockerfile includes comprehensive error handling and system dependencies

# Development stage
FROM python:3.11-slim as development

# Set environment variables for Python and application
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app:/app/src \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONIOENCODING=utf-8 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies with comprehensive package list
# This addresses the most common build failures
RUN apt-get update && apt-get install -y --no-install-recommends \
    # Basic utilities
    curl \
    wget \
    git \
    # Build tools (required for psycopg2 and other compiled packages)
    gcc \
    g++ \
    make \
    # PostgreSQL development headers (CRITICAL for psycopg2-binary)
    libpq-dev \
    # Python development headers
    python3-dev \
    # Additional libraries that some Python packages need
    libffi-dev \
    libssl-dev \
    # Cleanup
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && echo "✅ System dependencies installed successfully"

# Create non-root user for development (UID 1000 for volume mount compatibility)
RUN groupadd -g 1000 appuser && \
    useradd -r -u 1000 -g appuser appuser && \
    echo "✅ User appuser created successfully"

# Set work directory
WORKDIR /app

# Upgrade pip to latest version (prevents many installation issues)
RUN pip install --upgrade pip setuptools wheel && \
    echo "✅ pip upgraded successfully"

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-dev.txt .

# Verify requirements files exist and are readable
RUN echo "📋 Checking requirements files..." && \
    ls -la requirements*.txt && \
    echo "📋 Requirements.txt contents:" && \
    head -10 requirements.txt && \
    echo "✅ Requirements files verified"

# Install Python dependencies with error handling
RUN echo "📦 Installing Python dependencies..." && \
    pip install --no-cache-dir --timeout=300 -r requirements.txt && \
    echo "✅ Production dependencies installed" && \
    pip install --no-cache-dir --timeout=300 -r requirements-dev.txt && \
    echo "✅ Development dependencies installed"

# Verify critical packages are installed
RUN echo "🔍 Verifying critical packages..." && \
    python -c "import fastapi; print('✅ FastAPI installed')" && \
    python -c "import psycopg2; print('✅ psycopg2 installed')" && \
    python -c "import sqlalchemy; print('✅ SQLAlchemy installed')" && \
    echo "✅ All critical packages verified"

# Copy application code with verification
COPY src/ ./src/
RUN echo "📁 Verifying src directory structure..." && \
    ls -la src/ && \
    ls -la src/ai_coding_agent/ && \
    test -f src/ai_coding_agent/__init__.py && \
    test -f src/ai_coding_agent/main.py && \
    echo "✅ Source code structure verified"

# Create symlink for direct ai_coding_agent access
RUN ln -sf /app/src/ai_coding_agent /app/ai_coding_agent && \
    echo "✅ Module symlink created: $(ls -la /app/ai_coding_agent)"

# Copy configuration, scripts, and tests with verification
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY tests/ ./tests/

RUN echo "📁 Verifying copied directories..." && \
    ls -la config/ && \
    ls -la scripts/ && \
    ls -la tests/ && \
    echo "✅ All directories copied successfully"

# Create necessary directories and set permissions
RUN mkdir -p logs uploads user-projects && \
    chown -R appuser:appuser /app && \
    echo "✅ Directories created and permissions set"

# Test Python import before switching users
RUN echo "🧪 Testing Python imports..." && \
    cd /app && \
    python -c "import sys; sys.path.insert(0, '/app/src'); import ai_coding_agent; print('✅ ai_coding_agent module imports successfully')" && \
    echo "✅ Python import test passed"

# Switch to non-root user for development
USER appuser

# Expose port
EXPOSE 8000

# Development command with hot reload
CMD ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM python:3.11-slim as production

# Set environment variables for production
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app:/app/src \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONIOENCODING=utf-8 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies (minimal set for production)
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    # PostgreSQL client libraries (required for psycopg2 at runtime)
    libpq5 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security (UID 1000 for consistency)
RUN groupadd -g 1000 appuser && \
    useradd -r -u 1000 -g appuser appuser

# Set work directory
WORKDIR /app

# Copy requirements and upgrade pip
COPY requirements.txt .
RUN pip install --upgrade pip setuptools wheel

# Install Python dependencies (production only)
RUN pip install --no-cache-dir --timeout=300 -r requirements.txt

# Copy application code from development stage or directly
COPY src/ ./src/
RUN ln -sf /app/src/ai_coding_agent /app/ai_coding_agent

# Copy configuration, scripts, and tests
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY tests/ ./tests/

# Create necessary directories and set permissions
RUN mkdir -p logs uploads user-projects && \
    chown -R appuser:appuser /app

# Test imports in production stage
RUN python -c "import sys; sys.path.insert(0, '/app/src'); import ai_coding_agent; print('✅ Production: ai_coding_agent module imports successfully')"

# Switch to non-root user
USER appuser

# Health check with proper endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# Expose port
EXPOSE 8000

# Production command without reload
CMD ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000"]
