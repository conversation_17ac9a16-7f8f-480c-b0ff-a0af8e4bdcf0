"""
Vector Database Infrastructure for LTKB and STPM.

This module provides the vector database infrastructure using pgvector (via Supabase)
for storing and retrieving embeddings for both LTKB (Long-Term Knowledge Base) and
STPM (Short-Term Project Memory).
"""

import os
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
from enum import Enum
import json

try:
    import asyncpg
    import numpy as np
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False
    asyncpg = None
    np = None

import httpx
from pydantic import BaseModel, Field, ConfigDict

from ..config import settings
from .supabase import SupabaseService

logger = logging.getLogger(__name__)


class EmbeddingNamespace(str, Enum):
    """Namespaces for different types of embeddings."""
    LTKB = "ltkb"           # Long-term knowledge base
    STPM = "stpm"           # Short-term project memory
    SYSTEMS = "systems"     # System templates and patterns
    PROJECTS = "projects"   # Project-specific knowledge


class DocumentChunk(BaseModel):
    """Represents a document chunk for embedding."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    content: str = Field(description="Chunk content")
    source_document_id: str = Field(description="Source document ID")
    chunk_index: int = Field(description="Index of chunk within document")

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict)
    namespace: EmbeddingNamespace = Field(description="Embedding namespace")

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)

    model_config = ConfigDict(use_enum_values=True)


class SearchResult(BaseModel):
    """Vector search result."""

    chunk: DocumentChunk
    similarity_score: float = Field(ge=0.0, le=1.0, description="Similarity score")
    distance: float = Field(description="Vector distance")


class EmbeddingConfig(BaseModel):
    """Configuration for embedding models."""

    model_name: str
    chunk_size: int
    chunk_overlap: int
    description: str

    model_config = ConfigDict(protected_namespaces=())


class VectorDBClient:
    """Vector database client using pgvector via Supabase."""

    def __init__(self, supabase_service: Optional[SupabaseService] = None):
        if not ASYNCPG_AVAILABLE:
            raise RuntimeError("asyncpg not available. Install with: pip install asyncpg")

        self.supabase = supabase_service or SupabaseService()

        # HTTP client for embedding requests
        self.http_client = httpx.AsyncClient(timeout=60.0)

        # Initialize pgvector extension
        self._initialized = False

        # Load embedding configurations
        self.embedding_configs = self._load_embedding_configs()

        logger.info("VectorDBClient initialized with pgvector via Supabase")

    async def _initialize_pgvector(self):
        """Initialize pgvector extension and create embeddings table."""
        if self._initialized:
            return

        try:
            # Enable pgvector extension
            await self.supabase.client.rpc('enable_pgvector').execute()

            # Create embeddings table if it doesn't exist
            await self.supabase.client.rpc('create_embeddings_table').execute()

            self._initialized = True
            logger.info("pgvector extension and embeddings table initialized")
        except Exception as e:
            logger.warning(f"pgvector initialization may have failed (might already exist): {e}")
            self._initialized = True

    def _load_embedding_configs(self) -> Dict[str, EmbeddingConfig]:
        """Load embedding model configurations."""
        # Try multiple paths for models_config.json
        config_paths = [
            Path(__file__).parent / "models_config.json",
            Path(__file__).parent.parent / "models_config.json",
            Path(__file__).parent.parent.parent.parent / "src" / "ai_coding_agent" / "models_config.json"
        ]

        for config_path in config_paths:
            try:
                if config_path.exists():
                    import json
                    with open(config_path, 'r') as f:
                        config = json.load(f)

                    embedding_models = config.get("embedding_models", {})

                    configs = {}
                    for key, model_config in embedding_models.items():
                        configs[key] = EmbeddingConfig(
                            model_name=model_config["model"],
                            chunk_size=model_config["chunk_size"],
                            chunk_overlap=model_config["chunk_overlap"],
                            description=model_config["description"]
                        )

                    return configs
            except Exception as e:
                logger.debug(f"Failed to load config from {config_path}: {e}")
                continue

        logger.warning("Could not load embedding configs from any path, using defaults")
        # Return default configs
        return {
            "ltkb": EmbeddingConfig(
                model_name="nomic-embed-text:v1.5",
                chunk_size=2048,
                chunk_overlap=256,
                description="Long-context embeddings for LTKB"
            ),
            "stpm": EmbeddingConfig(
                model_name="mxbai-embed-large",
                chunk_size=512,
                chunk_overlap=64,
                description="Fast retrieval embeddings for STPM"
            )
        }

    async def generate_embedding(self, text: str, model_name: str) -> List[float]:
        """Generate embedding for text using specified model."""
        try:
            ollama_url = settings.ai.ollama_host
            response = await self.http_client.post(
                f"{ollama_url}/api/embeddings",
                json={
                    "model": model_name,
                    "prompt": text
                }
            )

            if response.status_code != 200:
                raise Exception(f"Ollama embedding request failed: {response.status_code}")

            result = response.json()
            return result.get("embedding", [])

        except Exception as e:
            logger.error(f"Error generating embedding with {model_name}: {e}")
            raise

    def chunk_document(self, content: str, config: EmbeddingConfig) -> List[str]:
        """Chunk document content based on configuration."""
        chunks = []
        chunk_size = config.chunk_size
        overlap = config.chunk_overlap

        # Simple character-based chunking
        start = 0
        while start < len(content):
            end = start + chunk_size
            chunk = content[start:end]

            # Try to break at word boundaries
            if end < len(content) and not content[end].isspace():
                last_space = chunk.rfind(' ')
                if last_space > chunk_size * 0.7:  # Don't make chunks too small
                    chunk = chunk[:last_space]
                    end = start + last_space

            chunks.append(chunk.strip())
            start = end - overlap

            if start >= len(content):
                break

        return chunks

    async def add_document(
        self,
        document_id: str,
        content: str,
        namespace: EmbeddingNamespace,
        metadata: Optional[Dict[str, Any]] = None,
        embedding_type: str = "ltkb"
    ) -> List[DocumentChunk]:
        """Add a document to the vector database using pgvector."""

        await self._initialize_pgvector()

        if embedding_type not in self.embedding_configs:
            raise ValueError(f"Unknown embedding type: {embedding_type}")

        config = self.embedding_configs[embedding_type]

        # Chunk the document
        chunks_text = self.chunk_document(content, config)

        # Create document chunks
        document_chunks = []

        for i, chunk_text in enumerate(chunks_text):
            # Create chunk object
            chunk = DocumentChunk(
                content=chunk_text,
                source_document_id=document_id,
                chunk_index=i,
                namespace=namespace,
                metadata=metadata or {}
            )

            # Generate embedding
            embedding = await self.generate_embedding(chunk_text, config.model_name)

            # Insert into Supabase with pgvector
            embedding_data = {
                "id": chunk.id,
                "content": chunk_text,
                "embedding": embedding,
                "source_document_id": document_id,
                "chunk_index": i,
                "namespace": namespace.value,
                "embedding_type": embedding_type,
                "metadata": {
                    "created_at": chunk.created_at.isoformat(),
                    **chunk.metadata
                },
                "created_at": chunk.created_at.isoformat()
            }

            try:
                result = self.supabase.client.table("embeddings").insert(embedding_data).execute()
                if not result.data:
                    logger.warning(f"No data returned when inserting chunk {chunk.id}")
            except Exception as e:
                logger.error(f"Failed to insert chunk {chunk.id}: {e}")
                raise

            document_chunks.append(chunk)

        logger.info(f"Added {len(document_chunks)} chunks for document {document_id} to {namespace.value}")
        return document_chunks

    async def search_similar(
        self,
        query: str,
        namespace: EmbeddingNamespace,
        limit: int = 10,
        embedding_type: str = "ltkb",
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for similar content in the vector database using pgvector."""

        await self._initialize_pgvector()

        if embedding_type not in self.embedding_configs:
            raise ValueError(f"Unknown embedding type: {embedding_type}")

        config = self.embedding_configs[embedding_type]

        # Generate query embedding
        query_embedding = await self.generate_embedding(query, config.model_name)

        # Build the search query using Supabase RPC for vector similarity
        try:
            # Use Supabase RPC function for vector similarity search
            rpc_params = {
                "query_embedding": query_embedding,
                "match_threshold": 0.7,  # Similarity threshold
                "match_count": limit,
                "namespace_filter": namespace.value,
                "embedding_type_filter": embedding_type
            }

            # Add metadata filters if provided
            if filters:
                rpc_params["metadata_filter"] = json.dumps(filters)

            result = self.supabase.client.rpc("match_embeddings", rpc_params).execute()

            # Convert to SearchResult objects
            search_results = []

            if result.data:
                for row in result.data:
                    # Create DocumentChunk
                    chunk = DocumentChunk(
                        id=row["id"],
                        content=row["content"],
                        source_document_id=row["source_document_id"],
                        chunk_index=row["chunk_index"],
                        namespace=EmbeddingNamespace(row["namespace"]),
                        metadata=row.get("metadata", {})
                    )

                    # Calculate similarity score from distance
                    distance = row.get("distance", 1.0)
                    similarity_score = max(0.0, 1.0 - distance)

                    search_results.append(SearchResult(
                        chunk=chunk,
                        similarity_score=similarity_score,
                        distance=distance
                    ))

            return search_results

        except Exception as e:
            logger.error(f"Error searching embeddings: {e}")
            raise

    async def delete_document(self, document_id: str, namespace: EmbeddingNamespace) -> int:
        """Delete all chunks for a document from the vector database."""

        await self._initialize_pgvector()

        try:
            # Delete all chunks for this document
            result = self.supabase.client.table("embeddings").delete().eq(
                "source_document_id", document_id
            ).eq("namespace", namespace.value).execute()

            deleted_count = len(result.data) if result.data else 0
            logger.info(f"Deleted {deleted_count} chunks for document {document_id} from {namespace.value}")
            return deleted_count

        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            raise

    async def get_collection_stats(self, namespace: EmbeddingNamespace) -> Dict[str, Any]:
        """Get statistics for a namespace."""

        await self._initialize_pgvector()

        try:
            # Get total count for namespace
            count_result = self.supabase.client.table("embeddings").select(
                "id", count="exact"
            ).eq("namespace", namespace.value).execute()

            total_chunks = count_result.count if count_result.count else 0

            # Get unique documents count
            unique_docs_result = self.supabase.client.table("embeddings").select(
                "source_document_id"
            ).eq("namespace", namespace.value).execute()

            unique_documents = set()
            if unique_docs_result.data:
                unique_documents = set(row["source_document_id"] for row in unique_docs_result.data)

            return {
                "namespace": namespace.value,
                "total_chunks": total_chunks,
                "unique_documents": len(unique_documents),
                "sample_size": min(100, total_chunks)
            }

        except Exception as e:
            logger.error(f"Error getting collection stats for {namespace.value}: {e}")
            raise

    async def close(self):
        """Close the HTTP client."""
        await self.http_client.aclose()


class EmbeddingAgent:
    """High-level embedding agent for LTKB operations."""

    def __init__(self, vector_db: Optional[VectorDBClient] = None):
        self.vector_db = vector_db or VectorDBClient()

    async def embed_ltkb_document(
        self,
        document_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[DocumentChunk]:
        """Embed a document for LTKB with long-context embeddings."""

        return await self.vector_db.add_document(
            document_id=document_id,
            content=content,
            namespace=EmbeddingNamespace.LTKB,
            metadata=metadata,
            embedding_type="ltkb"
        )

    async def embed_stpm_content(
        self,
        content_id: str,
        content: str,
        project_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[DocumentChunk]:
        """Embed content for STPM with fast retrieval embeddings."""

        stpm_metadata = {"project_id": project_id}
        if metadata:
            stpm_metadata.update(metadata)

        return await self.vector_db.add_document(
            document_id=content_id,
            content=content,
            namespace=EmbeddingNamespace.STPM,
            metadata=stpm_metadata,
            embedding_type="stpm"
        )

    async def search_ltkb(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search LTKB with long-context embeddings."""

        return await self.vector_db.search_similar(
            query=query,
            namespace=EmbeddingNamespace.LTKB,
            limit=limit,
            embedding_type="ltkb",
            filters=filters
        )

    async def search_stpm(
        self,
        query: str,
        project_id: Optional[str] = None,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search STPM with fast retrieval embeddings."""

        search_filters = filters or {}
        if project_id:
            search_filters["project_id"] = project_id

        return await self.vector_db.search_similar(
            query=query,
            namespace=EmbeddingNamespace.STPM,
            limit=limit,
            embedding_type="stpm",
            filters=search_filters
        )

    async def get_relevant_context(
        self,
        query: str,
        project_id: Optional[str] = None,
        include_ltkb: bool = True,
        include_stpm: bool = True,
        limit_per_source: int = 5
    ) -> Tuple[str, str]:
        """Get relevant context from both LTKB and STPM."""

        ltkb_context = ""
        stpm_context = ""

        if include_ltkb:
            ltkb_results = await self.search_ltkb(query, limit=limit_per_source)
            ltkb_chunks = [result.chunk.content for result in ltkb_results]
            ltkb_context = "\n\n".join(ltkb_chunks)

        if include_stpm:
            stpm_results = await self.search_stpm(query, project_id=project_id, limit=limit_per_source)
            stpm_chunks = [result.chunk.content for result in stpm_results]
            stpm_context = "\n\n".join(stpm_chunks)

        return ltkb_context, stpm_context

    async def close(self):
        """Close the vector database connection."""
        await self.vector_db.close()


# Global instances
vector_db = None
embedding_agent = None

def get_vector_db() -> VectorDBClient:
    """Get global vector database instance."""
    global vector_db
    if vector_db is None:
        vector_db = VectorDBClient()
    return vector_db

def get_embedding_agent() -> EmbeddingAgent:
    """Get global embedding agent instance."""
    global embedding_agent
    if embedding_agent is None:
        embedding_agent = EmbeddingAgent(get_vector_db())
    return embedding_agent
