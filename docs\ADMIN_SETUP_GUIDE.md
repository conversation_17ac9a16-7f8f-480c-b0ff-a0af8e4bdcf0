# AI Model Admin Setup Guide
*Easy model configuration without Docker complexity*

## 🎯 What You've Built

You now have a powerful admin interface that allows you to:
- ✅ **Switch between local (Ollama) and cloud AI models**
- ✅ **Configure different models for each AI agent role**
- ✅ **Test model connections before deployment**
- ✅ **Add cloud providers (OpenAI, Anthropic, Google) with API keys**
- ✅ **Monitor system health and model availability**

## 🚀 Quick Start

### 1. Start Your Backend
```bash
# Activate your virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Start the FastAPI server
cd src/ai_coding_agent
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Start Your Frontend
```bash
# In a new terminal
cd frontend
npm start
```

### 3. Access Admin Panel
- Open your browser to: `http://localhost:3000/admin`
- Navigate using the sidebar: **Admin** section

## 🔧 Configuration Options

### **Local Models (Ollama)**
- **Automatic Detection**: The admin panel automatically detects installed Ollama models
- **No Setup Required**: If Ollama is running, models appear instantly
- **Test Connection**: Click "Test Connection" to verify model availability

### **Cloud Models (API-based)**

#### **OpenAI Setup**
1. Click "Add Cloud Provider"
2. Select "OpenAI"
3. Enter your OpenAI API key
4. Available models: `gpt-4`, `gpt-4-turbo`, `gpt-3.5-turbo`

#### **Anthropic Setup**
1. Click "Add Cloud Provider"
2. Select "Anthropic"
3. Enter your Anthropic API key
4. Available models: `claude-3-opus`, `claude-3-sonnet`, `claude-3-haiku`

#### **Google Setup**
1. Click "Add Cloud Provider"
2. Select "Google"
3. Enter your Google AI API key
4. Available models: `gemini-pro`, `gemini-pro-vision`

## 🎛️ Agent Configuration

### **Agent Roles & Recommendations**

| Agent | Best Local Model | Best Cloud Model | Purpose |
|-------|------------------|------------------|---------|
| **Architect** | `llama3.2:3b` | `gpt-4` | Project planning, orchestration |
| **Frontend** | `starcoder2:3b` | `gpt-4-turbo` | React, UI/UX development |
| **Backend** | `deepseek-coder:6.7b-instruct` | `claude-3-sonnet` | APIs, business logic |
| **Shell** | `qwen2.5:3b` | `gpt-3.5-turbo` | Command execution, scripts |
| **Debug** | `deepseek-coder:6.7b-instruct` | `claude-3-opus` | Issue detection, fixes |
| **Test** | `qwen2.5:3b` | `gpt-3.5-turbo` | Unit tests, QA |

### **Configuration Strategy**

#### **For Development (Local)**
```
✅ Use Ollama models for cost-effective development
✅ Primary: Local models
✅ Fallback: Cloud models for complex tasks
```

#### **For Production (Cloud)**
```
✅ Use cloud models for reliability and performance
✅ Primary: Cloud models
✅ Fallback: Local models for cost optimization
```

## 💰 Cost Management

### **Local Models (Free)**
- **Cost**: $0 - completely free
- **Requirements**: GPU recommended, 8GB+ RAM
- **Best For**: Development, testing, cost-sensitive deployments

### **Cloud Models (Pay-per-use)**
- **OpenAI**: ~$0.01-0.06 per 1K tokens
- **Anthropic**: ~$0.008-0.075 per 1K tokens  
- **Google**: ~$0.0005-0.002 per 1K tokens
- **Best For**: Production, complex tasks, reliability

## 🔄 Easy VPS Migration Strategy

### **Phase 1: Local Development**
```bash
# Current setup - perfect for development
Ollama (local) + Admin Panel = $0/month
```

### **Phase 2: Hybrid Deployment**
```bash
# Best of both worlds
Local models (development) + Cloud APIs (production) = $50-200/month
```

### **Phase 3: Full Cloud**
```bash
# Scale to thousands of users
VPS + Cloud APIs = $100-500/month
```

## 🛠️ Troubleshooting

### **Ollama Not Detected**
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama if needed
ollama serve

# Pull models if missing
ollama pull llama3.2:3b
ollama pull starcoder2:3b
ollama pull deepseek-coder:6.7b-instruct
ollama pull qwen2.5:3b
```

### **Cloud API Issues**
1. **Invalid API Key**: Double-check your API key in the admin panel
2. **Rate Limits**: Cloud providers have usage limits
3. **Model Names**: Ensure you're using correct model names

### **Configuration Not Saving**
1. Check backend logs for errors
2. Ensure `models_config.json` is writable
3. Restart backend after major changes

## 📊 Monitoring & Health

### **System Health Check**
- **Admin Panel**: Shows real-time status of all providers
- **Model Testing**: Test individual models before deployment
- **Usage Tracking**: Monitor which models are being used

### **Performance Optimization**
1. **Local Models**: Use for development and simple tasks
2. **Cloud Models**: Use for complex reasoning and production
3. **Fallback Strategy**: Always configure fallbacks for reliability

## 🎯 Next Steps

### **Immediate Actions**
1. ✅ Configure your preferred models in the admin panel
2. ✅ Test connections to ensure everything works
3. ✅ Set up at least one cloud provider for fallback

### **Future Enhancements**
1. **User Project Isolation** (Phase J from priority list)
2. **GitHub Project Import** (Phase K from priority list)
3. **Multi-Agent Coordination** (Phase E from priority list)

## 🚀 Benefits of This Approach

### **vs Docker Complexity**
- ✅ **Simple Setup**: No container orchestration needed
- ✅ **Easy Debugging**: Direct access to logs and configuration
- ✅ **Flexible Deployment**: Works locally and on VPS
- ✅ **Cost Effective**: Mix local and cloud models as needed

### **vs Fixed Model Setup**
- ✅ **Dynamic Configuration**: Change models without code changes
- ✅ **A/B Testing**: Try different models for different agents
- ✅ **Gradual Migration**: Move from local to cloud incrementally
- ✅ **Fallback Strategy**: Automatic failover between providers

## 🎉 You're Ready!

Your AI Coding Agent now has:
- **Flexible model configuration** ✅
- **Local and cloud model support** ✅
- **Easy admin interface** ✅
- **VPS migration path** ✅
- **Cost optimization options** ✅

**Start experimenting with different model combinations to find what works best for your use case!**

---

## 🆘 Need Help?

If you encounter any issues:
1. Check the browser console for frontend errors
2. Check the FastAPI logs for backend errors
3. Verify Ollama is running: `curl http://localhost:11434/api/tags`
4. Test API keys in the admin panel before using them

**Happy coding with your AI agents!** 🤖✨
