#!/usr/bin/env python3
"""
Comprehensive Ollama Model Diagnostics

Tests each of the 5 required models individually to identify issues.
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, Any, List

# Required models for AI Integration Foundation
REQUIRED_MODELS = [
    "yi-coder:1.5b",
    "mistral:7b-instruct-q4_0",
    "qwen2.5:3b",
    "starcoder2:3b",
    "deepseek-coder:6.7b-instruct-q4_0"
]

async def test_model_individually(model_name: str) -> Dict[str, Any]:
    """Test a single model comprehensively."""
    print(f"\n🔍 Testing {model_name}...")

    result = {
        "model": model_name,
        "available": False,
        "responsive": False,
        "response_time_ms": None,
        "error": None,
        "response_content": None
    }

    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            # First check if model is available
            async with session.get("http://localhost:11434/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    available_models = [m.get("name", "") for m in data.get("models", [])]
                    if model_name in available_models:
                        result["available"] = True
                        print(f"  ✅ Model {model_name} is available")
                    else:
                        result["error"] = f"Model {model_name} not found in available models"
                        print(f"  ❌ Model {model_name} not available")
                        return result
                else:
                    result["error"] = f"Ollama API returned status {response.status}"
                    return result

            # Test model responsiveness with a simple prompt
            start_time = time.time()

            test_payload = {
                "model": model_name,
                "prompt": "Hello! Please respond with just 'OK' to confirm you're working.",
                "stream": False,
                "options": {
                    "num_predict": 10,  # Limit response length
                    "temperature": 0.1  # Low temperature for consistent response
                }
            }

            print(f"  🧪 Testing responsiveness...")
            async with session.post(
                "http://localhost:11434/api/generate",
                json=test_payload
            ) as response:
                response_time = (time.time() - start_time) * 1000
                result["response_time_ms"] = round(response_time, 2)

                if response.status == 200:
                    data = await response.json()
                    response_content = data.get("response", "").strip()
                    result["response_content"] = response_content
                    result["responsive"] = True

                    print(f"  ✅ Response received in {result['response_time_ms']}ms")
                    print(f"  📝 Response: '{response_content[:50]}{'...' if len(response_content) > 50 else ''}'")

                    # Check if response seems reasonable
                    if len(response_content) > 0:
                        print(f"  ✅ Model is responding normally")
                    else:
                        print(f"  ⚠️  Model responded but with empty content")
                        result["error"] = "Empty response received"

                else:
                    error_text = await response.text()
                    result["error"] = f"HTTP {response.status}: {error_text}"
                    print(f"  ❌ HTTP {response.status}: {error_text}")

    except asyncio.TimeoutError:
        result["error"] = "Request timeout (30s)"
        print(f"  ❌ Timeout after 30 seconds")
    except Exception as e:
        result["error"] = str(e)
        print(f"  ❌ Error: {str(e)}")

    return result

async def test_all_models():
    """Test all required models."""
    print("🚀 Starting comprehensive model diagnostics...")
    print(f"📋 Testing {len(REQUIRED_MODELS)} required models\n")

    results = []
    healthy_count = 0

    for model_name in REQUIRED_MODELS:
        result = await test_model_individually(model_name)
        results.append(result)

        if result["available"] and result["responsive"]:
            healthy_count += 1

    # Summary Report
    print("\n" + "="*60)
    print("📊 COMPREHENSIVE DIAGNOSTICS REPORT")
    print("="*60)

    print(f"\n🏥 Health Summary:")
    print(f"  Healthy models: {healthy_count}/{len(REQUIRED_MODELS)}")
    print(f"  Overall status: {'✅ HEALTHY' if healthy_count == len(REQUIRED_MODELS) else '⚠️  DEGRADED' if healthy_count > 0 else '❌ UNHEALTHY'}")

    print(f"\n⏱️  Performance Summary:")
    response_times = [r["response_time_ms"] for r in results if r["response_time_ms"] is not None]
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        min_time = min(response_times)
        print(f"  Average response time: {avg_time:.1f}ms")
        print(f"  Fastest response: {min_time:.1f}ms")
        print(f"  Slowest response: {max_time:.1f}ms")

        if avg_time > 5000:
            print(f"  ⚠️  WARNING: Average response time is quite high ({avg_time:.1f}ms)")
        elif avg_time > 2000:
            print(f"  ⚠️  NOTICE: Response times are moderately high ({avg_time:.1f}ms)")
        else:
            print(f"  ✅ Response times are good")

    print(f"\n🔍 Detailed Results:")
    for result in results:
        status = "✅ HEALTHY" if result["available"] and result["responsive"] else "❌ UNHEALTHY"
        print(f"  {result['model']}: {status}")

        if result["response_time_ms"]:
            print(f"    Response time: {result['response_time_ms']}ms")

        if result["error"]:
            print(f"    Error: {result['error']}")

        if result["response_content"]:
            content_preview = result["response_content"][:100]
            print(f"    Response preview: '{content_preview}{'...' if len(result['response_content']) > 100 else ''}'")

    # Recommendations
    print(f"\n💡 Recommendations:")

    unhealthy_models = [r for r in results if not (r["available"] and r["responsive"])]
    if unhealthy_models:
        print("  🔧 Fix unhealthy models:")
        for result in unhealthy_models:
            print(f"    - {result['model']}: {result['error']}")

    slow_models = [r for r in results if r["response_time_ms"] and r["response_time_ms"] > 3000]
    if slow_models:
        print("  ⚡ Optimize slow models (>3s response time):")
        for result in slow_models:
            print(f"    - {result['model']}: {result['response_time_ms']}ms")

    if healthy_count == len(REQUIRED_MODELS) and all(r["response_time_ms"] and r["response_time_ms"] < 3000 for r in results if r["response_time_ms"]):
        print("  🎉 All models are healthy and performing well!")
        print("  ✅ AI Integration Foundation (Phase 4) is ready!")

    return results

if __name__ == "__main__":
    asyncio.run(test_all_models())
