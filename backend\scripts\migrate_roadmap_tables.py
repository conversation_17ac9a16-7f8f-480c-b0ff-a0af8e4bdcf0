"""
Database migration script for roadmap system (Phase B1).
Creates tables for projects, roadmaps, phases, steps, and tasks.
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from ai_coding_agent.config import settings
from ai_coding_agent.models import Base, create_tables


def migrate_roadmap_tables():
    """Create roadmap system tables."""
    print("🔄 Creating roadmap system tables...")

    # Create database engine
    engine = create_engine(settings.database.url)

    # Create all tables
    Base.metadata.create_all(engine)

    print("✅ Roadmap tables created successfully")
    print("📋 Tables created:")
    print("  - projects")
    print("  - roadmaps")
    print("  - phases")
    print("  - steps")
    print("  - tasks")


if __name__ == "__main__":
    migrate_roadmap_tables()
