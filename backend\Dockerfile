# AI Coding Agent Backend Container
# Multi-stage build for production optimization

# Development stage
FROM python:3.13-slim as development

# Set environment variables for Python and application
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app:/app/src \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONIOENCODING=utf-8

# Install system dependencies with security updates
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    gcc \
    g++ \
    git \
    wget \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for development (UID 1000 for volume mount compatibility)
RUN groupadd -g 1000 appuser && \
    useradd -r -u 1000 -g appuser appuser

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-dev.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-dev.txt

# Copy application code with correct structure
# CRITICAL FIX: Copy src directory to maintain Python module structure
COPY src/ ./src/
# CRITICAL FIX: Create symlink for direct ai_coding_agent access
RUN ln -sf /app/src/ai_coding_agent /app/ai_coding_agent

# Copy configuration, scripts, and tests
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY tests/ ./tests/

# Create necessary directories and set permissions
RUN mkdir -p logs uploads user-projects && \
    chown -R appuser:appuser /app

# Switch to non-root user for development
USER appuser

# Expose port
EXPOSE 8000

# Development command with hot reload
CMD ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM python:3.13-slim as production

# Set environment variables for production
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app:/app/src \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONIOENCODING=utf-8

# Create non-root user for security (UID 1000 for consistency) and add to docker group
RUN groupadd -g 1000 appuser && \
    useradd -r -u 1000 -g appuser appuser && \
    groupadd -f docker && \
    usermod -aG docker appuser

# Install system dependencies with security hardening
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-dev.txt .

# Install Python dependencies (production only)
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code with correct structure
# CRITICAL FIX: Copy src directory to maintain Python module structure
COPY src/ ./src/
# CRITICAL FIX: Create symlink for direct ai_coding_agent access
RUN ln -sf /app/src/ai_coding_agent /app/ai_coding_agent

# Copy configuration, scripts, and tests
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY tests/ ./tests/

# Create necessary directories and set permissions
RUN mkdir -p logs uploads user-projects && \
    chown -R appuser:appuser /app

# TEMPORARY: Run as root for Docker socket access
# TODO: Implement proper Docker socket permissions for production security
# USER appuser

# Health check with proper endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# Expose port
EXPOSE 8000

# Production command without reload
CMD ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000"]
