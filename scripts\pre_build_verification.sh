#!/bin/bash

# Pre-Build File Structure Verification for AI Coding Agent
# Run this BEFORE building Docker containers to ensure all files are in place
# Usage: bash scripts/pre_build_verification.sh

set -e

echo "🔍 AI Coding Agent - Pre-Build File Structure Verification"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if file exists
check_file() {
    local file_path=$1
    local description=$2

    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✅ $description: $file_path${NC}"
        return 0
    else
        echo -e "${RED}❌ Missing $description: $file_path${NC}"
        return 1
    fi
}

# Function to check if directory exists
check_directory() {
    local dir_path=$1
    local description=$2

    if [ -d "$dir_path" ]; then
        echo -e "${GREEN}✅ $description: $dir_path${NC}"
        return 0
    else
        echo -e "${RED}❌ Missing $description: $dir_path${NC}"
        return 1
    fi
}

echo ""
echo "🏗️ Backend File Structure Verification"
echo "-------------------------------------"

# Check backend directories
check_directory "backend/src/ai_coding_agent" "Backend source code"
check_directory "backend/config" "Backend configuration"
check_directory "backend/scripts" "Backend scripts"
check_directory "backend/tests" "Backend tests"

# Check critical backend files
check_file "backend/Dockerfile" "Backend Dockerfile"
check_file "backend/requirements.txt" "Backend requirements"
check_file "backend/src/ai_coding_agent/main.py" "Main application file"
check_file "backend/src/ai_coding_agent/__init__.py" "Package init file"
check_file "backend/src/ai_coding_agent/config.py" "Configuration module"

# Check backend scripts
check_file "backend/scripts/test_migration.py" "Migration test script"
check_file "backend/scripts/setup_pgvector.sql" "pgvector setup script"
check_file "backend/scripts/migrate_db.py" "Database migration script"

# Check backend services
check_file "backend/src/ai_coding_agent/services/vector_db.py" "Vector DB service"
check_file "backend/src/ai_coding_agent/services/redis_cache.py" "Redis cache service"

echo ""
echo "🌐 Frontend File Structure Verification"
echo "--------------------------------------"

# Check frontend directories
check_directory "frontend/src" "Frontend source code"
check_directory "frontend/public" "Frontend public assets"

# Check critical frontend files
check_file "frontend/Dockerfile" "Frontend Dockerfile"
check_file "frontend/package.json" "Frontend package.json"
check_file "frontend/nginx.conf" "Nginx configuration"
check_file "frontend/src/App.js" "Main React component"

echo ""
echo "🐳 Docker Configuration Verification"
echo "-----------------------------------"

# Check Docker files
check_file "docker-compose.yml" "Docker Compose configuration"
check_file "docker-compose.dev.yml" "Docker Compose development configuration"

# Check environment files
if [ -f ".env" ]; then
    echo -e "${GREEN}✅ Environment file: .env${NC}"

    # Check for required environment variables
    echo ""
    echo -e "${BLUE}🔐 Environment Variables Check:${NC}"

    if grep -q "SECRET_KEY=" .env; then
        echo -e "${GREEN}✅ SECRET_KEY found in .env${NC}"
    else
        echo -e "${RED}❌ SECRET_KEY missing in .env${NC}"
    fi

    if grep -q "CONFIG_ENCRYPTION_KEY=" .env; then
        echo -e "${GREEN}✅ CONFIG_ENCRYPTION_KEY found in .env${NC}"
    else
        echo -e "${RED}❌ CONFIG_ENCRYPTION_KEY missing in .env${NC}"
    fi

    if grep -q "DB_PASSWORD=" .env; then
        echo -e "${GREEN}✅ DB_PASSWORD found in .env${NC}"
    else
        echo -e "${YELLOW}⚠️ DB_PASSWORD not found in .env (will use default)${NC}"
    fi
else
    echo -e "${RED}❌ Environment file missing: .env${NC}"
    echo -e "${YELLOW}💡 Create .env file with required variables:${NC}"
    echo "SECRET_KEY=your-secret-key-here-32-chars-min"
    echo "CONFIG_ENCRYPTION_KEY=your-encryption-key-32-chars-min"
    echo "DB_PASSWORD=your-database-password"
fi

echo ""
echo "📁 Volume Mount Directories"
echo "--------------------------"

# Check volume mount directories
check_directory "user-projects" "User projects directory"
check_directory "database/init" "Database initialization scripts"

echo ""
echo "=========================================================="
echo "🎯 Pre-Build Verification Complete!"
echo ""

# Count errors
error_count=$(grep -c "❌" /tmp/verification_output 2>/dev/null || echo "0")

if [ "$error_count" -eq 0 ]; then
    echo -e "${GREEN}✅ All checks passed! Ready to build containers.${NC}"
    echo ""
    echo "Next steps:"
    echo "1. docker-compose build --no-cache"
    echo "2. docker-compose up -d"
    echo "3. bash scripts/verify_container_files.sh"
else
    echo -e "${RED}❌ Found $error_count issues. Please fix them before building.${NC}"
    echo ""
    echo "Common fixes:"
    echo "1. Ensure all source files are in the correct directories"
    echo "2. Create missing .env file with required variables"
    echo "3. Check file permissions and paths"
fi
echo "=========================================================="
