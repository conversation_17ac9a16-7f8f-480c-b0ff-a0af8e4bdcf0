# Frontend Dockerfile for AI Coding Agent React Application
# Multi-stage build for optimized production deployment

# Stage 1: Build the React application
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache git

# Copy package files for dependency installation
COPY package*.json ./

# Install dependencies with clean install for reproducible builds
RUN npm ci --only=production --silent

# Copy source code (excluding node_modules via .dockerignore)
COPY . .

# Build the application with error handling
RUN npm run build && \
    echo "Build completed successfully" && \
    ls -la /app/build/ && \
    echo "Static assets:" && \
    ls -la /app/build/static/ || (echo "Build failed!" && exit 1)

# Verify build output contains required files
RUN test -f /app/build/index.html || (echo "Missing index.html!" && exit 1) && \
    test -d /app/build/static || (echo "Missing static directory!" && exit 1) && \
    echo "Build verification passed"

# Stage 2: Development stage with hot reload
FROM node:20-alpine AS development

# Set working directory
WORKDIR /app

# Install development dependencies
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci --silent

# Create non-root user for security (use existing node user)
RUN chown -R node:node /app

# Switch to non-root user
USER node

# Expose development server port
EXPOSE 3000

# Start development server with hot reload
CMD ["npm", "start"]

# Stage 3: Production serving with NGINX
FROM nginx:alpine AS production

# Install wget for health checks
RUN apk add --no-cache wget

# Create non-root user for security
RUN addgroup -g 1000 appuser && \
    adduser -D -u 1000 -G appuser appuser

# Copy built assets from build stage with verification
COPY --from=build /app/build /usr/share/nginx/html

# Verify copied assets
RUN ls -la /usr/share/nginx/html/ && \
    test -f /usr/share/nginx/html/index.html || (echo "Missing index.html in nginx!" && exit 1) && \
    test -d /usr/share/nginx/html/static || (echo "Missing static directory in nginx!" && exit 1) && \
    echo "Asset copy verification passed"

# Copy custom NGINX configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Set proper permissions for nginx directories
RUN chown -R appuser:appuser /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Enhanced health check with better error handling
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:80/ || exit 1

# Start NGINX with error logging
CMD ["nginx", "-g", "daemon off;"]
