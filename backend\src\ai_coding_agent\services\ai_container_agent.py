"""
AI Container Agent Service

This service bridges AI processing with container execution, implementing the
AIContainerAgent component required for Phase A0.1. It processes user requests
using AI models and executes the generated commands in user containers.
"""

import asyncio
import json
import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

from pydantic import BaseModel, Field

from .container_manager import UserContainerManager, get_container_manager, ProjectType
from .ai.orchestrator import AgentOrchestrator
from .ai.base import Chat<PERSON>essage, ChatRequest
from ..agents import AgentRole
from ..config import get_settings

logger = logging.getLogger(__name__)


class RequestType(str, Enum):
    """Types of user requests that can be processed."""
    CREATE_PROJECT = "create_project"
    MODIFY_CODE = "modify_code"
    RUN_COMMAND = "run_command"
    DEBUG_ISSUE = "debug_issue"
    INSTALL_PACKAGE = "install_package"
    GENERAL_HELP = "general_help"


class AIContainerRequest(BaseModel):
    """Request model for AI container operations."""
    user_request: str = Field(..., description="Natural language user request")
    project_type: Optional[ProjectType] = Field(None, description="Project type if creating new project")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")


class AIContainerResponse(BaseModel):
    """Response model for AI container operations."""
    success: bool
    request_type: RequestType
    ai_explanation: str
    commands_executed: List[str]
    execution_results: List[Dict[str, Any]]
    container_status: str
    timestamp: datetime
    error_message: Optional[str] = None


class CommandExecution(BaseModel):
    """Model for individual command execution."""
    command: str
    exit_code: int
    output: str
    success: bool
    execution_time: float


class AIContainerAgent:
    """
    AI Container Agent that processes user requests and executes them in containers.

    This service implements the missing component from Phase A0.1, providing
    AI-driven container operations with proper error handling and user feedback.
    """

    def __init__(self, container_manager: Optional[UserContainerManager] = None):
        """Initialize the AI Container Agent."""
        self.container_manager = container_manager or get_container_manager()
        self.ai_orchestrator = AgentOrchestrator()
        self.settings = get_settings()

        # Command patterns for security validation
        self.safe_command_patterns = [
            r'^npm (install|start|run|test)',
            r'^python -m (pip|pytest|unittest)',
            r'^git (status|add|commit|push|pull)',
            r'^ls|pwd|cat|echo|mkdir|touch',
            r'^cd [a-zA-Z0-9_/.-]+$',
            r'^code\.|vim\.|nano\.',
        ]

        # Dangerous command patterns to block
        self.dangerous_patterns = [
            r'rm -rf',
            r'sudo',
            r'chmod 777',
            r'>/dev/',
            r'curl.*\|.*sh',
            r'wget.*\|.*sh',
        ]

        logger.info("AIContainerAgent initialized successfully")

    async def execute_user_request(
        self,
        user_id: str,
        request: AIContainerRequest
    ) -> AIContainerResponse:
        """
        Process a user request with AI and execute in their container.

        Args:
            user_id: User identifier
            request: AI container request with user's natural language input

        Returns:
            AIContainerResponse: Results of AI processing and execution
        """
        start_time = datetime.utcnow()

        try:
            logger.info(f"Processing AI request for user {user_id}: {request.user_request}")

            # Step 1: Classify the request type
            request_type = await self._classify_request(request.user_request)

            # Step 2: Ensure user has a container
            container_info = await self._ensure_user_container(user_id, request.project_type)

            # Step 3: Get project context
            project_context = await self._get_project_context(user_id)

            # Step 4: Generate AI response and commands
            ai_response = await self._generate_ai_response(
                request.user_request,
                request_type,
                project_context,
                request.context
            )

            # Step 5: Extract and validate commands
            commands = self._extract_commands(ai_response.content)
            validated_commands = self._validate_commands(commands)

            # Step 6: Execute commands in container
            execution_results = await self._execute_commands(user_id, validated_commands)

            # Step 7: Build response
            return AIContainerResponse(
                success=True,
                request_type=request_type,
                ai_explanation=ai_response.content,
                commands_executed=validated_commands,
                execution_results=execution_results,
                container_status="running",
                timestamp=start_time
            )

        except Exception as e:
            logger.error(f"Failed to process AI request for user {user_id}: {e}")
            return AIContainerResponse(
                success=False,
                request_type=RequestType.GENERAL_HELP,
                ai_explanation=f"I encountered an error processing your request: {str(e)}",
                commands_executed=[],
                execution_results=[],
                container_status="error",
                timestamp=start_time,
                error_message=str(e)
            )

    async def _classify_request(self, user_request: str) -> RequestType:
        """Classify the type of user request."""
        request_lower = user_request.lower()

        if any(keyword in request_lower for keyword in ["create", "new project", "start project"]):
            return RequestType.CREATE_PROJECT
        elif any(keyword in request_lower for keyword in ["modify", "change", "update", "edit"]):
            return RequestType.MODIFY_CODE
        elif any(keyword in request_lower for keyword in ["run", "execute", "command"]):
            return RequestType.RUN_COMMAND
        elif any(keyword in request_lower for keyword in ["debug", "error", "fix", "issue"]):
            return RequestType.DEBUG_ISSUE
        elif any(keyword in request_lower for keyword in ["install", "add package", "dependency"]):
            return RequestType.INSTALL_PACKAGE
        else:
            return RequestType.GENERAL_HELP

    async def _ensure_user_container(
        self,
        user_id: str,
        project_type: Optional[ProjectType]
    ) -> Dict[str, Any]:
        """Ensure user has an active container."""
        try:
            # Check if user already has a container
            container_status = await self.container_manager.get_container_status(user_id)
            if container_status and container_status.status.value == "running":
                return {
                    "container_id": container_status.container_id,
                    "status": "existing"
                }
        except Exception:
            # Container doesn't exist or isn't running
            pass

        # Create new container
        if not project_type:
            project_type = ProjectType.PYTHON  # Default

        container = await self.container_manager.provision_user_environment(
            user_id=user_id,
            project_type=project_type
        )

        return {
            "container_id": container.container_id,
            "status": "created"
        }

    async def _get_project_context(self, user_id: str) -> Dict[str, Any]:
        """Get current project context from user's container."""
        try:
            # Get current directory structure
            ls_result = await self.container_manager.execute_in_container(
                user_id=user_id,
                command="ls -la"
            )

            # Get current working directory
            pwd_result = await self.container_manager.execute_in_container(
                user_id=user_id,
                command="pwd"
            )

            return {
                "current_directory": pwd_result.get("output", "").strip(),
                "directory_listing": ls_result.get("output", ""),
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.warning(f"Failed to get project context for user {user_id}: {e}")
            return {"error": str(e)}

    async def _generate_ai_response(
        self,
        user_request: str,
        request_type: RequestType,
        project_context: Dict[str, Any],
        additional_context: Dict[str, Any]
    ) -> Any:
        """Generate AI response with appropriate agent."""
        # Choose appropriate agent based on request type
        if request_type == RequestType.CREATE_PROJECT:
            agent_role = AgentRole.ARCHITECT
        elif request_type in [RequestType.MODIFY_CODE, RequestType.DEBUG_ISSUE]:
            agent_role = AgentRole.DEBUG  # Use DEBUG agent for debugging tasks
        elif request_type == RequestType.RUN_COMMAND:
            agent_role = AgentRole.SHELL
        else:
            agent_role = AgentRole.ARCHITECT

        # Build context-aware prompt
        prompt = self._build_ai_prompt(user_request, request_type, project_context, additional_context)

        # Create chat messages
        messages = [
            ChatMessage(role="system", content=self._get_system_prompt(agent_role)),
            ChatMessage(role="user", content=prompt)
        ]

        # Execute with AI orchestrator
        return await self.ai_orchestrator._execute_with_agent(
            agent_role=agent_role,
            messages=messages,
            context={"project_context": project_context}
        )

    def _build_ai_prompt(
        self,
        user_request: str,
        request_type: RequestType,
        project_context: Dict[str, Any],
        additional_context: Dict[str, Any]
    ) -> str:
        """Build context-aware prompt for AI."""
        prompt = f"""
User Request: {user_request}
Request Type: {request_type.value}

Current Project Context:
{json.dumps(project_context, indent=2)}

Please provide:
1. A clear explanation of what you'll do
2. The specific commands to execute (wrapped in ```bash blocks)
3. Expected outcomes

Additional Context:
{json.dumps(additional_context, indent=2)}

Focus on safe, secure commands that work in a containerized environment.
"""
        return prompt

    def _get_system_prompt(self, agent_role: AgentRole) -> str:
        """Get system prompt for specific agent role."""
        base_prompt = """
You are an AI coding assistant working in a secure containerized environment.
You can execute commands safely in the user's isolated container.

Guidelines:
- Provide clear explanations before suggesting commands
- Use safe, standard commands only
- Avoid destructive operations
- Focus on the user's specific request
- Wrap commands in ```bash code blocks
"""

        role_specific = {
            AgentRole.ARCHITECT: "Focus on project structure, planning, and high-level decisions.",
            AgentRole.BACKEND: "Focus on server-side code, APIs, and backend development.",
            AgentRole.FRONTEND: "Focus on UI/UX, client-side code, and frontend development.",
            AgentRole.SHELL: "Focus on command-line operations and system tasks.",
            AgentRole.DEBUG: "Focus on debugging, error resolution, and troubleshooting."
        }

        return base_prompt + "\n" + role_specific.get(agent_role, "")

    def _extract_commands(self, ai_response: str) -> List[str]:
        """Extract executable commands from AI response."""
        # Find all bash code blocks
        bash_pattern = r'```(?:bash|shell|sh)?\n(.*?)\n```'
        matches = re.findall(bash_pattern, ai_response, re.DOTALL)

        commands = []
        for match in matches:
            # Split multi-line commands
            lines = match.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):  # Skip comments
                    commands.append(line)

        return commands

    def _validate_commands(self, commands: List[str]) -> List[str]:
        """Validate commands for security and safety."""
        validated = []

        for command in commands:
            # Check against dangerous patterns
            if any(re.search(pattern, command, re.IGNORECASE) for pattern in self.dangerous_patterns):
                logger.warning(f"Blocked dangerous command: {command}")
                continue

            # Check if command matches safe patterns
            is_safe = any(re.match(pattern, command) for pattern in self.safe_command_patterns)

            if is_safe or self._is_basic_safe_command(command):
                validated.append(command)
            else:
                logger.warning(f"Blocked potentially unsafe command: {command}")

        return validated

    def _is_basic_safe_command(self, command: str) -> bool:
        """Check if command is a basic safe operation."""
        safe_commands = ['ls', 'pwd', 'cat', 'echo', 'mkdir', 'touch', 'cd']
        return any(command.startswith(cmd) for cmd in safe_commands)

    async def _execute_commands(self, user_id: str, commands: List[str]) -> List[Dict[str, Any]]:
        """Execute validated commands in user's container."""
        results = []

        for command in commands:
            try:
                start_time = datetime.utcnow()

                result = await self.container_manager.execute_in_container(
                    user_id=user_id,
                    command=command
                )

                end_time = datetime.utcnow()
                execution_time = (end_time - start_time).total_seconds()

                results.append({
                    "command": command,
                    "exit_code": result.get("exit_code", -1),
                    "output": result.get("output", ""),
                    "success": result.get("success", False),
                    "execution_time": execution_time
                })

            except Exception as e:
                logger.error(f"Failed to execute command '{command}' for user {user_id}: {e}")
                results.append({
                    "command": command,
                    "exit_code": -1,
                    "output": f"Error: {str(e)}",
                    "success": False,
                    "execution_time": 0.0
                })

        return results


# Dependency injection
_ai_container_agent: Optional[AIContainerAgent] = None


def get_ai_container_agent() -> AIContainerAgent:
    """Get or create AI Container Agent instance."""
    global _ai_container_agent
    if _ai_container_agent is None:
        _ai_container_agent = AIContainerAgent()
    return _ai_container_agent
