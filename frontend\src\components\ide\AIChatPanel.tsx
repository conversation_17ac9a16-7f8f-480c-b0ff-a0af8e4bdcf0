import React, { useState, useRef, useEffect } from 'react';
import {
  Send,
  User,
  Cpu,
  Trash2,
  Co<PERSON>,
  ThumbsUp,
  ThumbsDown,
  Code,
  FileText,
  Settings,
  MoreVertical
} from 'lucide-react';
import { AIChat, AIAssistantMessage } from '../../types/ide';
import { AgentRole } from '../../types/agents';

interface AIChatPanelProps {
  chat: AIChat;
  onSendMessage: (message: string, agentRole?: AgentRole) => void;
  onClearChat: () => void;
  currentFile?: string;
  selectedText?: string;
  className?: string;
}

interface MessageProps {
  message: AIAssistantMessage;
  onCopy: (content: string) => void;
  onFeedback: (messageId: string, feedback: 'positive' | 'negative') => void;
}

const Message: React.FC<MessageProps> = ({ message, onCopy, onFeedback }) => {
  const [showActions, setShowActions] = useState(false);

  const getAgentIcon = (agentRole?: AgentRole) => {
    switch (agentRole) {
      case AgentRole.ARCHITECT:
        return <Settings size={16} />;
      case AgentRole.DEVELOPER:
        return <Code size={16} />;
      case AgentRole.SHELL:
        return <FileText size={16} />;
      case AgentRole.TESTER:
        return <Cpu size={16} />;
      case AgentRole.REVIEWER:
        return <FileText size={16} />;
      default:
        return <Cpu size={16} />;
    }
  };

  const getAgentColor = (agentRole?: AgentRole) => {
    switch (agentRole) {
      case AgentRole.ARCHITECT:
        return 'text-purple-400';
      case AgentRole.DEVELOPER:
        return 'text-blue-400';
      case AgentRole.SHELL:
        return 'text-green-400';
      case AgentRole.TESTER:
        return 'text-yellow-400';
      case AgentRole.REVIEWER:
        return 'text-orange-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatTime = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(timestamp);
  };

  const isCode = (content: string) => {
    return content.includes('```') || content.includes('function') || content.includes('{') || content.includes('}');
  };

  const renderContent = (content: string) => {
    // Simple markdown-like rendering for code blocks
    const parts = content.split('```');

    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // This is a code block
        return (
          <div key={index} className="bg-gray-800 rounded p-3 my-2 overflow-x-auto">
            <pre className="text-green-400 text-sm">
              <code>{part}</code>
            </pre>
          </div>
        );
      } else {
        // This is regular text
        return (
          <div key={index} className="whitespace-pre-wrap">
            {part.split('\n').map((line, lineIndex) => (
              <React.Fragment key={lineIndex}>
                {line}
                {lineIndex < part.split('\n').length - 1 && <br />}
              </React.Fragment>
            ))}
          </div>
        );
      }
    });
  };

  return (
    <div
      className={`flex space-x-3 p-3 rounded-lg mb-3 ${
        message.type === 'user'
          ? 'bg-blue-900/20 ml-4'
          : message.isError
            ? 'bg-red-900/20 mr-4'
            : 'bg-gray-800/50 mr-4'
      }`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Avatar */}
      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
        message.type === 'user'
          ? 'bg-blue-600'
          : message.isError
            ? 'bg-red-600'
            : 'bg-gray-700'
      }`}>
        {message.type === 'user' ? (
          <User size={14} className="text-white" />
        ) : (
          <span className={getAgentColor(message.agentRole)}>
            {getAgentIcon(message.agentRole)}
          </span>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1 min-w-0">
        {/* Header */}
        <div className="flex items-center space-x-2 mb-1">
          <span className="text-sm font-medium text-white">
            {message.type === 'user'
              ? 'You'
              : message.agentRole
                ? `${message.agentRole.charAt(0).toUpperCase() + message.agentRole.slice(1)} Agent`
                : 'AI Assistant'
            }
          </span>
          <span className="text-xs text-gray-500">
            {formatTime(message.timestamp)}
          </span>
          {message.metadata?.executionTime && (
            <span className="text-xs text-gray-500">
              • {message.metadata.executionTime}ms
            </span>
          )}
        </div>

        {/* Content */}
        <div className={`text-sm ${
          message.isError ? 'text-red-300' : 'text-gray-300'
        }`}>
          {renderContent(message.content)}
        </div>

        {/* Metadata */}
        {message.metadata && (
          <div className="mt-2 text-xs text-gray-500 space-y-1">
            {message.metadata.taskId && (
              <div>Task ID: {message.metadata.taskId}</div>
            )}
            {message.metadata.tokenCount && (
              <div>Tokens: {message.metadata.tokenCount}</div>
            )}
            {message.metadata.filesModified && message.metadata.filesModified.length > 0 && (
              <div>Files modified: {message.metadata.filesModified.join(', ')}</div>
            )}
          </div>
        )}

        {/* Actions */}
        {showActions && message.type === 'assistant' && (
          <div className="flex items-center space-x-2 mt-2">
            <button
              onClick={() => onCopy(message.content)}
              className="p-1 text-gray-500 hover:text-gray-300 rounded"
              title="Copy message"
            >
              <Copy size={12} />
            </button>
            <button
              onClick={() => onFeedback(message.id, 'positive')}
              className="p-1 text-gray-500 hover:text-green-400 rounded"
              title="Good response"
            >
              <ThumbsUp size={12} />
            </button>
            <button
              onClick={() => onFeedback(message.id, 'negative')}
              className="p-1 text-gray-500 hover:text-red-400 rounded"
              title="Poor response"
            >
              <ThumbsDown size={12} />
            </button>
          </div>
        )}
      </div>

      {/* More Actions */}
      {showActions && (
        <button className="flex-shrink-0 p-1 text-gray-500 hover:text-gray-300 rounded">
          <MoreVertical size={14} />
        </button>
      )}
    </div>
  );
};

export const AIChatPanel: React.FC<AIChatPanelProps> = ({
  chat,
  onSendMessage,
  onClearChat,
  currentFile,
  selectedText,
  className = ''
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<AgentRole | undefined>(undefined);
  const [showAgentSelector, setShowAgentSelector] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chat.messages]);

  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      onSendMessage(inputMessage.trim(), selectedAgent);
      setInputMessage('');
      inputRef.current?.focus();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const handleMessageFeedback = (messageId: string, feedback: 'positive' | 'negative') => {
    console.log('Message feedback:', messageId, feedback);
    // TODO: Implement feedback API call
  };

  const getAgentOptions = () => [
    { role: AgentRole.ARCHITECT, name: 'Architect', description: 'Project planning & design' },
    { role: AgentRole.DEVELOPER, name: 'Developer', description: 'Code generation & implementation' },
    { role: AgentRole.SHELL, name: 'Shell', description: 'System commands & deployment' },
    { role: AgentRole.TESTER, name: 'Tester', description: 'Testing & quality assurance' },
    { role: AgentRole.REVIEWER, name: 'Reviewer', description: 'Code review & documentation' },
  ];

  const getQuickActions = () => {
    const actions = [
      'Explain this code',
      'Optimize performance',
      'Add error handling',
      'Write unit tests',
      'Generate documentation',
    ];

    if (selectedText) {
      actions.unshift('Refactor selection');
    }

    if (currentFile) {
      actions.push(`Review ${currentFile.split('/').pop()}`);
    }

    return actions;
  };

  return (
    <div className={`h-full bg-gray-800 flex flex-col ${className}`}>
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-white">AI Assistant</h3>
          {currentFile && (
            <p className="text-xs text-gray-400">Working on: {currentFile.split('/').pop()}</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowAgentSelector(!showAgentSelector)}
            className={`p-1 rounded ${selectedAgent ? 'text-blue-400' : 'text-gray-400'} hover:bg-gray-700`}
            title="Select Agent"
          >
            <Cpu size={14} />
          </button>
          <button
            onClick={onClearChat}
            className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded"
            title="Clear Chat"
          >
            <Trash2 size={14} />
          </button>
        </div>
      </div>

      {/* Agent Selector */}
      {showAgentSelector && (
        <div className="bg-gray-700 border-b border-gray-600 p-3">
          <div className="text-xs text-gray-400 mb-2">Select AI Agent:</div>
          <div className="grid grid-cols-1 gap-1">
            <button
              onClick={() => {
                setSelectedAgent(undefined);
                setShowAgentSelector(false);
              }}
              className={`text-left p-2 rounded text-xs ${
                !selectedAgent ? 'bg-blue-600 text-white' : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
              }`}
            >
              <div className="font-medium">Auto Select</div>
              <div className="text-gray-400">Let AI choose the best agent</div>
            </button>
            {getAgentOptions().map((agent) => (
              <button
                key={agent.role}
                onClick={() => {
                  setSelectedAgent(agent.role);
                  setShowAgentSelector(false);
                }}
                className={`text-left p-2 rounded text-xs ${
                  selectedAgent === agent.role
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                }`}
              >
                <div className="font-medium">{agent.name}</div>
                <div className="text-gray-400">{agent.description}</div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Context Info */}
      {(selectedText || currentFile) && (
        <div className="bg-gray-700 border-b border-gray-600 p-3">
          <div className="text-xs text-gray-400 mb-1">Context:</div>
          {currentFile && (
            <div className="text-xs text-blue-400 mb-1">
              📁 {currentFile}
            </div>
          )}
          {selectedText && (
            <div className="text-xs text-green-400 bg-gray-800 rounded p-2">
              Selected: {selectedText.length > 50 ? selectedText.substring(0, 50) + '...' : selectedText}
            </div>
          )}
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {chat.messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <Cpu size={48} className="mx-auto mb-4 opacity-50" />
            <h4 className="text-lg font-medium mb-2">AI Assistant Ready</h4>
            <p className="text-sm mb-4">Ask me anything about your code!</p>

            {/* Quick Actions */}
            <div className="space-y-2">
              <p className="text-xs text-gray-600">Quick actions:</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {getQuickActions().slice(0, 3).map((action, index) => (
                  <button
                    key={index}
                    onClick={() => setInputMessage(action)}
                    className="px-3 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
                  >
                    {action}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          chat.messages.map((message) => (
            <Message
              key={message.id}
              message={message}
              onCopy={handleCopyMessage}
              onFeedback={handleMessageFeedback}
            />
          ))
        )}

        {chat.isLoading && (
          <div className="flex items-center space-x-2 text-gray-400 p-3">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span className="text-sm">AI is thinking...</span>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="bg-gray-800 border-t border-gray-700 p-4">
        {/* Quick Actions */}
        {chat.messages.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {getQuickActions().slice(0, 4).map((action, index) => (
                <button
                  key={index}
                  onClick={() => setInputMessage(action)}
                  className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
                >
                  {action}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Input */}
        <div className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={selectedAgent ? `Ask ${selectedAgent} agent...` : "Ask AI anything..."}
            className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            disabled={chat.isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || chat.isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg px-4 py-2 transition-colors"
          >
            <Send size={16} />
          </button>
        </div>

        {/* Agent Indicator */}
        {selectedAgent && (
          <div className="mt-2 text-xs text-gray-400">
            Talking to: <span className="text-blue-400 capitalize">{selectedAgent}</span> Agent
          </div>
        )}
      </div>
    </div>
  );
};
