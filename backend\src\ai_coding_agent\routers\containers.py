"""
Container Management API Endpoints.

Provides REST API endpoints for managing user containers in the
container-per-user model. Includes provisioning, status checking,
command execution, and cleanup operations.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Request
from fastapi.responses import Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer
from pydantic import BaseModel, Field

from ..services.container_manager import (
    UserContainerManager,
    get_container_manager,
    UserContainer,
    ProjectType,
    ContainerStatus
)
from ..services.ai_container_agent import (
    AIContainerAgent,
    get_ai_container_agent,
    AIContainerRequest,
    AIContainerResponse,
    RequestType
)
from ..services.auth import get_current_user
from ..models.user import User

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Router
router = APIRouter(
    prefix="/api/v1/containers",
    tags=["containers"]
)


# NGINX Integration Endpoints for Subdomain Routing
# These must be defined early to ensure proper route registration

@router.get("/auth-check")
async def nginx_auth_check():
    """Simplified auth check for testing."""
    return {"status": "ok", "message": "Auth check working"}

@router.get("/get-port")
async def nginx_get_port():
    """Simplified port resolution for testing."""
    return {"port": 3000, "message": "Port resolution working"}

@router.get("/dev-status")
async def development_status():
    """Development endpoint to show hot reload is working."""
    return {
        "status": "hot_reload_active",
        "message": "This endpoint was added during development!",
        "timestamp": datetime.now().isoformat()
    }

@router.get("/implementation-progress")
async def implementation_progress():
    """Track implementation progress from consolidated plan."""
    return {
        "phase": "A0.1",
        "description": "Docker SDK & Container-Per-User Implementation",
        "status": "in_progress",
        "completed_items": [
            "Docker SDK dependency added",
            "Container manager service exists",
            "API endpoints partially implemented"
        ],
        "next_items": [
            "Complete ContainerOrchestrator service",
            "Implement UserContainerManager",
            "Add AIContainerAgent service"
        ],
        "timestamp": datetime.now().isoformat()
    }


# Request/Response Models
class ContainerProvisionRequest(BaseModel):
    """Request model for container provisioning."""
    project_type: ProjectType
    project_name: Optional[str] = Field(None, description="Optional project name")


class ContainerExecuteRequest(BaseModel):
    """Request model for command execution."""
    command: str = Field(..., description="Command to execute")
    working_dir: Optional[str] = Field(None, description="Working directory")


class ContainerResponse(BaseModel):
    """Response model for container information."""
    user_id: str
    container_id: str
    container_name: str
    project_type: ProjectType
    status: ContainerStatus
    port: Optional[int]
    preview_url: Optional[str]
    created_at: datetime
    last_accessed: datetime
    resource_limits: Dict[str, Any]


class ExecutionResponse(BaseModel):
    """Response model for command execution."""
    exit_code: int
    output: str
    success: bool
    executed_at: datetime


class ContainerListResponse(BaseModel):
    """Response model for container listing."""
    containers: List[ContainerResponse]
    total_count: int


@router.post("/provision", response_model=ContainerResponse, dependencies=[Depends(security)])
async def provision_user_container(
    request: ContainerProvisionRequest,
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Provision an isolated container environment for the current user.

    Creates a new container with the specified project type and proper
    security isolation, resource limits, and networking.
    """
    try:
        logger.info(f"Provisioning container for user {current_user.id}, type: {request.project_type}")

        user_container = await container_manager.provision_user_environment(
            user_id=str(current_user.id),
            project_type=request.project_type,
            project_name=request.project_name
        )

        return ContainerResponse(
            user_id=user_container.user_id,
            container_id=user_container.container_id,
            container_name=user_container.container_name,
            project_type=user_container.project_type,
            status=user_container.status,
            port=user_container.port,
            preview_url=user_container.preview_url,
            created_at=user_container.created_at,
            last_accessed=user_container.last_accessed,
            resource_limits=user_container.resource_limits
        )

    except Exception as e:
        logger.error(f"Failed to provision container for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Container provisioning failed: {str(e)}"
        )


@router.get("/status", response_model=Optional[ContainerResponse], dependencies=[Depends(security)])
async def get_container_status(
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Get the current status of the user's container.

    Returns container information if it exists, or None if no container
    is currently provisioned for the user.
    """
    try:
        user_container = await container_manager.get_container_status(str(current_user.id))

        if not user_container:
            return None

        return ContainerResponse(
            user_id=user_container.user_id,
            container_id=user_container.container_id,
            container_name=user_container.container_name,
            project_type=user_container.project_type,
            status=user_container.status,
            port=user_container.port,
            preview_url=user_container.preview_url,
            created_at=user_container.created_at,
            last_accessed=user_container.last_accessed,
            resource_limits=user_container.resource_limits
        )

    except Exception as e:
        logger.error(f"Failed to get container status for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get container status: {str(e)}"
        )


@router.post("/execute", response_model=ExecutionResponse, dependencies=[Depends(security)])
async def execute_command(
    request: ContainerExecuteRequest,
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Execute a command in the user's container.

    Runs the specified command in the user's isolated container environment
    with proper security restrictions and returns the execution results.
    """
    try:
        logger.info(f"Executing command for user {current_user.id}: {request.command}")

        result = await container_manager.execute_in_container(
            user_id=str(current_user.id),
            command=request.command,
            working_dir=request.working_dir
        )

        return ExecutionResponse(
            exit_code=result["exit_code"],
            output=result["output"],
            success=result["success"],
            executed_at=datetime.utcnow()
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to execute command for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Command execution failed: {str(e)}"
        )


@router.delete("/cleanup", dependencies=[Depends(security)])
async def cleanup_user_container(
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Clean up the user's container and associated resources.

    Stops and removes the user's container while preserving data volumes
    for future use. This is useful for freeing up resources when the
    container is no longer needed.
    """
    try:
        logger.info(f"Cleaning up container for user {current_user.id}")

        success = await container_manager.cleanup_user_container(str(current_user.id))

        if success:
            return {"message": "Container cleaned up successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cleanup container"
            )

    except Exception as e:
        logger.error(f"Failed to cleanup container for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Container cleanup failed: {str(e)}"
        )


# Admin endpoints (require admin privileges)
@router.get("/admin/list", response_model=ContainerListResponse)
async def list_all_containers(
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    List all active user containers (admin only).

    Returns information about all currently active user containers
    for monitoring and management purposes.
    """
    # TODO: Add admin role check
    # if not current_user.is_admin:
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    try:
        containers = container_manager.get_all_user_containers()

        container_responses = [
            ContainerResponse(
                user_id=container.user_id,
                container_id=container.container_id,
                container_name=container.container_name,
                project_type=container.project_type,
                status=container.status,
                port=container.port,
                preview_url=container.preview_url,
                created_at=container.created_at,
                last_accessed=container.last_accessed,
                resource_limits=container.resource_limits
            )
            for container in containers
        ]

        return ContainerListResponse(
            containers=container_responses,
            total_count=len(container_responses)
        )

    except Exception as e:
        logger.error(f"Failed to list containers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list containers: {str(e)}"
        )


@router.post("/admin/cleanup-inactive")
async def cleanup_inactive_containers(
    background_tasks: BackgroundTasks,
    max_idle_hours: int = 24,
    current_user: User = Depends(get_current_user),
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Clean up inactive containers (admin only).

    Removes containers that have been inactive for longer than the
    specified time period to free up system resources.
    """
    # TODO: Add admin role check
    # if not current_user.is_admin:
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    try:
        # Run cleanup in background
        background_tasks.add_task(
            container_manager.cleanup_inactive_containers,
            max_idle_hours
        )

        return {
            "message": f"Cleanup task started for containers inactive for {max_idle_hours} hours"
        }

    except Exception as e:
        logger.error(f"Failed to start cleanup task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start cleanup task: {str(e)}"
        )


@router.get("/health")
async def container_service_health(
    container_manager: UserContainerManager = Depends(get_container_manager)
):
    """
    Check the health of the container management service.

    Verifies that the Docker daemon is accessible and the container
    management service is functioning properly.
    """
    try:
        # Test Docker connection
        container_manager.client.ping()

        # Get basic stats
        active_containers = len(container_manager.get_all_user_containers())

        return {
            "status": "healthy",
            "docker_connected": True,
            "active_containers": active_containers,
            "timestamp": datetime.utcnow()
        }

    except Exception as e:
        logger.error(f"Container service health check failed: {e}")
        return {
            "status": "unhealthy",
            "docker_connected": False,
            "error": str(e),
            "timestamp": datetime.utcnow()
        }


# AI-Driven Container Operations
@router.post("/ai/execute", response_model=AIContainerResponse, dependencies=[Depends(security)])
async def ai_execute_request(
    request: AIContainerRequest,
    current_user: User = Depends(get_current_user),
    ai_agent: AIContainerAgent = Depends(get_ai_container_agent)
):
    """
    Process a natural language request with AI and execute in user's container.

    This endpoint implements the core AI-driven container functionality from Phase A0.1.
    It takes natural language input, processes it with AI, and executes the resulting
    commands in the user's isolated container environment.
    """
    try:
        logger.info(f"AI request from user {current_user.id}: {request.user_request}")

        # Process request with AI Container Agent
        response = await ai_agent.execute_user_request(
            user_id=str(current_user.id),
            request=request
        )

        logger.info(f"AI request completed for user {current_user.id}, success: {response.success}")
        return response

    except Exception as e:
        logger.error(f"Failed to process AI request for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process AI request: {str(e)}"
        )


@router.post("/ai/create-project", response_model=AIContainerResponse, dependencies=[Depends(security)])
async def ai_create_project(
    project_type: ProjectType,
    description: str,
    current_user: User = Depends(get_current_user),
    ai_agent: AIContainerAgent = Depends(get_ai_container_agent)
):
    """
    Create a new project using AI guidance.

    Uses AI to set up a project structure based on the project type and description,
    then executes the setup commands in the user's container.
    """
    try:
        logger.info(f"AI project creation for user {current_user.id}: {project_type.value}")

        # Build AI request for project creation
        ai_request = AIContainerRequest(
            user_request=f"Create a new {project_type.value} project: {description}",
            project_type=project_type,
            context={"operation": "create_project"}
        )

        # Process with AI agent
        response = await ai_agent.execute_user_request(
            user_id=str(current_user.id),
            request=ai_request
        )

        return response

    except Exception as e:
        logger.error(f"Failed to create AI project for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create project: {str(e)}"
        )


@router.get("/ai/capabilities")
async def get_ai_capabilities():
    """
    Get information about AI container capabilities.

    Returns the types of requests that can be processed and the available
    project types for AI-driven operations.
    """
    return {
        "request_types": [request_type.value for request_type in RequestType],
        "project_types": [project_type.value for project_type in ProjectType],
        "capabilities": [
            "Natural language command execution",
            "Project creation and setup",
            "Code modification assistance",
            "Debugging and troubleshooting",
            "Package installation",
            "General development help"
        ],
        "security_features": [
            "Command validation and filtering",
            "Isolated container execution",
            "Non-root user execution",
            "Resource limits enforcement"
        ]
    }
