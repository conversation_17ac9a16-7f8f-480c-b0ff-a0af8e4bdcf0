#!/usr/bin/env python3
"""
Quick CUDA availability test for the current environment.
"""

import torch
import sys

def test_cuda():
    """Test CUDA availability and configuration."""
    print("=== CUDA Test Results ===")
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU device: {torch.cuda.get_device_name(0)}")
        print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

        # Test actual GPU computation
        try:
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print("✅ GPU computation test successful!")
            return True
        except Exception as e:
            print(f"❌ GPU computation test failed: {e}")
            return False
    else:
        print("❌ CUDA not available")
        print("\nPossible fixes:")
        print("1. Install CUDA-enabled PyTorch:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        print("2. Check NVIDIA drivers are installed")
        print("3. Verify CUDA toolkit is available")
        return False

if __name__ == "__main__":
    test_cuda()
