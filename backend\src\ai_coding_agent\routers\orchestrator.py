"""
FastAPI router for orchestrator management and analytics.

Provides REST endpoints for:
- Task dispatch and agent management
- Model analytics and performance monitoring
- Load balancing status and optimization
- Health monitoring and diagnostics
"""

from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field

from ..orchestrator import (
    EnhancedOrchestrator, TaskContext, TaskType, TaskComplexity, AgentOutput,
    dispatch_to_agent, get_model_analytics, get_load_balancing_status,
    optimize_model_routing, reset_model_metrics
)

router = APIRouter(prefix="/api/v1/orchestrator", tags=["orchestrator"])


# Request/Response Models
class TaskDispatchRequest(BaseModel):
    """Request model for task dispatch."""
    agent_name: str = Field(description="Target agent name")
    task: str = Field(description="Task description")
    task_type: Optional[TaskType] = Field(None, description="Optional task type hint")
    complexity: Optional[TaskComplexity] = Field(None, description="Optional complexity hint")
    project_id: Optional[str] = Field(None, description="Project identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    ltkb_context: Optional[str] = Field(None, description="LTKB knowledge context")
    stpm_context: Optional[str] = Field(None, description="Short-term project memory context")


class ModelRouteRequest(BaseModel):
    """Request model for model routing."""
    agent_name: str = Field(description="Target agent name")
    task_type: TaskType = Field(description="Task type")
    complexity: TaskComplexity = Field(description="Task complexity")


class ModelRouteResponse(BaseModel):
    """Response model for model routing."""
    agent_name: str
    task_type: TaskType
    complexity: TaskComplexity
    selected_model: str
    routing_reason: str
    available_models: List[str]


class HealthCheckResponse(BaseModel):
    """Response model for health check."""
    timestamp: datetime
    healthy_models: List[str]
    unhealthy_models: List[str]
    total_models: int
    health_percentage: float


# Global orchestrator instance
orchestrator = EnhancedOrchestrator()


@router.post("/dispatch", response_model=AgentOutput)
async def dispatch_task(request: TaskDispatchRequest):
    """
    Dispatch a task to a specific agent with intelligent model routing.

    This endpoint provides the core orchestration functionality, automatically
    selecting the optimal model based on agent capabilities, task complexity,
    current load balancing, and model health status.
    """
    try:
        # Build task context
        context = TaskContext(
            project_id=request.project_id,
            user_id=request.user_id,
            session_id=request.session_id,
            ltkb_context=request.ltkb_context,
            stpm_context=request.stpm_context
        )

        # Dispatch task
        result = await orchestrator.dispatch_to_agent(
            agent_name=request.agent_name,
            task=request.task,
            context=context,
            task_type=request.task_type,
            complexity=request.complexity
        )

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task dispatch failed: {str(e)}")


@router.post("/route-model", response_model=ModelRouteResponse)
async def route_model(request: ModelRouteRequest):
    """
    Get the optimal model for a given agent, task type, and complexity.

    This endpoint allows testing and debugging of the model routing logic
    without actually executing a task.
    """
    try:
        # Get routing configuration
        agent_config = orchestrator.routing_config.get(request.agent_name, {})
        available_models = []

        # Build available models list
        if agent_config:
            available_models.extend([
                agent_config.get("primary"),
                agent_config.get("secondary"),
                agent_config.get("fallback")
            ])
            available_models = [m for m in available_models if m]

        # Route to optimal model
        selected_model = await orchestrator.route_model_by_task(
            request.agent_name, request.task_type, request.complexity
        )

        # Determine routing reason
        routing_reason = "default"
        task_routing = agent_config.get("task_routing", {})
        if request.task_type.value in task_routing:
            routing_reason = "task_specific"
        elif request.complexity in [TaskComplexity.COMPLEX, TaskComplexity.EXPERT]:
            routing_reason = "complexity_based"
        else:
            routing_reason = "primary_model"

        return ModelRouteResponse(
            agent_name=request.agent_name,
            task_type=request.task_type,
            complexity=request.complexity,
            selected_model=selected_model,
            routing_reason=routing_reason,
            available_models=available_models
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Model routing failed: {str(e)}")


@router.get("/analytics")
async def get_analytics():
    """
    Get comprehensive analytics for all models.

    Returns performance metrics, health scores, usage statistics,
    and other analytics data for monitoring and optimization.
    """
    try:
        analytics = await orchestrator.get_model_analytics()
        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")


@router.get("/load-balancing")
async def get_load_balancing():
    """
    Get current load balancing status and statistics.

    Shows current load distribution across models, request counts,
    and load balancing strategy information.
    """
    try:
        status = await orchestrator.get_load_balancing_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get load balancing status: {str(e)}")


@router.get("/optimization")
async def get_optimization_suggestions():
    """
    Get optimization suggestions based on model performance analysis.

    Analyzes current performance data and provides actionable suggestions
    for improving model routing, load balancing, and overall system performance.
    """
    try:
        optimizations = await orchestrator.optimize_model_routing()
        return optimizations
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get optimizations: {str(e)}")


@router.get("/health", response_model=HealthCheckResponse)
async def check_health():
    """
    Check health status of all configured models.

    Tests connectivity and responsiveness of each model to ensure
    availability for task dispatch.
    """
    try:
        # Get all configured models
        all_models = []
        for provider_config in orchestrator.config.config.get("providers", {}).values():
            all_models.extend(provider_config.get("models", {}).keys())

        # Check health for each model
        healthy_models = await orchestrator.health_monitor.get_healthy_models(all_models)
        unhealthy_models = [m for m in all_models if m not in healthy_models]

        health_percentage = len(healthy_models) / len(all_models) * 100 if all_models else 0

        return HealthCheckResponse(
            timestamp=datetime.now(),
            healthy_models=healthy_models,
            unhealthy_models=unhealthy_models,
            total_models=len(all_models),
            health_percentage=health_percentage
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.post("/reset-metrics")
async def reset_metrics(model_name: Optional[str] = None):
    """
    Reset performance metrics for a specific model or all models.

    Useful for clearing historical data after configuration changes
    or when starting fresh monitoring.
    """
    try:
        await orchestrator.reset_model_metrics(model_name)

        message = f"Reset metrics for model: {model_name}" if model_name else "Reset metrics for all models"
        return {"message": message, "timestamp": datetime.now().isoformat()}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset metrics: {str(e)}")


@router.get("/config")
async def get_configuration():
    """
    Get current orchestrator configuration.

    Returns the complete configuration including routing rules,
    performance settings, quality thresholds, and load balancing settings.
    """
    try:
        config = {
            "routing": orchestrator.routing_config,
            "performance_settings": orchestrator.performance_settings,
            "quality_thresholds": orchestrator.quality_thresholds,
            "load_balancing": orchestrator.load_balancing_config,
            "adaptive_routing": orchestrator.adaptive_routing_config,
            "analytics": orchestrator.analytics_config
        }
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get configuration: {str(e)}")


@router.get("/agents")
async def list_agents():
    """
    List all configured agents and their capabilities.

    Returns information about each agent including their preferred models,
    task routing configuration, and specializations.
    """
    try:
        agents_info = {}

        for agent_name, config in orchestrator.routing_config.items():
            # Get model info for this agent
            primary_model = config.get("primary")
            model_info = orchestrator.config.get_model_info(primary_model) if primary_model else {}

            agents_info[agent_name] = {
                "primary_model": config.get("primary"),
                "secondary_model": config.get("secondary"),
                "fallback_model": config.get("fallback"),
                "task_routing": config.get("task_routing", {}),
                "specializations": model_info.get("specialized_for", []),
                "description": model_info.get("description", "")
            }

        return {
            "agents": agents_info,
            "total_agents": len(agents_info)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list agents: {str(e)}")


@router.get("/models")
async def list_models():
    """
    List all configured models and their specifications.

    Returns detailed information about each model including capabilities,
    performance characteristics, and current health status.
    """
    try:
        models_info = {}

        # Get model configurations
        providers = orchestrator.config.config.get("providers", {})
        for provider_name, provider_config in providers.items():
            for model_name, model_config in provider_config.get("models", {}).items():
                # Get current health and metrics
                health_status = orchestrator.health_monitor.model_status.get(model_name, True)
                metrics = orchestrator.model_metrics.get(model_name)

                models_info[model_name] = {
                    "provider": provider_name,
                    "role": model_config.get("role"),
                    "agents": model_config.get("agents", []),
                    "description": model_config.get("description"),
                    "context_window": model_config.get("context_window"),
                    "performance": model_config.get("performance"),
                    "specialized_for": model_config.get("specialized_for", []),
                    "health_status": health_status,
                    "health_score": metrics.health_score if metrics else 1.0,
                    "avg_response_time": metrics.avg_response_time if metrics else 0.0,
                    "success_rate": metrics.success_rate if metrics else 1.0
                }

        return {
            "models": models_info,
            "total_models": len(models_info)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list models: {str(e)}")


# Cleanup on app shutdown
@router.on_event("shutdown")
async def shutdown_orchestrator():
    """Clean up orchestrator resources on shutdown."""
    await orchestrator.close()
