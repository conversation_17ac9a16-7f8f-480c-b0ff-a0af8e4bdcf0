"""
Admin authentication middleware for protecting admin endpoints.

This module provides authentication and authorization middleware
specifically for admin routes, ensuring only superusers can access
administrative functions.
"""

from typing import Optional
from datetime import datetime, timedelta
from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
import logging

from ..models import get_db, User
from ..services.auth import get_current_user, verify_token
from ..services.admin_audit import admin_audit_logger, AuditAction, AuditSeverity

# Configure security logger
security_logger = logging.getLogger("security.admin_auth")
security_logger.setLevel(logging.INFO)


async def get_current_admin_user(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> User:
    """
    Enhanced dependency to ensure current user is an admin (superuser).

    This function provides comprehensive security checks including:
    - User authentication validation
    - Account status verification
    - Admin privilege verification
    - Security event logging
    - Session validation

    Args:
        request: FastAPI request object for IP and user agent logging
        current_user: The currently authenticated user
        db: Database session

    Returns:
        User: The current user if they are an admin

    Raises:
        HTTPException: If user is not authenticated or not an admin
    """
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    # Check if user exists
    if not current_user:
        security_logger.warning(
            f"Admin access attempt without authentication from IP: {client_ip}"
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if account is active
    if not getattr(current_user, 'is_active', False):
        security_logger.warning(
            f"Admin access attempt by inactive user {current_user.username} from IP: {client_ip}"
        )
        admin_audit_logger.log_security_event(
            user=current_user,
            event_type="inactive_admin_access_attempt",
            details={
                "reason": "inactive_account",
                "user_agent": user_agent
            },
            ip_address=client_ip
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user account"
        )

    # Check admin privileges
    if not getattr(current_user, 'is_superuser', False):
        security_logger.warning(
            f"Admin access attempt by non-admin user {current_user.username} from IP: {client_ip}"
        )
        admin_audit_logger.log_security_event(
            user=current_user,
            event_type="unauthorized_admin_access_attempt",
            details={
                "reason": "insufficient_privileges",
                "user_agent": user_agent,
                "attempted_endpoint": str(request.url)
            },
            ip_address=client_ip
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required. Contact your administrator."
        )

    # Log successful admin access (for audit trail)
    security_logger.info(
        f"Admin access granted to {current_user.username} from IP: {client_ip}"
    )

    return current_user


async def get_current_admin_user_optional(
    current_user: Optional[User] = Depends(get_current_user),
) -> Optional[User]:
    """
    Optional admin dependency that returns None if user is not admin.

    Useful for endpoints that have different behavior for admin vs regular users.

    Args:
        current_user: The currently authenticated user (optional)

    Returns:
        User: The current user if they are an admin, None otherwise
    """
    if not current_user:
        return None

    if not getattr(current_user, 'is_active', False):
        return None

    if not getattr(current_user, 'is_superuser', False):
        return None

    return current_user


def require_admin_permission(permission: str = "admin"):
    """
    Decorator factory for requiring specific admin permissions.

    Args:
        permission: The required permission level

    Returns:
        Dependency function that checks the permission
    """
    async def check_permission(
        current_admin: User = Depends(get_current_admin_user)
    ) -> User:
        # For now, just check if user is superuser
        # In the future, this could check specific permissions
        if not getattr(current_admin, 'is_superuser', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_admin

    return check_permission
