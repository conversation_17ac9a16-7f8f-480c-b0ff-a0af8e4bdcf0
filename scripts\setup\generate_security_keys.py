#!/usr/bin/env python3
"""
Security Key Generator for AI Coding Agent

This script generates secure keys for your FastAPI admin dashboard.
Run this script to generate the required environment variables.

IMPORTANT: Store these keys securely and never commit them to version control!
"""

import secrets
import os
from pathlib import Path


def generate_secure_key(length: int = 32) -> str:
    """Generate a cryptographically secure random key."""
    return secrets.token_urlsafe(length)


def generate_encryption_salt() -> str:
    """Generate a secure salt for encryption."""
    return secrets.token_hex(16)


def main():
    """Generate all required security keys."""
    print("🔐 AI Coding Agent Security Key Generator")
    print("=" * 50)
    
    # Generate keys
    config_encryption_key = generate_secure_key(32)
    jwt_secret_key = generate_secure_key(32)
    encryption_salt = generate_encryption_salt()
    
    # Display keys
    print("\n📋 Add these to your .env file:")
    print("-" * 30)
    print(f"CONFIG_ENCRYPTION_KEY={config_encryption_key}")
    print(f"SECURITY_SECRET_KEY={jwt_secret_key}")
    print(f"ENCRYPTION_SALT={encryption_salt}")
    
    # Additional security settings
    print("\n🔧 Recommended security settings:")
    print("-" * 30)
    print("SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30")
    print("SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7")
    print("ADMIN_RATE_LIMIT_REQUESTS=20")
    print("ADMIN_RATE_LIMIT_WINDOW_MINUTES=5")
    print("AUTH_RATE_LIMIT_REQUESTS=5")
    print("AUTH_RATE_LIMIT_WINDOW_MINUTES=15")
    
    # Check if .env exists and offer to append
    env_file = Path(".env")
    if env_file.exists():
        print(f"\n📁 Found existing .env file: {env_file.absolute()}")
        response = input("Would you like to append these keys to your .env file? (y/N): ")
        
        if response.lower() in ['y', 'yes']:
            try:
                with open(env_file, 'a') as f:
                    f.write("\n# Security keys generated by generate_security_keys.py\n")
                    f.write(f"CONFIG_ENCRYPTION_KEY={config_encryption_key}\n")
                    f.write(f"SECURITY_SECRET_KEY={jwt_secret_key}\n")
                    f.write(f"ENCRYPTION_SALT={encryption_salt}\n")
                    f.write("\n# Security settings\n")
                    f.write("SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30\n")
                    f.write("SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7\n")
                    f.write("ADMIN_RATE_LIMIT_REQUESTS=20\n")
                    f.write("ADMIN_RATE_LIMIT_WINDOW_MINUTES=5\n")
                    f.write("AUTH_RATE_LIMIT_REQUESTS=5\n")
                    f.write("AUTH_RATE_LIMIT_WINDOW_MINUTES=15\n")
                
                print("✅ Keys successfully added to .env file!")
                
            except Exception as e:
                print(f"❌ Error writing to .env file: {e}")
                print("Please copy the keys manually.")
    else:
        print(f"\n📝 No .env file found. Create one at: {env_file.absolute()}")
        response = input("Would you like to create a new .env file with these keys? (y/N): ")
        
        if response.lower() in ['y', 'yes']:
            try:
                with open(env_file, 'w') as f:
                    f.write("# AI Coding Agent Environment Configuration\n")
                    f.write("# Generated by generate_security_keys.py\n\n")
                    f.write("# Application Settings\n")
                    f.write("APP_NAME=AI Coding Agent\n")
                    f.write("ENVIRONMENT=development\n")
                    f.write("DEBUG=true\n")
                    f.write("HOST=localhost\n")
                    f.write("PORT=8000\n\n")
                    f.write("# Security Keys (KEEP THESE SECRET!)\n")
                    f.write(f"CONFIG_ENCRYPTION_KEY={config_encryption_key}\n")
                    f.write(f"SECURITY_SECRET_KEY={jwt_secret_key}\n")
                    f.write(f"ENCRYPTION_SALT={encryption_salt}\n\n")
                    f.write("# Security Settings\n")
                    f.write("SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30\n")
                    f.write("SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7\n")
                    f.write("ADMIN_RATE_LIMIT_REQUESTS=20\n")
                    f.write("ADMIN_RATE_LIMIT_WINDOW_MINUTES=5\n")
                    f.write("AUTH_RATE_LIMIT_REQUESTS=5\n")
                    f.write("AUTH_RATE_LIMIT_WINDOW_MINUTES=15\n\n")
                    f.write("# Database Configuration (update as needed)\n")
                    f.write("DB_HOST=localhost\n")
                    f.write("DB_PORT=5432\n")
                    f.write("DB_NAME=ai_coding_agent\n")
                    f.write("DB_USER=postgres\n")
                    f.write("DB_PASSWORD=your_secure_password_here\n\n")
                    f.write("# AI Service Configuration\n")
                    f.write("OLLAMA_HOST=http://localhost:11434\n")
                    f.write("DEFAULT_AI_MODEL=mistral:7b-instruct-q4_0\n")
                
                print(f"✅ New .env file created at: {env_file.absolute()}")
                
            except Exception as e:
                print(f"❌ Error creating .env file: {e}")
                print("Please create the file manually.")
    
    print("\n🚨 IMPORTANT SECURITY NOTES:")
    print("-" * 30)
    print("1. Never commit these keys to version control")
    print("2. Store them securely (password manager, vault, etc.)")
    print("3. Rotate keys regularly (every 90 days)")
    print("4. Use different keys for different environments")
    print("5. Monitor for any unauthorized access attempts")
    
    print("\n🔧 Next Steps:")
    print("-" * 30)
    print("1. Restart your FastAPI application")
    print("2. Test admin authentication")
    print("3. Verify rate limiting is working")
    print("4. Check encryption/decryption of API keys")
    print("5. Review security logs")
    
    print("\n✅ Security setup complete!")


if __name__ == "__main__":
    main()
