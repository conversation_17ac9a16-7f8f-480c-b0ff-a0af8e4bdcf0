#!/bin/bash

# AI Coding Agent - Quick Start Script
# This script helps you get the multi-service Docker setup running quickly

set -e  # Exit on any error

echo "🚀 AI Coding Agent - Quick Start Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is installed
check_docker() {
    print_info "Checking Docker installation..."

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi

    print_status "Docker and Docker Compose are installed"
}

# Check if we're in the right directory
check_directory() {
    print_info "Checking project directory..."

    if [[ ! -f "docker-compose.yml" ]]; then
        print_error "docker-compose.yml not found. Please run this script from the project root."
        exit 1
    fi

    if [[ ! -d "backend" ]] || [[ ! -d "frontend" ]]; then
        print_error "Backend or frontend directory not found. Please ensure project structure is correct."
        exit 1
    fi

    print_status "Project directory structure is correct"
}

# Generate .env file if it doesn't exist
setup_environment() {
    print_info "Setting up environment configuration..."

    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            print_info "Copying .env.example to .env..."
            cp .env.example .env

            # Generate secure keys
            if command -v openssl &> /dev/null; then
                print_info "Generating secure keys..."
                SECRET_KEY=$(openssl rand -hex 32)
                CONFIG_KEY=$(openssl rand -hex 32)
                DB_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-16)

                # Update .env file with generated keys
                sed -i.bak "s/your_super_secret_key_that_must_be_at_least_32_characters_long/$SECRET_KEY/" .env
                sed -i.bak "s/your_config_encryption_key_32_chars_min/$CONFIG_KEY/" .env
                sed -i.bak "s/your_secure_database_password_here/$DB_PASSWORD/" .env

                # Remove backup file
                rm -f .env.bak

                print_status "Generated secure keys and updated .env file"
            else
                print_warning "OpenSSL not found. Please manually update the keys in .env file"
            fi
        else
            print_error ".env.example file not found. Cannot create .env file."
            exit 1
        fi
    else
        print_status ".env file already exists"
    fi
}

# Pull required Docker images
pull_images() {
    print_info "Pulling required Docker images..."

    # Pull base images
    docker pull python:3.11-slim
    docker pull node:18-alpine
    docker pull postgres:15-alpine
    docker pull ollama/ollama:latest

    print_status "Base Docker images pulled"
}

# Build the application images
build_images() {
    print_info "Building application images..."

    # Build all services
    docker-compose build --no-cache

    print_status "Application images built successfully"
}

# Setup infrastructure configuration
setup_infrastructure() {
    print_info "Setting up infrastructure configuration..."

    # Check if infrastructure files exist
    if [[ ! -f "infrastructure/nginx/default.conf" ]]; then
        print_error "Infrastructure configuration files missing. Please run the infrastructure setup first."
        return 1
    fi

    # Generate SSL certificates for development
    if [[ ! -f "infrastructure/ssl/certs/fullchain.pem" ]]; then
        print_info "Generating development SSL certificates..."
        if [[ -f "infrastructure/ssl/generate-certs.sh" ]]; then
            chmod +x infrastructure/ssl/generate-certs.sh
            ENVIRONMENT=development DOMAIN=ai-coding-agent.local ./infrastructure/ssl/generate-certs.sh
        else
            print_warning "SSL certificate generation script not found"
        fi
    fi

    # Initialize vector database
    print_info "Initializing vector database..."
    if [[ -f "vector-db/init/setup-collections.py" ]]; then
        chmod +x vector-db/init/setup-collections.py
        print_status "Vector database initialization script prepared"
    else
        print_warning "Vector database initialization script not found"
    fi

    print_status "Infrastructure configuration completed"
}

# Start the services
start_services() {
    print_info "Starting all services..."

    # Start services in detached mode
    docker-compose up -d

    print_status "Services started in background"
}

# Wait for services to be healthy
wait_for_services() {
    print_info "Waiting for services to be ready..."

    # Wait for backend health check
    echo -n "Waiting for backend"
    for i in {1..30}; do
        if curl -f http://localhost:8000/api/v1/health &> /dev/null; then
            echo ""
            print_status "Backend is ready"
            break
        fi
        echo -n "."
        sleep 2
    done

    # Wait for frontend
    echo -n "Waiting for frontend"
    for i in {1..30}; do
        if curl -f http://localhost:3000 &> /dev/null; then
            echo ""
            print_status "Frontend is ready"
            break
        fi
        echo -n "."
        sleep 2
    done

    # Check vector database
    echo -n "Waiting for vector database"
    for i in {1..20}; do
        if curl -f http://localhost:8001/api/v1/heartbeat &> /dev/null; then
            echo ""
            print_status "Vector database is ready"
            break
        fi
        echo -n "."
        sleep 2
    done
}

# Show service status
show_status() {
    print_info "Service Status:"
    docker-compose ps

    echo ""
    print_info "Service URLs:"
    echo "🌐 Frontend:        http://localhost:3000"
    echo "🔧 Backend API:     http://localhost:8000"
    echo "📊 API Health:      http://localhost:8000/api/v1/health"
    echo "🗄️  Vector DB:       http://localhost:8001"
    echo "🤖 Ollama:          http://localhost:11434"
    echo "🐘 PostgreSQL:      localhost:5432"
}

# Main execution
main() {
    echo ""
    print_info "Starting AI Coding Agent setup..."

    check_docker
    check_directory
    setup_environment
    setup_infrastructure

    echo ""
    print_info "Building and starting services..."
    pull_images
    build_images
    start_services

    echo ""
    wait_for_services

    echo ""
    show_status

    echo ""
    echo "🎉 Setup complete! Your AI Coding Agent is now running."
    echo ""
    echo "📝 Next steps:"
    echo "1. Open http://localhost:3000 in your browser"
    echo "2. Check the API health at http://localhost:8000/api/v1/health"
    echo "3. View logs with: docker-compose logs -f"
    echo "4. Stop services with: docker-compose down"
    echo ""
    echo "📚 For more information, see the README.md file."
}

# Run main function
main
