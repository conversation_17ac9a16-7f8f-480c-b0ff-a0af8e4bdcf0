# AI Coding Agent - SSL Certificate Configuration
# Configuration file for SSL certificate management

# Certificate Configuration
certificate:
  # Domain configuration
  domain: "ai-coding-agent.local"
  alt_names:
    - "localhost"
    - "*.ai-coding-agent.local"
    - "api.ai-coding-agent.local"
    - "admin.ai-coding-agent.local"
  
  # Certificate details
  country: "US"
  state: "Development"
  locality: "Local"
  organization: "AI Coding Agent"
  organizational_unit: "Development"
  
  # Key configuration
  key_size: 2048
  key_algorithm: "RSA"
  
  # Certificate validity
  validity_days: 365
  hash_algorithm: "sha256"

# Environment-specific settings
environments:
  development:
    certificate_type: "self-signed"
    auto_generate: true
    trust_self_signed: true
    paths:
      cert_dir: "./infrastructure/ssl/certs"
      private_key: "privkey.pem"
      certificate: "fullchain.pem"
      chain: "chain.pem"
    
  production:
    certificate_type: "letsencrypt"
    auto_renew: true
    renewal_days_before_expiry: 30
    email: "<EMAIL>"
    paths:
      cert_dir: "/etc/letsencrypt/live"
      private_key: "privkey.pem"
      certificate: "fullchain.pem"
      chain: "chain.pem"
    
    # Let's Encrypt configuration
    letsencrypt:
      server: "https://acme-v02.api.letsencrypt.org/directory"
      challenge_type: "http-01"
      webroot_path: "/var/www/certbot"
      
    # Renewal configuration
    renewal:
      check_interval: "12h"
      retry_attempts: 3
      retry_delay: "1h"
      notification_email: "<EMAIL>"

# SSL/TLS Security Configuration
security:
  # Supported TLS versions
  tls_versions:
    - "TLSv1.2"
    - "TLSv1.3"
  
  # Cipher suites (modern configuration)
  cipher_suites:
    - "ECDHE-RSA-AES256-GCM-SHA512"
    - "DHE-RSA-AES256-GCM-SHA512"
    - "ECDHE-RSA-AES256-GCM-SHA384"
    - "DHE-RSA-AES256-GCM-SHA384"
    - "ECDHE-RSA-AES256-SHA384"
    - "ECDHE-RSA-AES128-GCM-SHA256"
    - "ECDHE-RSA-AES128-SHA256"
  
  # Security headers
  headers:
    strict_transport_security:
      enabled: true
      max_age: 31536000
      include_subdomains: true
      preload: true
    
    content_security_policy:
      enabled: true
      policy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss:; frame-ancestors 'none';"
    
    x_frame_options:
      enabled: true
      value: "SAMEORIGIN"
    
    x_content_type_options:
      enabled: true
      value: "nosniff"
    
    x_xss_protection:
      enabled: true
      value: "1; mode=block"
    
    referrer_policy:
      enabled: true
      value: "strict-origin-when-cross-origin"
  
  # OCSP Stapling
  ocsp_stapling:
    enabled: true
    verify: true
    resolver: "******* *******"
    resolver_timeout: "5s"
  
  # Session configuration
  session:
    cache: "shared:SSL:10m"
    timeout: "10m"
    tickets: false

# Monitoring and Alerting
monitoring:
  # Certificate expiry monitoring
  expiry_check:
    enabled: true
    warning_days: 30
    critical_days: 7
    check_interval: "24h"
  
  # Health checks
  health_checks:
    enabled: true
    endpoints:
      - "/health"
      - "/api/v1/health"
    check_interval: "5m"
    timeout: "10s"
  
  # Logging
  logging:
    access_log: "/var/log/nginx/ssl_access.log"
    error_log: "/var/log/nginx/ssl_error.log"
    log_level: "warn"
    
    # Log format for SSL
    ssl_log_format: |
      $remote_addr - $remote_user [$time_local] "$request" 
      $status $body_bytes_sent "$http_referer" 
      "$http_user_agent" "$http_x_forwarded_for" 
      rt=$request_time uct="$upstream_connect_time" 
      uht="$upstream_header_time" urt="$upstream_response_time" 
      ssl_protocol=$ssl_protocol ssl_cipher=$ssl_cipher

# Backup and Recovery
backup:
  enabled: true
  retention_days: 90
  backup_location: "./infrastructure/ssl/backups"
  schedule: "0 2 * * *"  # Daily at 2 AM
  
  # What to backup
  include:
    - "certificates"
    - "private_keys"
    - "configuration"
  
  # Encryption for backups
  encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key_derivation: "PBKDF2"

# Validation Rules
validation:
  # Certificate validation
  certificate:
    check_expiry: true
    check_chain: true
    check_revocation: true
    check_hostname: true
  
  # Key validation
  private_key:
    check_strength: true
    min_key_size: 2048
    check_permissions: true
  
  # Configuration validation
  config:
    check_syntax: true
    check_security: true
    check_compatibility: true

# Integration Settings
integration:
  # Docker integration
  docker:
    enabled: true
    container_name: "nginx"
    volume_mount: "/etc/nginx/ssl"
    reload_command: "nginx -s reload"
  
  # Kubernetes integration
  kubernetes:
    enabled: false
    namespace: "ai-coding-agent"
    secret_name: "tls-secret"
    ingress_class: "nginx"
  
  # Cloud provider integration
  cloud:
    provider: null  # aws, gcp, azure
    certificate_manager: null  # acm, google-managed, key-vault
