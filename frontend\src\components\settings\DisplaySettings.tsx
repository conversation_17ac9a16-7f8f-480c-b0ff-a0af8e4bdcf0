/**
 * Display Settings Component
 * Handles theme, language, timezone, and layout preferences
 */

import React from 'react';
import { Moon, Sun, Monitor, Globe, Clock, Layout, Eye } from 'lucide-react';
import { usePreferences } from '../../contexts/PreferencesContext';
import Button from '../ui/Button';

const themes = [
  { value: 'light', label: 'Light', icon: Sun },
  { value: 'dark', label: 'Dark', icon: Moon },
  { value: 'system', label: 'System', icon: Monitor },
];

const languages = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'it', label: 'Italiano' },
  { value: 'pt', label: 'Português' },
  { value: 'ru', label: 'Русский' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'zh', label: '中文' },
];

const timezones = [
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'London (GMT)' },
  { value: 'Europe/Paris', label: 'Paris (CET)' },
  { value: 'Europe/Berlin', label: 'Berlin (CET)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
  { value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
  { value: 'Asia/Kolkata', label: 'India (IST)' },
  { value: 'Australia/Sydney', label: 'Sydney (AEST)' },
];

const dateFormats = [
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY (US)' },
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY (EU)' },
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD (ISO)' },
];

const timeFormats = [
  { value: '12h', label: '12-hour (AM/PM)' },
  { value: '24h', label: '24-hour' },
];

const densityOptions = [
  { value: 'compact', label: 'Compact', description: 'More content, less spacing' },
  { value: 'comfortable', label: 'Comfortable', description: 'Balanced spacing' },
  { value: 'spacious', label: 'Spacious', description: 'More spacing, easier to read' },
];

export const DisplaySettings: React.FC = () => {
  const { preferences, updateSection } = usePreferences();
  const { display } = preferences;

  const handleThemeChange = (theme: string) => {
    updateSection('display', { theme: theme as any });
  };

  const handleLanguageChange = (language: string) => {
    updateSection('display', { language });
  };

  const handleTimezoneChange = (timezone: string) => {
    updateSection('display', { timezone });
  };

  const handleDateFormatChange = (dateFormat: string) => {
    updateSection('display', { dateFormat: dateFormat as any });
  };

  const handleTimeFormatChange = (timeFormat: string) => {
    updateSection('display', { timeFormat: timeFormat as any });
  };

  const handleDensityChange = (density: string) => {
    updateSection('display', { density: density as any });
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Display Settings
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Customize how the interface looks and feels
        </p>
      </div>

      {/* Theme Selection */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
          <Sun className="h-4 w-4" />
          Theme
        </h4>
        <div className="grid grid-cols-3 gap-3">
          {themes.map((theme) => {
            const Icon = theme.icon;
            return (
              <Button
                key={theme.value}
                variant={display.theme === theme.value ? 'primary' : 'outline'}
                onClick={() => handleThemeChange(theme.value)}
                className="justify-start"
              >
                <Icon className="h-4 w-4 mr-2" />
                {theme.label}
              </Button>
            );
          })}
        </div>
      </div>

      {/* Language Selection */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
          <Globe className="h-4 w-4" />
          Language
        </h4>
        <select
          value={display.language}
          onChange={(e) => handleLanguageChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
        >
          {languages.map((lang) => (
            <option key={lang.value} value={lang.value}>
              {lang.label}
            </option>
          ))}
        </select>
      </div>

      {/* Timezone Selection */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
          <Clock className="h-4 w-4" />
          Timezone
        </h4>
        <select
          value={display.timezone}
          onChange={(e) => handleTimezoneChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
        >
          {timezones.map((tz) => (
            <option key={tz.value} value={tz.value}>
              {tz.label}
            </option>
          ))}
        </select>
      </div>

      {/* Date and Time Format */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            Date Format
          </h4>
          <div className="space-y-2">
            {dateFormats.map((format) => (
              <label key={format.value} className="flex items-center">
                <input
                  type="radio"
                  name="dateFormat"
                  value={format.value}
                  checked={display.dateFormat === format.value}
                  onChange={(e) => handleDateFormatChange(e.target.value)}
                  className="mr-3 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="text-gray-900 dark:text-white">{format.label}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            Time Format
          </h4>
          <div className="space-y-2">
            {timeFormats.map((format) => (
              <label key={format.value} className="flex items-center">
                <input
                  type="radio"
                  name="timeFormat"
                  value={format.value}
                  checked={display.timeFormat === format.value}
                  onChange={(e) => handleTimeFormatChange(e.target.value)}
                  className="mr-3 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="text-gray-900 dark:text-white">{format.label}</span>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Layout Density */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
          <Layout className="h-4 w-4" />
          Layout Density
        </h4>
        <div className="space-y-3">
          {densityOptions.map((option) => (
            <label key={option.value} className="flex items-start">
              <input
                type="radio"
                name="density"
                value={option.value}
                checked={display.density === option.value}
                onChange={(e) => handleDensityChange(e.target.value)}
                className="mt-1 mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <div>
                <div className="text-gray-900 dark:text-white font-medium">
                  {option.label}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {option.description}
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};
