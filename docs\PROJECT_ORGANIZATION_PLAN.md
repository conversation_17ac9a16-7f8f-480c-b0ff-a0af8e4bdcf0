# AI Coding Agent - Project Organization Plan

## **Current Issues**
1. **Root Directory Clutter**: 50+ loose files in root directory
2. **Mixed File Types**: Scripts, configs, docs, databases scattered everywhere
3. **Container File Placement**: Files not properly organized for Docker containers
4. **Development vs Production**: No clear separation of environments

## **Target Structure**

```
ai-coding-agent/
├── README.md                          # Main project README
├── docker-compose.yml                 # Production compose
├── docker-compose.dev.yml             # Development compose
├── .env.example                       # Environment template
├── .gitignore                         # Git ignore rules
├── pyproject.toml                     # Python project config
├── requirements.txt                   # Core dependencies
│
├── backend/                           # Backend container files
│   ├── Dockerfile                     # Backend container
│   ├── requirements.txt               # Backend dependencies
│   ├── src/                          # Source code
│   │   └── ai_coding_agent/          # Main application
│   ├── config/                       # Configuration files
│   ├── scripts/                      # Backend utility scripts
│   └── tests/                        # Backend tests
│
├── frontend/                         # Frontend container files
│   ├── Dockerfile                    # Frontend container
│   ├── nginx.conf                    # Nginx configuration
│   ├── package.json                  # Node dependencies
│   ├── src/                         # React source code
│   ├── public/                      # Static assets
│   └── build/                       # Production build
│
├── database/                        # Database container files
│   ├── Dockerfile                   # Custom DB container (if needed)
│   ├── init/                       # Database initialization scripts
│   ├── migrations/                 # Database migrations
│   ├── schemas/                    # Database schemas
│   └── backups/                    # Database backups
│
├── infrastructure/                  # Infrastructure & deployment
│   ├── docker/                     # Docker configurations
│   ├── nginx/                      # Nginx configurations
│   ├── ssl/                        # SSL certificates
│   └── monitoring/                 # Monitoring configs
│
├── scripts/                        # Development & utility scripts
│   ├── setup/                      # Setup scripts
│   ├── development/                # Development utilities
│   ├── deployment/                 # Deployment scripts
│   └── maintenance/                # Maintenance scripts
│
├── docs/                           # Documentation
│   ├── api/                        # API documentation
│   ├── deployment/                 # Deployment guides
│   ├── development/                # Development guides
│   └── architecture/               # Architecture docs
│
├── tests/                          # Test suites
│   ├── unit/                       # Unit tests
│   ├── integration/                # Integration tests
│   ├── e2e/                        # End-to-end tests
│   └── fixtures/                   # Test fixtures
│
├── data/                           # Data storage
│   ├── vector_db/                  # Vector database files
│   ├── logs/                       # Application logs
│   ├── uploads/                    # File uploads
│   └── backups/                    # Data backups
│
├── examples/                       # Example files
│   ├── configs/                    # Example configurations
│   ├── projects/                   # Example projects
│   └── templates/                  # Project templates
│
└── tools/                          # Development tools
    ├── ltkb/                       # LTKB system files
    ├── monitoring/                 # Monitoring tools
    └── utilities/                  # Utility tools
```

## **File Organization Tasks**

### **Phase 1: Create Directory Structure**
- [ ] Create all target directories
- [ ] Set up proper .gitignore patterns
- [ ] Create directory README files

### **Phase 2: Backend Container Organization**
- [ ] Move `src/ai_coding_agent/` to `backend/src/ai_coding_agent/`
- [ ] Move backend configs to `backend/config/`
- [ ] Move backend scripts to `backend/scripts/`
- [ ] Update Dockerfile paths
- [ ] Update docker-compose backend paths

### **Phase 3: Frontend Container Organization**
- [ ] Verify frontend structure in `frontend/`
- [ ] Ensure all frontend assets are containerized
- [ ] Update nginx configuration paths
- [ ] Verify build process works in container

### **Phase 4: Database Container Organization**
- [ ] Move database schemas to `database/schemas/`
- [ ] Move migration scripts to `database/migrations/`
- [ ] Create database initialization scripts
- [ ] Set up database backup procedures

### **Phase 5: Root Directory Cleanup**
- [ ] Move loose Python scripts to appropriate directories
- [ ] Move documentation files to `docs/`
- [ ] Move configuration files to appropriate containers
- [ ] Move test files to `tests/`
- [ ] Remove temporary/generated files

## **Container File Mapping**

### **Backend Container Should Include:**
- All Python source code (`src/ai_coding_agent/`)
- Backend configuration files
- Python dependencies (`requirements.txt`)
- Backend-specific scripts
- API documentation
- Backend tests

### **Frontend Container Should Include:**
- React source code (`frontend/src/`)
- Node.js dependencies (`package.json`)
- Build configuration (`webpack`, `vite`, etc.)
- Static assets (`public/`)
- Nginx configuration
- Frontend tests

### **Database Container Should Include:**
- Database schemas
- Migration scripts
- Initialization scripts
- Database-specific configurations
- Backup/restore scripts

## **Next Steps**
1. Execute Phase 1: Create directory structure
2. Execute Phase 2: Organize backend files
3. Execute Phase 3: Verify frontend organization
4. Execute Phase 4: Organize database files
5. Execute Phase 5: Clean up root directory
6. Update all Docker configurations
7. Test all containers work properly
8. Update documentation
