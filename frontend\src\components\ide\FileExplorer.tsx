import React, { useState } from 'react';
import {
  Folder,
  File,
  ChevronRight,
  ChevronDown,
  Plus,
  RefreshCw,
  MoreHorizontal,
  Edit,
  Trash2
} from 'lucide-react';
import { FileNode } from '../../types/ide';
import {
  validateFilePath,
  validateFileName,
  validateFileSize,
  sanitizeContent,
  secureFileOperation
} from '../../utils/ideValidation';

interface FileExplorerProps {
  files: FileNode[];
  onFileSelect: (file: FileNode) => void;
  onFileCreate: (parentPath: string, name: string, type: 'file' | 'folder') => void;
  onFileDelete: (filePath: string) => void;
  onFileRename: (filePath: string, newName: string) => void;
  onRefresh: () => void;
  selectedFile?: string;
  className?: string;
}

interface FileTreeItemProps {
  file: FileNode;
  level: number;
  onSelect: (file: FileNode) => void;
  onToggle: (file: FileNode) => void;
  selectedFile?: string;
  expandedFolders: Set<string>;
}

const FileTreeItem: React.FC<FileTreeItemProps> = ({
  file,
  level,
  onSelect,
  onToggle,
  selectedFile,
  expandedFolders
}) => {
  const [showContextMenu, setShowContextMenu] = useState(false);
  const isExpanded = expandedFolders.has(file.id);
  const isSelected = selectedFile === file.path;

  const handleClick = () => {
    if (file.type === 'folder') {
      onToggle(file);
    } else {
      onSelect(file);
    }
  };

  const getFileIcon = () => {
    if (file.type === 'folder') {
      return <Folder size={16} />;
    }
    return <File size={16} />;
  };

  const getFileTypeColor = () => {
    if (file.type === 'folder') return 'text-blue-400';

    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'tsx':
      case 'ts':
        return 'text-blue-300';
      case 'jsx':
      case 'js':
        return 'text-yellow-300';
      case 'css':
      case 'scss':
        return 'text-green-300';
      case 'html':
        return 'text-orange-300';
      case 'json':
        return 'text-purple-300';
      case 'md':
        return 'text-gray-300';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="select-none">
      <div
        className={`
          flex items-center py-1 px-2 cursor-pointer rounded text-sm
          hover:bg-gray-700 transition-colors duration-150
          ${isSelected ? 'bg-blue-600 text-white' : 'text-gray-300'}
        `}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
        onContextMenu={(e) => {
          e.preventDefault();
          setShowContextMenu(true);
        }}
      >
        {file.type === 'folder' && (
          <span className="mr-1 text-gray-400">
            {isExpanded ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
          </span>
        )}

        <span className={`mr-2 ${getFileTypeColor()}`}>
          {getFileIcon()}
        </span>

        <span className="flex-1 truncate">
          {file.name}
        </span>

        {file.size && file.type === 'file' && (
          <span className="text-xs text-gray-500 ml-2">
            {formatFileSize(file.size)}
          </span>
        )}

        <button
          className="opacity-0 group-hover:opacity-100 ml-1 p-1 hover:bg-gray-600 rounded"
          onClick={(e) => {
            e.stopPropagation();
            setShowContextMenu(true);
          }}
        >
          <MoreHorizontal size={12} />
        </button>
      </div>

      {/* Children */}
      {file.type === 'folder' && file.children && isExpanded && (
        <div>
          {file.children.map((child) => (
            <FileTreeItem
              key={child.id}
              file={child}
              level={level + 1}
              onSelect={onSelect}
              onToggle={onToggle}
              selectedFile={selectedFile}
              expandedFolders={expandedFolders}
            />
          ))}
        </div>
      )}

      {/* Context Menu */}
      {showContextMenu && (
        <div
          className="fixed bg-gray-800 border border-gray-600 rounded shadow-lg py-1 z-50"
          style={{
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}
          onMouseLeave={() => setShowContextMenu(false)}
        >
          <button className="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full">
            <Edit size={14} className="mr-2" />
            Rename
          </button>
          <button className="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full">
            <Trash2 size={14} className="mr-2" />
            Delete
          </button>
          {file.type === 'folder' && (
            <>
              <hr className="border-gray-600 my-1" />
              <button className="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full">
                <File size={14} className="mr-2" />
                New File
              </button>
              <button className="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full">
                <Folder size={14} className="mr-2" />
                New Folder
              </button>
            </>
          )}
        </div>
      )}
    </div>
  );
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round((bytes / Math.pow(k, i)) * 100) / 100 + ' ' + sizes[i];
};

export const FileExplorer: React.FC<FileExplorerProps> = ({
  files,
  onFileSelect,
  onFileCreate,
  onFileDelete,
  onFileRename,
  onRefresh,
  selectedFile,
  className = ''
}) => {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(['1']) // Expand src folder by default
  );
  const [showNewFileDialog, setShowNewFileDialog] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [newFileType, setNewFileType] = useState<'file' | 'folder'>('file');

  const handleToggleFolder = (file: FileNode) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(file.id)) {
      newExpanded.delete(file.id);
    } else {
      newExpanded.add(file.id);
    }
    setExpandedFolders(newExpanded);
  };

  const handleCreateFile = () => {
    if (newFileName.trim()) {
      onFileCreate('/', newFileName.trim(), newFileType);
      setNewFileName('');
      setShowNewFileDialog(false);
    }
  };

  return (
    <div className={`h-full bg-gray-800 border-r border-gray-700 flex flex-col ${className}`}>
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-3 py-2 flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-300">Explorer</h3>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setShowNewFileDialog(true)}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            title="New File"
          >
            <Plus size={14} />
          </button>
          <button
            onClick={onRefresh}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            title="Refresh"
          >
            <RefreshCw size={14} />
          </button>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto">
        {files.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <Folder size={32} className="mx-auto mb-2 opacity-50" />
            <p className="text-sm">No files in workspace</p>
            <button
              onClick={() => setShowNewFileDialog(true)}
              className="mt-2 text-xs text-blue-400 hover:text-blue-300"
            >
              Create your first file
            </button>
          </div>
        ) : (
          <div className="py-1">
            {files.map((file) => (
              <FileTreeItem
                key={file.id}
                file={file}
                level={0}
                onSelect={onFileSelect}
                onToggle={handleToggleFolder}
                selectedFile={selectedFile}
                expandedFolders={expandedFolders}
              />
            ))}
          </div>
        )}
      </div>

      {/* New File Dialog */}
      {showNewFileDialog && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-4 w-80">
            <h4 className="text-lg font-medium text-white mb-4">Create New {newFileType}</h4>

            <div className="mb-4">
              <div className="flex space-x-2 mb-3">
                <button
                  onClick={() => setNewFileType('file')}
                  className={`px-3 py-1 text-sm rounded ${
                    newFileType === 'file'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  File
                </button>
                <button
                  onClick={() => setNewFileType('folder')}
                  className={`px-3 py-1 text-sm rounded ${
                    newFileType === 'folder'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  Folder
                </button>
              </div>

              <input
                type="text"
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                placeholder={`Enter ${newFileType} name...`}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateFile();
                  } else if (e.key === 'Escape') {
                    setShowNewFileDialog(false);
                  }
                }}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowNewFileDialog(false)}
                className="px-3 py-2 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateFile}
                disabled={!newFileName.trim()}
                className="px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
