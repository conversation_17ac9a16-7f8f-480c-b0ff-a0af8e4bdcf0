import { useQuery } from '@tanstack/react-query';
import { useCurrentUser } from './useAuth';
import { AuthUser } from '../services/supabaseAuth';

interface AdminUser extends AuthUser {
  is_superuser: boolean;
  admin_permissions: string[];
}

/**
 * Hook to check if current user has admin privileges
 */
export const useAdminAuth = () => {
  const { data: user, isLoading: userLoading, error: userError } = useCurrentUser();

  const {
    data: adminUser,
    isLoading: adminLoading,
    error: adminError,
    refetch: refetchAdmin
  } = useQuery({
    queryKey: ['admin-auth', user?.id],
    queryFn: async (): Promise<AdminUser | null> => {
      if (!user) return null;

      try {
        // Check admin status with backend
        const response = await fetch('/api/v1/admin/auth/check', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.status === 403) {
          // User is authenticated but not admin
          return null;
        }

        if (!response.ok) {
          throw new Error('Failed to check admin status');
        }

        const adminData = await response.json();
        return {
          ...user,
          is_superuser: adminData.is_superuser,
          admin_permissions: adminData.permissions || []
        };
      } catch (error) {
        console.error('Admin auth check failed:', error);
        return null;
      }
    },
    enabled: !!user && !userLoading,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 403 (not admin)
      if (error?.status === 403) return false;
      return failureCount < 2;
    },
  });

  return {
    user: adminUser,
    isAdmin: !!adminUser?.is_superuser,
    isLoading: userLoading || adminLoading,
    error: userError || adminError,
    refetch: refetchAdmin,
    hasPermission: (permission: string) => {
      return adminUser?.admin_permissions?.includes(permission) || adminUser?.is_superuser || false;
    }
  };
};

/**
 * Hook for admin-only operations
 */
export const useAdminOperations = () => {
  const { isAdmin, user, hasPermission } = useAdminAuth();

  const requireAdmin = (operation: string = 'admin access') => {
    if (!isAdmin) {
      throw new Error(`Admin privileges required for ${operation}`);
    }
  };

  const requirePermission = (permission: string) => {
    if (!hasPermission(permission)) {
      throw new Error(`Permission '${permission}' required`);
    }
  };

  return {
    isAdmin,
    user,
    requireAdmin,
    requirePermission,
    hasPermission
  };
};
