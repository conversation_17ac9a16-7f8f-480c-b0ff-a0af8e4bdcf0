"""
Audit Trail Service Layer
Implements comprehensive audit logging and tracking for Phase B1 enhancements.
"""

import json
from datetime import datetime, timedelta, timezone, date
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from uuid import uuid4, UUID

from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_, desc, event
from fastapi import HTTPException
from fastapi import status as http_status

from ..models import (
    AuditLog, StatusHistory, ConcurrencyControl,
    AuditAction, AuditEntityType,
    AuditLogResponse, StatusHistoryResponse, ConcurrencyControlResponse,
    AuditLogFilter, ConflictResolution
)


def serialize_for_json(obj: Any) -> Any:
    """
    Recursively convert any non-JSON-serializable Python objects into
    JSON-safe formats. Essential for audit trail data integrity.

    Handles:
    - datetime/date → ISO format strings
    - Decimal → float or int
    - UUID → string representation
    - bytes → UTF-8 decoded strings
    - Pydantic models → dict representation
    - Custom objects → dict or string fallback
    - Nested structures → recursively processed
    """
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj) if obj % 1 else int(obj)
    elif isinstance(obj, UUID):
        return str(obj)
    elif isinstance(obj, bytes):
        return obj.decode("utf-8", errors="replace")
    elif hasattr(obj, 'dict') and callable(getattr(obj, 'dict')):
        # Handle Pydantic models
        return serialize_for_json(obj.dict())
    elif hasattr(obj, 'model_dump') and callable(getattr(obj, 'model_dump')):
        # Handle Pydantic v2 models
        return serialize_for_json(obj.model_dump())
    elif hasattr(obj, '__dict__'):
        # Handle custom objects with __dict__
        return serialize_for_json(obj.__dict__)
    elif isinstance(obj, dict):
        return {k: serialize_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_json(v) for v in obj]
    elif isinstance(obj, tuple):
        return tuple(serialize_for_json(v) for v in obj)
    elif isinstance(obj, set):
        return list(serialize_for_json(v) for v in obj)
    elif obj is None or isinstance(obj, (int, float, str, bool)):
        return obj
    else:
        # Fallback: try stringifying
        try:
            return str(obj)
        except Exception:
            return f"<non-serializable: {type(obj).__name__}>"


def to_json(data: Any, **kwargs) -> str:
    """
    Convert any Python object to a JSON string safely.
    Perfect for audit trail storage.
    """
    return json.dumps(serialize_for_json(data), **kwargs)


def safe_audit_data(data: Any) -> Any:
    """
    Prepare data for audit storage by ensuring JSON serializability.
    Use this before storing any audit data.
    """
    return serialize_for_json(data)


def setup_audit_serialization_hooks():
    """
    Set up SQLAlchemy event hooks to automatically serialize audit data
    before database insertion. Call this once during app initialization.
    """
    @event.listens_for(AuditLog, 'before_insert')
    def serialize_audit_log_before_insert(mapper, connection, target):
        """Automatically serialize audit log data before database insertion."""
        if target.old_values:
            target.old_values = serialize_for_json(target.old_values)
        if target.new_values:
            target.new_values = serialize_for_json(target.new_values)
        if target.metadata:
            target.metadata = serialize_for_json(target.metadata)

    @event.listens_for(AuditLog, 'before_update')
    def serialize_audit_log_before_update(mapper, connection, target):
        """Automatically serialize audit log data before database update."""
        if target.old_values:
            target.old_values = serialize_for_json(target.old_values)
        if target.new_values:
            target.new_values = serialize_for_json(target.new_values)
        if target.metadata:
            target.metadata = serialize_for_json(target.metadata)


class AuditService:
    """Service for managing audit trails and change tracking."""

    def __init__(self, db: Session):
        self.db = db

    @staticmethod
    def _utc_now() -> datetime:
        """Get current UTC time in a timezone-aware manner."""
        return datetime.now(timezone.utc)

    @staticmethod
    def _serialize_for_json(data: Any) -> Any:
        """
        Convert any objects to JSON-safe formats using our comprehensive serializer.
        This replaces the old limited datetime-only serialization.
        """
        return serialize_for_json(data)

    # Audit Logging Methods

    def log_action(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        action: AuditAction,
        user_id: Optional[str] = None,
        user_email: Optional[str] = None,
        old_values: Optional[Dict] = None,
        new_values: Optional[Dict] = None,
        changed_fields: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> AuditLogResponse:
        """Log an audit action."""
        try:
            # Convert datetime objects to strings for JSON serialization
            if old_values:
                old_values = self._serialize_for_json(old_values)
            if new_values:
                new_values = self._serialize_for_json(new_values)
            if metadata:
                metadata = self._serialize_for_json(metadata)

            # Determine changed fields if not provided
            if changed_fields is None and old_values and new_values:
                changed_fields = []
                for key, new_value in new_values.items():
                    old_value = old_values.get(key)
                    if old_value != new_value:
                        changed_fields.append(key)

            audit_log = AuditLog(
                id=str(uuid4()),
                entity_type=entity_type.value,
                entity_id=entity_id,
                action=action.value,
                user_id=user_id,
                user_email=user_email,
                old_values=old_values,
                new_values=new_values,
                changed_fields=changed_fields or [],
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                reason=reason,
                audit_metadata=metadata
            )

            self.db.add(audit_log)
            self.db.commit()
            self.db.refresh(audit_log)

            return AuditLogResponse.model_validate(audit_log)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to log audit action: {str(e)}"
            )

    def get_audit_logs(self, filters: AuditLogFilter) -> List[AuditLogResponse]:
        """Get audit logs with filtering."""
        query = select(AuditLog)

        # Apply filters
        conditions = []
        if filters.entity_type:
            conditions.append(AuditLog.entity_type == filters.entity_type.value)
        if filters.entity_id:
            conditions.append(AuditLog.entity_id == filters.entity_id)
        if filters.action:
            conditions.append(AuditLog.action == filters.action.value)
        if filters.user_id:
            conditions.append(AuditLog.user_id == filters.user_id)
        if filters.start_date:
            conditions.append(AuditLog.created_at >= filters.start_date)
        if filters.end_date:
            conditions.append(AuditLog.created_at <= filters.end_date)

        if conditions:
            query = query.where(and_(*conditions))

        # Order by creation time (newest first)
        query = query.order_by(desc(AuditLog.created_at))

        # Apply pagination
        query = query.offset(filters.offset).limit(filters.limit)

        audit_logs = self.db.execute(query).scalars().all()
        return [AuditLogResponse.model_validate(log) for log in audit_logs]

    def get_entity_audit_trail(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        limit: int = 100
    ) -> List[AuditLogResponse]:
        """Get complete audit trail for a specific entity."""
        filters = AuditLogFilter(
            entity_type=entity_type,
            entity_id=entity_id,
            limit=limit
        )
        return self.get_audit_logs(filters)

    # Status History Methods

    def log_status_change(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        old_status: Optional[str],
        new_status: str,
        user_id: Optional[str] = None,
        triggered_by: str = "user",
        reason: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> StatusHistoryResponse:
        """Log a status change with duration tracking."""
        try:
            # Calculate duration in previous status
            duration_in_previous_status = None
            if old_status:
                last_status_change = (
                    self.db.execute(
                        select(StatusHistory)
                        .where(
                            and_(
                                StatusHistory.entity_type == entity_type.value,
                                StatusHistory.entity_id == entity_id,
                                StatusHistory.new_status == old_status
                            )
                        )
                        .order_by(desc(StatusHistory.created_at))
                        .limit(1)
                    )
                    .scalar_one_or_none()
                )

                if last_status_change:
                    duration = self._utc_now() - last_status_change.created_at
                    duration_in_previous_status = int(duration.total_seconds())

            status_history = StatusHistory(
                id=str(uuid4()),
                entity_type=entity_type.value,
                entity_id=entity_id,
                old_status=old_status,
                new_status=new_status,
                user_id=user_id,
                triggered_by=triggered_by,
                reason=reason,
                duration_in_previous_status=duration_in_previous_status,
                status_metadata=metadata
            )

            self.db.add(status_history)
            self.db.commit()
            self.db.refresh(status_history)

            return StatusHistoryResponse.model_validate(status_history)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to log status change: {str(e)}"
            )

    def get_status_history(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        limit: int = 50
    ) -> List[StatusHistoryResponse]:
        """Get status change history for an entity."""
        status_history = (
            self.db.execute(
                select(StatusHistory)
                .where(
                    and_(
                        StatusHistory.entity_type == entity_type.value,
                        StatusHistory.entity_id == entity_id
                    )
                )
                .order_by(desc(StatusHistory.created_at))
                .limit(limit)
            )
            .scalars()
            .all()
        )

        return [StatusHistoryResponse.model_validate(history) for history in status_history]

    # Concurrency Control Methods

    def get_or_create_concurrency_control(
        self,
        entity_type: AuditEntityType,
        entity_id: str
    ) -> ConcurrencyControlResponse:
        """Get or create concurrency control record for an entity."""
        control = self._get_or_create_concurrency_control_model(entity_type, entity_id)
        return ConcurrencyControlResponse.model_validate(control)

    def _get_or_create_concurrency_control_model(
        self,
        entity_type: AuditEntityType,
        entity_id: str
    ) -> ConcurrencyControl:
        """Get or create concurrency control model for internal use."""
        control = (
            self.db.execute(
                select(ConcurrencyControl)
                .where(
                    and_(
                        ConcurrencyControl.entity_type == entity_type.value,
                        ConcurrencyControl.entity_id == entity_id
                    )
                )
            )
            .scalar_one_or_none()
        )

        if not control:
            control = ConcurrencyControl(
                id=str(uuid4()),
                entity_type=entity_type.value,
                entity_id=entity_id,
                version=1
            )
            self.db.add(control)
            self.db.commit()
            self.db.refresh(control)

        return control

    def acquire_lock(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        user_id: str,
        lock_duration_minutes: int = 30
    ) -> Tuple[bool, ConcurrencyControlResponse]:
        """Acquire a lock on an entity for editing."""
        try:
            control = self._get_or_create_concurrency_control_model(entity_type, entity_id)

            # Check if already locked by someone else
            if control.is_locked and control.locked_by != user_id:
                # Check if lock has expired
                if control.lock_expires_at and self._utc_now() > control.lock_expires_at:
                    # Lock expired, can acquire
                    pass
                else:
                    # Lock still active
                    return False, ConcurrencyControlResponse.model_validate(control)

            # Acquire or refresh lock
            lock_expires_at = self._utc_now() + timedelta(minutes=lock_duration_minutes)

            # Use the existing control object instead of fetching again
            control.is_locked = True
            control.locked_by = user_id
            control.locked_at = self._utc_now()
            control.lock_expires_at = lock_expires_at

            self.db.commit()
            self.db.refresh(control)

            return True, ConcurrencyControlResponse.model_validate(control)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to acquire lock: {str(e)}"
            )

    def release_lock(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        user_id: str
    ) -> ConcurrencyControlResponse:
        """Release a lock on an entity."""
        try:
            control = self._get_or_create_concurrency_control_model(entity_type, entity_id)

            if not control.is_locked or control.locked_by != user_id:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="Entity is not locked by this user"
                )

            control.is_locked = False
            control.locked_by = None
            control.locked_at = None
            control.lock_expires_at = None

            self.db.commit()
            self.db.refresh(control)

            return ConcurrencyControlResponse.model_validate(control)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to release lock: {str(e)}"
            )

    def increment_version(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        user_id: str
    ) -> ConcurrencyControlResponse:
        """Increment version number after successful update."""
        try:
            control = self._get_or_create_concurrency_control_model(entity_type, entity_id)

            control.version += 1
            control.last_modified_by = user_id
            control.last_modified_at = self._utc_now()

            self.db.commit()
            self.db.refresh(control)

            return ConcurrencyControlResponse.model_validate(control)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to increment version: {str(e)}"
            )

    # Enhanced Status Analytics

    def get_status_analytics(
        self,
        entity_type: AuditEntityType,
        entity_id: str
    ) -> Dict[str, Any]:
        """Get comprehensive status analytics for an entity."""
        status_history = self.get_status_history(entity_type, entity_id, limit=100)

        if not status_history:
            return {
                "entity_type": entity_type.value,
                "entity_id": entity_id,
                "total_status_changes": 0,
                "current_status": None,
                "status_distribution": {},
                "average_time_per_status": {},
                "total_time_tracked": 0
            }

        # Calculate status distribution and timing
        status_distribution = {}
        status_durations = {}
        total_time = 0

        for history in status_history:
            status = history.new_status
            status_distribution[status] = status_distribution.get(status, 0) + 1

            if history.duration_in_previous_status:
                prev_status = history.old_status or "initial"
                if prev_status not in status_durations:
                    status_durations[prev_status] = []
                status_durations[prev_status].append(history.duration_in_previous_status)
                total_time += history.duration_in_previous_status

        # Calculate averages
        average_time_per_status = {}
        for status, durations in status_durations.items():
            average_time_per_status[status] = sum(durations) / len(durations)

        return {
            "entity_type": entity_type.value,
            "entity_id": entity_id,
            "total_status_changes": len(status_history),
            "current_status": status_history[0].new_status if status_history else None,
            "status_distribution": status_distribution,
            "average_time_per_status": average_time_per_status,
            "total_time_tracked": total_time,
            "first_status_change": status_history[-1].created_at if status_history else None,
            "last_status_change": status_history[0].created_at if status_history else None
        }
