# AI Coding Agent - Infrastructure Configuration

This directory contains all infrastructure configuration files for the AI Coding Agent project, including reverse proxy, SSL certificates, monitoring, and Docker deployment configurations.

## 📁 Directory Structure

```
infrastructure/
├── nginx/                          # Nginx reverse proxy configurations
│   ├── default.conf                # Main reverse proxy configuration
│   ├── ssl.conf                    # HTTPS/SSL configuration
│   └── load-balancer.conf          # Load balancing configuration
├── ssl/                            # SSL certificate management
│   ├── generate-certs.sh           # Certificate generation script
│   ├── cert-config.yaml           # SSL configuration settings
│   └── certs/                      # Generated certificates (created at runtime)
├── monitoring/                     # Monitoring and observability
│   ├── prometheus.yml              # Prometheus configuration
│   ├── grafana-dashboard.json      # Grafana dashboard definition
│   └── docker-compose.monitoring.yml # Monitoring stack deployment
└── docker/                        # Advanced Docker configurations
    ├── production.yml              # Production deployment configuration
    └── scaling.yml                 # Horizontal scaling configuration
```

## 🚀 Quick Start

### 1. Basic Setup (Development)

```bash
# Generate development SSL certificates
cd infrastructure/ssl
ENVIRONMENT=development ./generate-certs.sh

# Start services with basic configuration
docker-compose up -d
```

### 2. Production Setup

```bash
# Generate production SSL certificates
cd infrastructure/ssl
ENVIRONMENT=production DOMAIN=your-domain.com EMAIL=<EMAIL> ./generate-certs.sh

# Deploy with production configuration
docker-compose -f infrastructure/docker/production.yml up -d
```

### 3. Monitoring Setup

```bash
# Start monitoring stack
docker-compose -f infrastructure/monitoring/docker-compose.monitoring.yml up -d

# Access monitoring dashboards
# Grafana: http://localhost:3001 (admin/admin123)
# Prometheus: http://localhost:9090
# AlertManager: http://localhost:9093
```

## 🔧 Configuration Details

### Nginx Reverse Proxy

The nginx configurations provide:

- **default.conf**: Basic reverse proxy with rate limiting and security headers
- **ssl.conf**: HTTPS configuration with modern TLS settings
- **load-balancer.conf**: Advanced load balancing for scaled deployments

Key features:
- Rate limiting (API: 10 req/s, Auth: 5 req/s, Admin: 2 req/s)
- Security headers (HSTS, CSP, X-Frame-Options, etc.)
- WebSocket support for real-time features
- Static asset caching and compression
- Health check endpoints

### SSL Certificate Management

The SSL system supports both development and production scenarios:

**Development:**
- Self-signed certificates for local development
- Automatic generation with proper SAN (Subject Alternative Names)
- Trust configuration for local browsers

**Production:**
- Let's Encrypt integration with automatic renewal
- OCSP stapling for improved performance
- Modern TLS configuration (TLS 1.2/1.3 only)

**Usage:**
```bash
# Development certificates
ENVIRONMENT=development DOMAIN=ai-coding-agent.local ./generate-certs.sh

# Production certificates
ENVIRONMENT=production DOMAIN=yourdomain.com EMAIL=<EMAIL> ./generate-certs.sh
```

### Monitoring Stack

Comprehensive monitoring with:

- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and dashboards
- **AlertManager**: Alert routing and management
- **Node Exporter**: System metrics
- **cAdvisor**: Container metrics
- **Custom exporters**: Application-specific metrics

**Key Metrics Monitored:**
- Request rate and response times
- Error rates and status codes
- CPU, memory, and disk usage
- Container resource utilization
- AI model performance and usage
- Vector database query performance
- Database connection pools

### Docker Configurations

**production.yml:**
- Optimized for production deployment
- Resource limits and health checks
- Security configurations (non-root users)
- Persistent volume management
- Network isolation

**scaling.yml:**
- Horizontal scaling configuration
- Load balancer setup (HAProxy)
- Database clustering (PostgreSQL primary/replica)
- Vector database read replicas
- Redis cluster for session management
- Auto-scaling service

## 🔒 Security Features

### Network Security
- Custom Docker networks with subnet isolation
- Internal service communication only
- Restricted access to sensitive services (vector-db, ollama)

### SSL/TLS Security
- Modern cipher suites only
- HSTS with preload
- OCSP stapling
- Perfect Forward Secrecy

### Application Security
- Rate limiting at multiple levels
- Security headers (CSP, X-Frame-Options, etc.)
- Input validation and sanitization
- Non-root container execution

### Monitoring Security
- Authentication for monitoring dashboards
- Encrypted metrics transmission
- Audit logging for all access

## 📊 Monitoring and Alerting

### Default Alerts
- High error rate (>10% for 5 minutes)
- High response time (>2s 95th percentile)
- Service downtime (>1 minute)
- High resource usage (CPU >80%, Memory >85%, Disk >90%)
- AI service failures (Ollama, Vector DB)

### Custom Dashboards
- System overview with health status
- Request metrics and error rates
- Resource utilization trends
- AI service performance
- Database performance metrics

### Log Aggregation
- Centralized logging with Loki
- Log shipping with Promtail
- Structured logging with metadata
- Log retention and rotation

## 🚀 Deployment Scenarios

### Single Server Deployment
```bash
# Basic production deployment
docker-compose -f infrastructure/docker/production.yml up -d
```

### Scaled Deployment
```bash
# Multi-instance deployment with load balancing
docker-compose -f infrastructure/docker/scaling.yml up -d
```

### Monitoring-Only Deployment
```bash
# Deploy only monitoring stack
docker-compose -f infrastructure/monitoring/docker-compose.monitoring.yml up -d
```

## 🔧 Maintenance

### Certificate Renewal
```bash
# Manual renewal
cd infrastructure/ssl
./generate-certs.sh

# Automatic renewal (production)
# Systemd timer is automatically configured for production deployments
```

### Backup Procedures
```bash
# Backup certificates
tar -czf ssl-backup-$(date +%Y%m%d).tar.gz infrastructure/ssl/certs/

# Backup monitoring data
docker run --rm -v prometheus-data:/data -v $(pwd):/backup alpine tar czf /backup/prometheus-backup-$(date +%Y%m%d).tar.gz /data
```

### Health Checks
```bash
# Check all services
docker-compose ps

# Check specific service health
curl http://localhost:8000/api/v1/health
curl http://localhost:3000/health
curl http://localhost:8001/api/v1/heartbeat
```

## 🐛 Troubleshooting

### Common Issues

**SSL Certificate Issues:**
```bash
# Regenerate certificates
rm -rf infrastructure/ssl/certs/
./infrastructure/ssl/generate-certs.sh
```

**Service Connection Issues:**
```bash
# Check network connectivity
docker network ls
docker network inspect ai-coding-agent-network
```

**Monitoring Issues:**
```bash
# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# Check Grafana datasources
curl -u admin:admin123 http://localhost:3001/api/datasources
```

### Log Locations
- Nginx: `/var/log/nginx/` (in container)
- Application: `backend-logs` volume
- Vector DB: `vector-db-logs` volume
- Monitoring: Container logs via `docker logs`

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Nginx Configuration Guide](https://nginx.org/en/docs/)
- [Prometheus Configuration](https://prometheus.io/docs/prometheus/latest/configuration/)
- [Grafana Dashboard Guide](https://grafana.com/docs/grafana/latest/dashboards/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)

## 🤝 Contributing

When adding new infrastructure components:

1. Follow the existing directory structure
2. Add comprehensive documentation
3. Include health checks and monitoring
4. Test in development environment first
5. Update this README with new features

## 📄 License

This infrastructure configuration is part of the AI Coding Agent project and follows the same license terms.
