/**
 * Agent Collaboration Graph Component
 *
 * Real-time visualization of AI agent collaboration using ReactFlow
 */

import React, { useCallback, useEffect, useState, useMemo } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  ConnectionMode,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import {
  AgentInfo,
  TaskInfo,
  CollaborationEvent,
  AgentRole,
  TaskStatus
} from '../../types/agents';
import { agentService } from '../../services/agentService';
import AgentNodeComponent from './AgentNodeComponent';
import TaskNodeComponent from './TaskNodeComponent';
import CollaborationEdgeComponent from './CollaborationEdgeComponent';

// Define custom node types
const nodeTypes = {
  agent: AgentNodeComponent,
  task: TaskNodeComponent,
};

// Define custom edge types
const edgeTypes = {
  collaboration: CollaborationEdgeComponent,
};

interface AgentCollaborationGraphProps {
  height?: string;
  className?: string;
}

const AgentCollaborationGraph: React.FC<AgentCollaborationGraphProps> = ({
  height = '600px',
  className = ''
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [agents, setAgents] = useState<AgentInfo[]>([]);
  const [tasks, setTasks] = useState<TaskInfo[]>([]);
  const [events, setEvents] = useState<CollaborationEvent[]>([]);
  const [loading, setLoading] = useState(true);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const agentsData = await agentService.getAgents();
        setAgents(agentsData);

        // Start simulation for demo purposes
        agentService.simulateActivity();
      } catch (error) {
        console.error('Failed to load agent data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Subscribe to real-time collaboration events
  useEffect(() => {
    const unsubscribe = agentService.subscribeToCollaborationEvents((newEvents) => {
      setEvents(newEvents);

      // Extract tasks from events
      const tasksFromEvents: TaskInfo[] = newEvents
        .filter(event => event.taskId)
        .map(event => ({
          id: event.taskId!,
          description: event.message || 'Unknown task',
          assignedAgent: event.toAgent || event.fromAgent || AgentRole.DEVELOPER,
          status: TaskStatus.IN_PROGRESS,
          startTime: event.timestamp,
        }))
        .reduce((unique, task) => {
          if (!unique.find(t => t.id === task.id)) {
            unique.push(task);
          }
          return unique;
        }, [] as TaskInfo[]);

      setTasks(tasksFromEvents);
    });

    return unsubscribe;
  }, []);

  // Generate nodes from agents and tasks
  const generatedNodes = useMemo(() => {
    const agentNodes: Node[] = agents.map((agent, index) => ({
      id: `agent-${agent.role}`,
      type: 'agent',
      position: {
        x: (index % 3) * 300 + 50,
        y: Math.floor(index / 3) * 200 + 50
      },
      data: {
        agent,
        isActive: agent.status === 'active' || agent.status === 'busy',
        taskCount: tasks.filter(task => task.assignedAgent === agent.role).length,
      },
    }));

    const taskNodes: Node[] = tasks.slice(0, 10).map((task, index) => ({
      id: `task-${task.id}`,
      type: 'task',
      position: {
        x: 400 + (index % 4) * 200,
        y: 300 + Math.floor(index / 4) * 150
      },
      data: {
        task,
        progress: task.status === TaskStatus.COMPLETED ? 100 :
                 task.status === TaskStatus.IN_PROGRESS ? 50 : 0,
      },
    }));

    return [...agentNodes, ...taskNodes];
  }, [agents, tasks]);

  // Generate edges from collaboration events
  const generatedEdges = useMemo(() => {
    const collaborationEdges: Edge[] = events
      .filter(event => event.fromAgent && event.toAgent && event.fromAgent !== event.toAgent)
      .slice(0, 20) // Limit to prevent performance issues
      .map((event, index) => ({
        id: `edge-${event.id}`,
        source: `agent-${event.fromAgent}`,
        target: `agent-${event.toAgent}`,
        type: 'collaboration',
        animated: Date.now() - event.timestamp.getTime() < 10000, // Animate recent events
        data: {
          event,
          isActive: Date.now() - event.timestamp.getTime() < 5000,
        },
        style: {
          stroke: Date.now() - event.timestamp.getTime() < 5000 ? '#3b82f6' : '#94a3b8',
          strokeWidth: Date.now() - event.timestamp.getTime() < 5000 ? 3 : 1,
        },
      }));

    // Add edges from agents to their current tasks
    const taskEdges: Edge[] = tasks
      .filter(task => task.status === TaskStatus.IN_PROGRESS)
      .map(task => ({
        id: `task-edge-${task.id}`,
        source: `agent-${task.assignedAgent}`,
        target: `task-${task.id}`,
        type: 'default',
        animated: true,
        style: {
          stroke: '#10b981',
          strokeWidth: 2,
          strokeDasharray: '5,5',
        },
      }));

    return [...collaborationEdges, ...taskEdges];
  }, [events, tasks]);

  // Update nodes and edges when data changes
  useEffect(() => {
    setNodes(generatedNodes as any);
  }, [generatedNodes, setNodes]);

  useEffect(() => {
    setEdges(generatedEdges as any);
  }, [generatedEdges, setEdges]);

  const onConnect = useCallback(
    (params: Edge | Connection) => {
      setEdges((eds) => addEdge(params, eds) as any);
    },
    [setEdges]
  );

  if (loading) {
    return (
      <div className={`${className} bg-gray-50 dark:bg-gray-900 rounded-lg border`} style={{ height }}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading agent collaboration graph...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className} bg-gray-50 dark:bg-gray-900 rounded-lg border`} style={{ height }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        connectionMode={ConnectionMode.Loose}
        fitView
        attributionPosition="bottom-left"
        className="bg-gray-50 dark:bg-gray-900"
      >
        <Background color="#e5e7eb" gap={20} />
        <Controls className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700" />
        <MiniMap
          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
          nodeColor={(node) => {
            if (node.type === 'agent') {
              return (node.data as any).isActive ? '#3b82f6' : '#94a3b8';
            }
            if (node.type === 'task') {
              const task = (node.data as any).task;
              return task.status === TaskStatus.COMPLETED ? '#10b981' :
                     task.status === TaskStatus.IN_PROGRESS ? '#f59e0b' : '#ef4444';
            }
            return '#94a3b8';
          }}
        />
      </ReactFlow>
    </div>
  );
};

export default AgentCollaborationGraph;
