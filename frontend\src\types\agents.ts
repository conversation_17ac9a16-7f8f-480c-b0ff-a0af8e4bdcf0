/**
 * Agent Types and Interfaces for Live Collaboration Visualization
 */

export enum AgentRole {
  ARCHITECT = "architect",
  DEVELOPER = "developer",
  SHELL = "shell",
  TESTER = "tester",
  REVIEWER = "reviewer"
}

export enum AgentCapability {
  CODE_GENERATION = "code_generation",
  ARCHITECTURE_DESIGN = "architecture_design",
  TESTING = "testing",
  CODE_REVIEW = "code_review",
  DEBUGGING = "debugging",
  SHELL_OPERATIONS = "shell_operations",
  DEPENDENCY_MANAGEMENT = "dependency_management",
  DOCUMENTATION = "documentation",
  REFACTORING = "refactoring",
  SYSTEM_INTEGRATION = "system_integration"
}

export enum TaskStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  FAILED = "failed",
  VERIFYING = "verifying",
  FIXING = "fixing"
}

export enum CollaborationEventType {
  TASK_STARTED = "task_started",
  TASK_COMPLETED = "task_completed",
  TASK_FAILED = "task_failed",
  VERIFICATION_STARTED = "verification_started",
  VERIFICATION_COMPLETED = "verification_completed",
  AGENT_HANDOFF = "agent_handoff",
  DEPENDENCY_INSTALL = "dependency_install",
  ERROR_DETECTED = "error_detected",
  FIX_APPLIED = "fix_applied"
}

export interface AgentInfo {
  role: AgentRole;
  name: string;
  capabilities: AgentCapability[];
  status: 'idle' | 'active' | 'busy' | 'error';
  lastActivity?: Date;
  currentTask?: string;
  performance?: {
    tasksCompleted: number;
    successRate: number;
    averageTime: number;
  };
}

export interface TaskInfo {
  id: string;
  description: string;
  assignedAgent: AgentRole;
  status: TaskStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  dependencies?: string[];
  result?: any;
  error?: string;
}

export interface CollaborationEvent {
  id: string;
  type: CollaborationEventType;
  fromAgent?: AgentRole;
  toAgent?: AgentRole;
  taskId?: string;
  message: string;
  timestamp: Date;
  metadata?: any;
}

export interface AgentStats {
  role: AgentRole;
  tasksCompleted: number;
  tasksInProgress: number;
  tasksFailed: number;
  averageCompletionTime: number;
  successRate: number;
  collaborationCount: number;
  lastActiveTime: Date;
}

export interface CollaborationMetrics {
  activeAgents: number;
  totalTasks: number;
  tasksInProgress: number;
  completedTasks: number;
  averageTaskTime: number;
  errorRate: number;
  collaborationEvents: number;
  peakPerformanceTime?: string;
  bottleneckAgent?: AgentRole;
}
