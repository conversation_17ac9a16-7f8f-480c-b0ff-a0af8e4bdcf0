import React from 'react';

/**
 * Touch interaction utilities for mobile-first design
 * Provides gestures, haptic feedback, and touch-friendly interactions
 */

export interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

export interface SwipeConfig {
  threshold: number;
  velocity: number;
  directional: boolean;
}

export interface GestureHandlers {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onTap?: (event: TouchEvent) => void;
  onDoubleTap?: (event: TouchEvent) => void;
  onLongPress?: (event: TouchEvent) => void;
  onPinch?: (scale: number) => void;
}

/**
 * Hook for swipe gestures
 */
export const useSwipeGesture = (
  handlers: GestureHandlers,
  config: SwipeConfig = {
    threshold: 50,
    velocity: 0.3,
    directional: true,
  }
) => {
  const touchStartRef = React.useRef<TouchPoint | null>(null);
  const touchEndRef = React.useRef<TouchPoint | null>(null);

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
    };
  }, []);

  const handleTouchEnd = React.useCallback((e: TouchEvent) => {
    if (!touchStartRef.current) return;

    const touch = e.changedTouches[0];
    touchEndRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
    };

    const deltaX = touchEndRef.current.x - touchStartRef.current.x;
    const deltaY = touchEndRef.current.y - touchStartRef.current.y;
    const deltaTime = touchEndRef.current.timestamp - touchStartRef.current.timestamp;

    const velocityX = Math.abs(deltaX) / deltaTime;
    const velocityY = Math.abs(deltaY) / deltaTime;

    // Check if gesture meets threshold and velocity requirements
    if (Math.abs(deltaX) > config.threshold && velocityX > config.velocity) {
      if (deltaX > 0) {
        handlers.onSwipeRight?.();
      } else {
        handlers.onSwipeLeft?.();
      }
    } else if (Math.abs(deltaY) > config.threshold && velocityY > config.velocity) {
      if (deltaY > 0) {
        handlers.onSwipeDown?.();
      } else {
        handlers.onSwipeUp?.();
      }
    }

    touchStartRef.current = null;
    touchEndRef.current = null;
  }, [handlers, config]);

  return {
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
  };
};

/**
 * Hook for tap gestures (single and double tap)
 */
export const useTapGesture = (handlers: GestureHandlers) => {
  const tapTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const tapCountRef = React.useRef(0);
  const lastTapRef = React.useRef<TouchPoint | null>(null);

  const handleTouchEnd = React.useCallback((e: TouchEvent) => {
    const touch = e.changedTouches[0];
    const currentTap: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
    };

    tapCountRef.current++;

    if (tapCountRef.current === 1) {
      // First tap
      lastTapRef.current = currentTap;
      tapTimeoutRef.current = setTimeout(() => {
        if (tapCountRef.current === 1) {
          handlers.onTap?.(e);
        }
        tapCountRef.current = 0;
        lastTapRef.current = null;
      }, 300); // Double tap detection window
    } else if (tapCountRef.current === 2) {
      // Second tap
      if (tapTimeoutRef.current) {
        clearTimeout(tapTimeoutRef.current);
        tapTimeoutRef.current = null;
      }

      // Check if taps are close enough in space and time
      if (lastTapRef.current) {
        const deltaX = Math.abs(currentTap.x - lastTapRef.current.x);
        const deltaY = Math.abs(currentTap.y - lastTapRef.current.y);
        const deltaTime = currentTap.timestamp - lastTapRef.current.timestamp;

        if (deltaX < 50 && deltaY < 50 && deltaTime < 300) {
          handlers.onDoubleTap?.(e);
        }
      }

      tapCountRef.current = 0;
      lastTapRef.current = null;
    }
  }, [handlers]);

  React.useEffect(() => {
    return () => {
      if (tapTimeoutRef.current) {
        clearTimeout(tapTimeoutRef.current);
      }
    };
  }, []);

  return {
    onTouchEnd: handleTouchEnd,
  };
};

/**
 * Hook for long press gesture
 */
export const useLongPress = (
  onLongPress: (event: TouchEvent) => void,
  delay: number = 500
) => {
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const touchStartRef = React.useRef<TouchPoint | null>(null);

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
    };

    timeoutRef.current = setTimeout(() => {
      onLongPress(e);
    }, delay);
  }, [onLongPress, delay]);

  const handleTouchEnd = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    touchStartRef.current = null;
  }, []);

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (!touchStartRef.current) return;

    const touch = e.touches[0];
    const deltaX = Math.abs(touch.clientX - touchStartRef.current.x);
    const deltaY = Math.abs(touch.clientY - touchStartRef.current.y);

    // Cancel long press if user moves finger too much
    if (deltaX > 10 || deltaY > 10) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
  }, []);

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
    onTouchMove: handleTouchMove,
  };
};

/**
 * Hook for pinch/zoom gesture
 */
export const usePinchGesture = (onPinch: (scale: number) => void) => {
  const initialDistanceRef = React.useRef<number | null>(null);
  const currentScaleRef = React.useRef(1);

  const getDistance = (touch1: Touch, touch2: Touch): number => {
    const deltaX = touch1.clientX - touch2.clientX;
    const deltaY = touch1.clientY - touch2.clientY;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  };

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    if (e.touches.length === 2) {
      initialDistanceRef.current = getDistance(e.touches[0], e.touches[1]);
    }
  }, []);

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (e.touches.length === 2 && initialDistanceRef.current) {
      e.preventDefault(); // Prevent default zoom behavior

      const currentDistance = getDistance(e.touches[0], e.touches[1]);
      const scale = currentDistance / initialDistanceRef.current;

      currentScaleRef.current = scale;
      onPinch(scale);
    }
  }, [onPinch]);

  const handleTouchEnd = React.useCallback(() => {
    initialDistanceRef.current = null;
    currentScaleRef.current = 1;
  }, []);

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
  };
};

/**
 * Haptic feedback utilities
 */
export const hapticFeedback = {
  // Light haptic feedback for button presses
  light: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  },

  // Medium haptic feedback for selections
  medium: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(20);
    }
  },

  // Heavy haptic feedback for errors or important actions
  heavy: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([30, 10, 30]);
    }
  },

  // Success pattern
  success: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([10, 10, 10]);
    }
  },

  // Error pattern
  error: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 30, 50, 30, 50]);
    }
  },

  // Check if haptic feedback is supported
  isSupported: (): boolean => {
    return 'vibrate' in navigator;
  },
};

/**
 * Pull-to-refresh functionality
 */
export const usePullToRefresh = (
  onRefresh: () => Promise<void>,
  threshold: number = 80
) => {
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [pullDistance, setPullDistance] = React.useState(0);
  const startTouchRef = React.useRef<number | null>(null);
  const containerRef = React.useRef<HTMLElement>(null);

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    if (containerRef.current?.scrollTop === 0) {
      startTouchRef.current = e.touches[0].clientY;
    }
  }, []);

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (startTouchRef.current !== null && containerRef.current?.scrollTop === 0) {
      const currentY = e.touches[0].clientY;
      const distance = Math.max(0, currentY - startTouchRef.current);

      if (distance > 0) {
        e.preventDefault();
        setPullDistance(Math.min(distance, threshold * 1.5));
      }
    }
  }, [threshold]);

  const handleTouchEnd = React.useCallback(async () => {
    if (pullDistance >= threshold && !isRefreshing) {
      setIsRefreshing(true);
      hapticFeedback.medium();

      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }

    setPullDistance(0);
    startTouchRef.current = null;
  }, [pullDistance, threshold, isRefreshing, onRefresh]);

  React.useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  return {
    containerRef,
    isRefreshing,
    pullDistance,
    pullProgress: Math.min(pullDistance / threshold, 1),
  };
};

/**
 * Touch-friendly component props
 */
export const touchFriendlyProps = {
  // Minimum touch target size (44x44px as per WCAG)
  minTouchTarget: {
    minHeight: '44px',
    minWidth: '44px',
  },

  // Touch feedback classes
  touchFeedback: 'active:scale-95 active:bg-opacity-80 transition-transform duration-75',

  // Prevent text selection on touch
  preventSelection: {
    userSelect: 'none' as const,
    WebkitUserSelect: 'none' as const,
    MozUserSelect: 'none' as const,
    msUserSelect: 'none' as const,
  },

  // Touch callout prevention (iOS)
  preventCallout: {
    WebkitTouchCallout: 'none' as const,
  },
};
