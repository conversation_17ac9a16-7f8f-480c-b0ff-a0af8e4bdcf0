# Windows 10 Docker Setup Guide for AI Coding Agent

## 🪟 **Complete Windows Setup Instructions**

### **Step 1: Install Docker Desktop**

1. **Download Docker Desktop for Windows:**
   - Visit: https://www.docker.com/products/docker-desktop/
   - Click "Download for Windows"
   - Download the installer (Docker Desktop Installer.exe)

2. **Install Docker Desktop:**
   - Run the installer **as Administrator**
   - Accept the license agreement
   - Choose installation options:
     - ✅ Enable WSL 2 integration (recommended)
     - ✅ Add shortcut to desktop
   - Click "Install"

3. **Post-Installation Setup:**
   - **Restart your computer** (this is critical!)
   - Launch Docker Desktop from the Start menu
   - Complete the initial setup wizard
   - Sign in to Docker Hub (optional but recommended)

4. **Verify Installation:**
   ```powershell
   # Open PowerShell as Administrator and run:
   docker --version
   docker-compose --version
   ```

### **Step 2: Configure Docker for Development**

1. **Docker Desktop Settings:**
   - Right-click Docker Desktop system tray icon
   - Select "Settings"
   - Configure these settings:

2. **Resources Tab:**
   - **Memory:** Allocate at least 4GB (8GB recommended)
   - **CPU:** Allocate at least 2 cores (4 recommended)
   - **Disk:** Ensure at least 20GB free space

3. **WSL Integration Tab (if using WSL):**
   - ✅ Enable integration with my default WSL distro
   - ✅ Enable integration with additional distros (if any)

4. **Docker Engine Tab:**
   - Keep default settings unless you have specific requirements

### **Step 3: Verify Your Environment**

Open **PowerShell as Administrator** and navigate to your project directory:

```powershell
# Navigate to your project
cd "F:\NasShare\AiCodingaagent"

# Run pre-build verification
.\scripts\Verify-PreBuild.ps1

# If all checks pass, build and verify containers
.\scripts\Build-And-Verify.ps1
```

### **Step 4: Troubleshooting Common Windows Issues**

#### **Issue: "Docker is not running"**
```powershell
# Check if Docker Desktop is running
Get-Process "Docker Desktop" -ErrorAction SilentlyContinue

# If not running, start Docker Desktop
Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"

# Wait for Docker to start (usually 30-60 seconds)
Start-Sleep -Seconds 60

# Verify Docker is ready
docker version
```

#### **Issue: "Access Denied" or Permission Errors**
```powershell
# Run PowerShell as Administrator
# Right-click PowerShell icon → "Run as Administrator"

# Check if your user is in the docker-users group
net localgroup docker-users

# If not, add your user (replace USERNAME with your actual username)
net localgroup docker-users USERNAME /add

# Restart Docker Desktop after adding user
```

#### **Issue: "WSL 2 installation is incomplete"**
1. Open PowerShell as Administrator
2. Run: `wsl --install`
3. Restart your computer
4. Open PowerShell again and run: `wsl --set-default-version 2`
5. Restart Docker Desktop

#### **Issue: "Hyper-V is not enabled"**
1. Open PowerShell as Administrator
2. Run: `Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All`
3. Restart your computer
4. Start Docker Desktop

### **Step 5: PowerShell Script Usage**

#### **Pre-Build Verification:**
```powershell
# Basic verification
.\scripts\Verify-PreBuild.ps1

# Detailed verification with extra information
.\scripts\Verify-PreBuild.ps1 -Detailed
```

#### **Build and Verify Containers:**
```powershell
# Full build and verification
.\scripts\Build-And-Verify.ps1

# Quick build (skip some checks)
.\scripts\Build-And-Verify.ps1 -Quick

# Skip pre-build verification
.\scripts\Build-And-Verify.ps1 -SkipPreCheck
```

#### **Container Verification:**
```powershell
# Full container verification
.\scripts\Verify-Containers.ps1

# Quick verification (less detailed output)
.\scripts\Verify-Containers.ps1 -Quick
```

### **Step 6: Environment File Setup**

Create a `.env` file in your project root with these variables:

```env
# Security Keys (must be at least 32 characters)
SECRET_KEY=your-secret-key-here-minimum-32-characters-long-for-security
CONFIG_ENCRYPTION_KEY=your-encryption-key-minimum-32-characters-long-for-security

# Database Configuration
DB_PASSWORD=your-secure-database-password-here

# Optional: Supabase Configuration (if using cloud database)
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Optional: OpenAI Configuration (if using OpenAI models)
OPENAI_API_KEY=your-openai-api-key-here
```

### **Step 7: Success Verification**

After running the scripts, you should see:

✅ **All containers running:**
```powershell
docker-compose ps
# Should show all services as "Up"
```

✅ **Services accessible:**
- Backend API: http://localhost:8000
- Frontend App: http://localhost:3000
- PostgreSQL: localhost:5432
- Redis: localhost:6379
- Ollama: http://localhost:11434

✅ **Health checks passing:**
```powershell
# Backend health
curl http://localhost:8000/api/v1/health

# Frontend accessibility
curl http://localhost:3000
```

### **Step 8: Daily Development Workflow**

```powershell
# Start your development session
docker-compose up -d

# Check status
docker-compose ps

# View logs if needed
docker-compose logs backend
docker-compose logs frontend

# Stop when done
docker-compose down
```

## 🆘 **Emergency Recovery**

If everything breaks:

```powershell
# Nuclear option - clean everything
docker-compose down --volumes --remove-orphans
docker system prune -af --volumes
docker volume prune -f

# Rebuild from scratch
.\scripts\Build-And-Verify.ps1
```

## 📞 **Getting Help**

If you encounter issues:

1. **Check Docker Desktop logs:**
   - Docker Desktop → Troubleshoot → View logs

2. **Check container logs:**
   ```powershell
   docker-compose logs backend
   docker-compose logs frontend
   ```

3. **Verify system requirements:**
   - Windows 10 version 2004 or higher
   - WSL 2 enabled
   - Hyper-V enabled
   - At least 4GB RAM available for Docker
