/**
 * Accessibility Settings Component
 * Handles accessibility preferences for better user experience
 */

import React from 'react';
import { User, <PERSON>, <PERSON>arO<PERSON>, MousePointer, Type, Zap } from 'lucide-react';
import { usePreferences } from '../../contexts/PreferencesContext';

const fontSizeOptions = [
  { value: 'small', label: 'Small (14px)', description: 'Compact text size' },
  { value: 'medium', label: 'Medium (16px)', description: 'Default text size' },
  { value: 'large', label: 'Large (18px)', description: 'Larger text for better readability' },
  { value: 'extra-large', label: 'Extra Large (20px)', description: 'Maximum text size' },
];

const accessibilityFeatures = [
  {
    key: 'reduceMotion' as const,
    icon: Zap,
    title: 'Reduce Motion',
    description: 'Minimize animations and transitions for better focus',
  },
  {
    key: 'highContrast' as const,
    icon: Eye,
    title: 'High Contrast',
    description: 'Increase contrast between text and background',
  },
  {
    key: 'screenReader' as const,
    icon: EarOff,
    title: 'Screen Reader Support',
    description: 'Enhanced support for screen reading software',
  },
  {
    key: 'keyboardNavigation' as const,
    icon: MousePointer,
    title: 'Keyboard Navigation',
    description: 'Enhanced keyboard navigation throughout the app',
  },
];

export const AccessibilitySettings: React.FC = () => {
  const { preferences, updateSection } = usePreferences();
  const { accessibility } = preferences;

  const handleToggle = (key: keyof typeof accessibility) => {
    if (key === 'fontSize') return; // fontSize is handled separately

    updateSection('accessibility', {
      [key]: !accessibility[key],
    });
  };

  const handleFontSizeChange = (fontSize: string) => {
    updateSection('accessibility', { fontSize: fontSize as any });
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          <User className="h-5 w-5" />
          Accessibility Settings
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Customize the interface to meet your accessibility needs
        </p>
      </div>

      {/* Font Size Settings */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
          <Type className="h-4 w-4" />
          Font Size
        </h4>
        <div className="space-y-3">
          {fontSizeOptions.map((option) => (
            <label key={option.value} className="flex items-start">
              <input
                type="radio"
                name="fontSize"
                value={option.value}
                checked={accessibility.fontSize === option.value}
                onChange={(e) => handleFontSizeChange(e.target.value)}
                className="mt-1 mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <div>
                <div className="text-gray-900 dark:text-white font-medium">
                  {option.label}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {option.description}
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Accessibility Features */}
      <div className="space-y-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white">
          Accessibility Features
        </h4>

        {accessibilityFeatures.map((feature) => {
          const Icon = feature.icon;
          const isEnabled = accessibility[feature.key];

          return (
            <div
              key={feature.key}
              className="flex items-start justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div className="flex items-start gap-3">
                <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400 mt-0.5" />
                <div>
                  <h5 className="text-md font-medium text-gray-900 dark:text-white">
                    {feature.title}
                  </h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {feature.description}
                  </p>
                </div>
              </div>

              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isEnabled}
                  onChange={() => handleToggle(feature.key)}
                  className="sr-only peer"
                />
                <div className={`
                  relative w-11 h-6 rounded-full peer transition-colors duration-200 ease-in-out
                  ${isEnabled
                    ? 'bg-indigo-600'
                    : 'bg-gray-200 dark:bg-gray-700'
                  }
                  peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800
                `}>
                  <div className={`
                    absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 transition-transform duration-200 ease-in-out
                    ${isEnabled ? 'translate-x-full border-white' : 'translate-x-0'}
                  `} />
                </div>
              </label>
            </div>
          );
        })}
      </div>

      {/* Additional Accessibility Options */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Additional Options
        </h4>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Focus Indicator Style
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
              <option value="default">Default</option>
              <option value="high-contrast">High Contrast</option>
              <option value="thick">Thick Border</option>
              <option value="glow">Glow Effect</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Link Indication
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
              <option value="color-only">Color Only</option>
              <option value="underline">Always Underlined</option>
              <option value="both">Color and Underline</option>
            </select>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <span className="text-gray-900 dark:text-white">
                Show tooltips on hover
              </span>
            </label>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-3 text-indigo-600 focus:ring-indigo-500"
              />
              <span className="text-gray-900 dark:text-white">
                Announce page changes to screen readers
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};
