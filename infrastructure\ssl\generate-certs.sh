#!/bin/bash
# AI Coding Agent - SSL Certificate Generation Script
# Generates self-signed certificates for development and sets up Let's Encrypt for production

set -euo pipefail

# Configuration
DOMAIN="${DOMAIN:-ai-coding-agent.local}"
EMAIL="${EMAIL:-<EMAIL>}"
SSL_DIR="${SSL_DIR:-/etc/nginx/ssl}"
CERT_DIR="${CERT_DIR:-./infrastructure/ssl/certs}"
ENVIRONMENT="${ENVIRONMENT:-development}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create certificate directory
create_cert_directory() {
    log_info "Creating certificate directory: $CERT_DIR"
    mkdir -p "$CERT_DIR"
    chmod 700 "$CERT_DIR"
}

# Generate self-signed certificate for development
generate_self_signed() {
    log_info "Generating self-signed certificate for development..."
    
    # Create OpenSSL configuration
    cat > "$CERT_DIR/openssl.conf" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=US
ST=Development
L=Local
O=AI Coding Agent
OU=Development
CN=$DOMAIN

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = localhost
DNS.3 = *.ai-coding-agent.local
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

    # Generate private key
    openssl genrsa -out "$CERT_DIR/privkey.pem" 2048
    chmod 600 "$CERT_DIR/privkey.pem"
    
    # Generate certificate signing request
    openssl req -new -key "$CERT_DIR/privkey.pem" -out "$CERT_DIR/cert.csr" -config "$CERT_DIR/openssl.conf"
    
    # Generate self-signed certificate
    openssl x509 -req -in "$CERT_DIR/cert.csr" -signkey "$CERT_DIR/privkey.pem" -out "$CERT_DIR/fullchain.pem" -days 365 -extensions v3_req -extfile "$CERT_DIR/openssl.conf"
    
    # Create chain file (same as fullchain for self-signed)
    cp "$CERT_DIR/fullchain.pem" "$CERT_DIR/chain.pem"
    
    # Set proper permissions
    chmod 644 "$CERT_DIR/fullchain.pem" "$CERT_DIR/chain.pem"
    
    log_success "Self-signed certificate generated successfully!"
    log_info "Certificate: $CERT_DIR/fullchain.pem"
    log_info "Private Key: $CERT_DIR/privkey.pem"
    log_info "Chain: $CERT_DIR/chain.pem"
}

# Setup Let's Encrypt for production
setup_letsencrypt() {
    log_info "Setting up Let's Encrypt for production..."
    
    # Check if certbot is installed
    if ! command -v certbot &> /dev/null; then
        log_error "Certbot is not installed. Please install certbot first."
        log_info "Ubuntu/Debian: sudo apt-get install certbot python3-certbot-nginx"
        log_info "CentOS/RHEL: sudo yum install certbot python3-certbot-nginx"
        exit 1
    fi
    
    # Create webroot directory for challenges
    mkdir -p /var/www/certbot
    
    # Generate certificate using webroot method
    log_info "Requesting certificate from Let's Encrypt..."
    certbot certonly \
        --webroot \
        --webroot-path=/var/www/certbot \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        --domains "$DOMAIN" \
        --non-interactive
    
    # Copy certificates to our directory
    if [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
        cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$CERT_DIR/"
        cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$CERT_DIR/"
        cp "/etc/letsencrypt/live/$DOMAIN/chain.pem" "$CERT_DIR/"
        
        log_success "Let's Encrypt certificate obtained successfully!"
    else
        log_error "Failed to obtain Let's Encrypt certificate"
        exit 1
    fi
}

# Setup certificate renewal
setup_renewal() {
    log_info "Setting up automatic certificate renewal..."
    
    # Create renewal script
    cat > "$CERT_DIR/renew-certs.sh" << 'EOF'
#!/bin/bash
# Automatic certificate renewal script

set -euo pipefail

CERT_DIR="./infrastructure/ssl/certs"
DOMAIN="${DOMAIN:-ai-coding-agent.local}"

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
}

# Renew certificates
log_info "Starting certificate renewal process..."

if certbot renew --quiet; then
    log_info "Certificate renewal successful"
    
    # Copy renewed certificates
    if [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
        cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$CERT_DIR/"
        cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$CERT_DIR/"
        cp "/etc/letsencrypt/live/$DOMAIN/chain.pem" "$CERT_DIR/"
        
        # Reload nginx
        if command -v docker &> /dev/null; then
            docker-compose exec nginx nginx -s reload
        elif command -v systemctl &> /dev/null; then
            systemctl reload nginx
        fi
        
        log_info "Certificates updated and nginx reloaded"
    fi
else
    log_error "Certificate renewal failed"
    exit 1
fi
EOF

    chmod +x "$CERT_DIR/renew-certs.sh"
    
    # Create systemd timer for automatic renewal (if systemd is available)
    if command -v systemctl &> /dev/null; then
        log_info "Creating systemd timer for automatic renewal..."
        
        # Create service file
        sudo tee /etc/systemd/system/ai-coding-agent-cert-renewal.service > /dev/null << EOF
[Unit]
Description=AI Coding Agent Certificate Renewal
After=network.target

[Service]
Type=oneshot
ExecStart=$PWD/$CERT_DIR/renew-certs.sh
User=root
EOF

        # Create timer file
        sudo tee /etc/systemd/system/ai-coding-agent-cert-renewal.timer > /dev/null << EOF
[Unit]
Description=Run AI Coding Agent Certificate Renewal twice daily
Requires=ai-coding-agent-cert-renewal.service

[Timer]
OnCalendar=*-*-* 00,12:00:00
RandomizedDelaySec=3600
Persistent=true

[Install]
WantedBy=timers.target
EOF

        # Enable and start timer
        sudo systemctl daemon-reload
        sudo systemctl enable ai-coding-agent-cert-renewal.timer
        sudo systemctl start ai-coding-agent-cert-renewal.timer
        
        log_success "Automatic renewal timer created and started"
    else
        log_warning "Systemd not available. Please set up a cron job to run $CERT_DIR/renew-certs.sh twice daily"
    fi
}

# Validate certificates
validate_certificates() {
    log_info "Validating certificates..."
    
    if [ ! -f "$CERT_DIR/fullchain.pem" ] || [ ! -f "$CERT_DIR/privkey.pem" ]; then
        log_error "Certificate files not found!"
        exit 1
    fi
    
    # Check certificate validity
    if openssl x509 -in "$CERT_DIR/fullchain.pem" -text -noout > /dev/null 2>&1; then
        log_success "Certificate is valid"
        
        # Show certificate details
        log_info "Certificate details:"
        openssl x509 -in "$CERT_DIR/fullchain.pem" -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:|DNS:|IP Address:)"
    else
        log_error "Certificate is invalid!"
        exit 1
    fi
}

# Main function
main() {
    log_info "AI Coding Agent SSL Certificate Setup"
    log_info "Environment: $ENVIRONMENT"
    log_info "Domain: $DOMAIN"
    log_info "Email: $EMAIL"
    
    create_cert_directory
    
    case "$ENVIRONMENT" in
        "development"|"dev")
            generate_self_signed
            ;;
        "production"|"prod")
            setup_letsencrypt
            setup_renewal
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT"
            log_info "Supported environments: development, production"
            exit 1
            ;;
    esac
    
    validate_certificates
    
    log_success "SSL certificate setup completed successfully!"
    log_info "Certificates are available in: $CERT_DIR"
    log_info "Configure your nginx to use these certificates."
}

# Run main function
main "$@"
