{
  "name": "AI Coding Agent Development",
  "dockerComposeFile": [
    "../docker-compose.yml",
    "../docker-compose.dev.yml"
  ],
  "service": "backend",
  "workspaceFolder": "/app",
  "shutdownAction": "stopCompose",

  // Features to install in the container
  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers/features/node:1": {
      "version": "18"
    }
  },

  // VS Code settings
  "customizations": {
    "vscode": {
      "settings": {
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": true,
        "python.formatting.provider": "black",
        "python.linting.flake8Enabled": true,
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.organizeImports": true
        },
        "files.exclude": {
          "**/__pycache__": true,
          "**/*.pyc": true
        }
      },

      // Extensions to install
      "extensions": [
        "ms-python.python",
        "ms-python.black-formatter",
        "ms-python.flake8",
        "ms-python.pylint",
        "ms-toolsai.jupyter",
        "ms-vscode.vscode-json",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "ms-vscode.vscode-typescript-next",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        "ms-azuretools.vscode-docker",
        "ms-vscode-remote.remote-containers"
      ]
    }
  },

  // Forward ports
  "forwardPorts": [8000, 3000],
  "portsAttributes": {
    "8000": {
      "label": "Backend API",
      "onAutoForward": "notify"
    },
    "3000": {
      "label": "Frontend App",
      "onAutoForward": "openBrowser"
    }
  },

  // Post-create commands
  "postCreateCommand": "pip install -r requirements-dev.txt",

  // Mount the Docker socket for Docker-in-Docker
  "mounts": [
    "source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
  ],

  // Run as non-root user
  "remoteUser": "root"
}
