import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { cn } from '../../utils/cn';

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
}

interface DropdownProps {
  options: Option[];
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  className?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  name?: string;
  id?: string;
  required?: boolean;
}

export const Dropdown: React.FC<DropdownProps> = ({
  options,
  value,
  defaultValue,
  placeholder = 'Select an option...',
  disabled = false,
  error,
  className,
  onChange,
  onBlur,
  name,
  id,
  required = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value || defaultValue || '');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const selectedOption = options.find(option => option.value === selectedValue);

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        onBlur?.();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onBlur]);

  const handleSelect = (option: Option) => {
    if (option.disabled) return;

    setSelectedValue(option.value);
    setIsOpen(false);
    onChange?.(option.value);
    buttonRef.current?.focus();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (disabled) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        setIsOpen(!isOpen);
        break;
      case 'Escape':
        setIsOpen(false);
        buttonRef.current?.focus();
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          const currentIndex = options.findIndex(opt => opt.value === selectedValue);
          const nextIndex = Math.min(currentIndex + 1, options.length - 1);
          if (nextIndex !== currentIndex) {
            handleSelect(options[nextIndex]);
          }
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          const currentIndex = options.findIndex(opt => opt.value === selectedValue);
          const prevIndex = Math.max(currentIndex - 1, 0);
          if (prevIndex !== currentIndex) {
            handleSelect(options[prevIndex]);
          }
        }
        break;
    }
  };

  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      <button
        ref={buttonRef}
        type="button"
        className={cn(
          'relative w-full rounded-lg border bg-white dark:bg-gray-800 px-3 py-2 text-left shadow-sm transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          disabled
            ? 'cursor-not-allowed bg-gray-50 dark:bg-gray-700 text-gray-400'
            : 'cursor-pointer hover:border-gray-400 dark:hover:border-gray-500',
          error
            ? 'border-red-300 dark:border-red-600'
            : 'border-gray-300 dark:border-gray-600',
          isOpen && 'ring-2 ring-blue-500 border-blue-500'
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        name={name}
        id={id}
        data-required={required}
      >
        <span className={cn(
          'block truncate',
          !selectedOption && 'text-gray-500 dark:text-gray-400'
        )}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>

        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <ChevronDown
            className={cn(
              'h-4 w-4 text-gray-400 transition-transform duration-200',
              isOpen && 'rotate-180'
            )}
            aria-hidden="true"
          />
        </span>
      </button>

      {isOpen && (
        <div className={cn(
          'absolute z-50 mt-1 w-full rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-600',
          'max-h-60 overflow-auto focus:outline-none'
        )}>
          <ul role="listbox" className="py-1">
            {options.map((option) => (
              <li
                key={option.value}
                role="option"
                aria-selected={option.value === selectedValue}
                className={cn(
                  'relative cursor-pointer select-none px-3 py-2 text-sm transition-colors',
                  option.disabled
                    ? 'cursor-not-allowed text-gray-400 dark:text-gray-500'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700',
                  option.value === selectedValue && 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                )}
                onClick={() => handleSelect(option)}
              >
                <span className={cn(
                  'block truncate',
                  option.value === selectedValue ? 'font-medium' : 'font-normal'
                )}>
                  {option.label}
                </span>

                {option.value === selectedValue && (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" aria-hidden="true" />
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

// Multi-select dropdown variant
interface MultiSelectProps extends Omit<DropdownProps, 'value' | 'onChange'> {
  values?: string[];
  defaultValues?: string[];
  onChange?: (values: string[]) => void;
  maxSelections?: number;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  values,
  defaultValues,
  placeholder = 'Select options...',
  disabled = false,
  error,
  className,
  onChange,
  onBlur,
  name,
  id,
  required = false,
  maxSelections
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValues, setSelectedValues] = useState<string[]>(values || defaultValues || []);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (values !== undefined) {
      setSelectedValues(values);
    }
  }, [values]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        onBlur?.();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onBlur]);

  const handleToggleOption = (option: Option) => {
    if (option.disabled) return;

    const newValues = selectedValues.includes(option.value)
      ? selectedValues.filter(v => v !== option.value)
      : maxSelections && selectedValues.length >= maxSelections
        ? selectedValues
        : [...selectedValues, option.value];

    setSelectedValues(newValues);
    onChange?.(newValues);
  };

  const selectedOptions = options.filter(option => selectedValues.includes(option.value));
  const displayText = selectedOptions.length === 0
    ? placeholder
    : selectedOptions.length === 1
      ? selectedOptions[0].label
      : `${selectedOptions.length} selected`;

  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      <button
        ref={buttonRef}
        type="button"
        className={cn(
          'relative w-full rounded-lg border bg-white dark:bg-gray-800 px-3 py-2 text-left shadow-sm transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          disabled
            ? 'cursor-not-allowed bg-gray-50 dark:bg-gray-700 text-gray-400'
            : 'cursor-pointer hover:border-gray-400 dark:hover:border-gray-500',
          error
            ? 'border-red-300 dark:border-red-600'
            : 'border-gray-300 dark:border-gray-600',
          isOpen && 'ring-2 ring-blue-500 border-blue-500'
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        name={name}
        id={id}
        data-required={required}
      >
        <span className={cn(
          'block truncate',
          selectedOptions.length === 0 && 'text-gray-500 dark:text-gray-400'
        )}>
          {displayText}
        </span>

        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <ChevronDown
            className={cn(
              'h-4 w-4 text-gray-400 transition-transform duration-200',
              isOpen && 'rotate-180'
            )}
            aria-hidden="true"
          />
        </span>
      </button>

      {isOpen && (
        <div className={cn(
          'absolute z-50 mt-1 w-full rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-600',
          'max-h-60 overflow-auto focus:outline-none'
        )}>
          <ul role="listbox" className="py-1">
            {options.map((option) => (
              <li
                key={option.value}
                role="option"
                aria-selected={selectedValues.includes(option.value)}
                className={cn(
                  'relative cursor-pointer select-none px-3 py-2 text-sm transition-colors',
                  option.disabled
                    ? 'cursor-not-allowed text-gray-400 dark:text-gray-500'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                )}
                onClick={() => handleToggleOption(option)}
              >
                <span className={cn(
                  'block truncate',
                  selectedValues.includes(option.value) ? 'font-medium' : 'font-normal'
                )}>
                  {option.label}
                </span>

                {selectedValues.includes(option.value) && (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" aria-hidden="true" />
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};
