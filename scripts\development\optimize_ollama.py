#!/usr/bin/env python3
"""
Ollama Performance Optimization and System Resource Check

Investigates performance issues and applies optimizations.
"""

import asyncio
import aiohttp
import json
import psutil
import subprocess
import time
from typing import Dict, Any

async def check_system_resources():
    """Check system resources that might affect Ollama performance."""
    print("🖥️  System Resource Check:")

    # CPU usage
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"  CPU Usage: {cpu_percent}%")

    # Memory usage
    memory = psutil.virtual_memory()
    print(f"  Memory Usage: {memory.percent}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)")
    print(f"  Available Memory: {memory.available // (1024**3):.1f}GB")

    # Disk usage
    disk = psutil.disk_usage('/')
    print(f"  Disk Usage: {disk.percent}% ({disk.used // (1024**3):.1f}GB / {disk.total // (1024**3):.1f}GB)")

    # Check if system is under stress
    warnings = []
    if cpu_percent > 80:
        warnings.append(f"High CPU usage ({cpu_percent}%)")
    if memory.percent > 85:
        warnings.append(f"High memory usage ({memory.percent}%)")
    if memory.available < 2 * (1024**3):  # Less than 2GB available
        warnings.append(f"Low available memory ({memory.available // (1024**3):.1f}GB)")

    if warnings:
        print(f"  ⚠️  Resource warnings: {', '.join(warnings)}")
    else:
        print(f"  ✅ System resources look good")

    return {
        "cpu_percent": cpu_percent,
        "memory_percent": memory.percent,
        "memory_available_gb": memory.available // (1024**3),
        "warnings": warnings
    }

async def check_ollama_process():
    """Check Ollama process status and resource usage."""
    print("\n🐋 Ollama Process Check:")

    try:
        # Find Ollama process
        ollama_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'memory_info']):
            if 'ollama' in proc.info['name'].lower():
                ollama_processes.append(proc.info)

        if not ollama_processes:
            print("  ❌ No Ollama processes found")
            return None

        for proc in ollama_processes:
            print(f"  Process: {proc['name']} (PID: {proc['pid']})")
            print(f"    CPU: {proc['cpu_percent']}%")
            print(f"    Memory: {proc['memory_percent']:.1f}% ({proc['memory_info'].rss // (1024**2):.0f}MB)")

        return ollama_processes

    except Exception as e:
        print(f"  ❌ Error checking Ollama processes: {e}")
        return None

async def optimize_model_settings(model_name: str) -> Dict[str, Any] | None:
    """Test different optimization settings for a model."""
    print(f"\n⚡ Optimizing {model_name}...")

    # Base settings
    base_options = {
        "num_predict": 10,
        "temperature": 0.1
    }

    # Optimization settings to test
    optimizations = [
        {
            "name": "Default",
            "options": base_options
        },
        {
            "name": "Fast Inference",
            "options": {
                **base_options,
                "num_ctx": 1024,  # Smaller context window
                "num_batch": 8,   # Smaller batch size
                "num_thread": 4   # Limit threads
            }
        },
        {
            "name": "Minimal Context",
            "options": {
                **base_options,
                "num_ctx": 512,   # Very small context
                "num_batch": 4,   # Very small batch
                "top_k": 20,      # Reduce sampling
                "top_p": 0.8
            }
        }
    ]

    results = []

    for opt in optimizations:
        try:
            print(f"  Testing {opt['name']} settings...")

            start_time = time.time()

            test_payload = {
                "model": model_name,
                "prompt": "Hello",
                "stream": False,
                "options": opt["options"]
            }

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.post("http://localhost:11434/api/generate", json=test_payload) as response:
                    response_time = (time.time() - start_time) * 1000

                    if response.status == 200:
                        data = await response.json()
                        results.append({
                            "name": opt["name"],
                            "response_time_ms": round(response_time, 2),
                            "success": True,
                            "options": opt["options"]
                        })
                        print(f"    ✅ {opt['name']}: {response_time:.0f}ms")
                    else:
                        results.append({
                            "name": opt["name"],
                            "response_time_ms": None,
                            "success": False,
                            "error": f"HTTP {response.status}"
                        })
                        print(f"    ❌ {opt['name']}: HTTP {response.status}")

        except asyncio.TimeoutError:
            results.append({
                "name": opt["name"],
                "response_time_ms": None,
                "success": False,
                "error": "Timeout (15s)"
            })
            print(f"    ❌ {opt['name']}: Timeout")
        except Exception as e:
            results.append({
                "name": opt["name"],
                "response_time_ms": None,
                "success": False,
                "error": str(e)
            })
            print(f"    ❌ {opt['name']}: {str(e)}")

    # Find best performing setting
    successful_results = [r for r in results if r["success"]]
    if successful_results:
        best = min(successful_results, key=lambda x: x["response_time_ms"])
        print(f"  🏆 Best setting: {best['name']} ({best['response_time_ms']}ms)")
        return best
    else:
        print(f"  ❌ No successful optimizations found")
        return None

async def main():
    """Main performance analysis and optimization."""
    print("🔧 Ollama Performance Optimization")
    print("="*50)

    # Check system resources
    system_resources = await check_system_resources()

    # Check Ollama process
    ollama_processes = await check_ollama_process()

    # Test optimization on the slowest models
    slow_models = [
        "mistral:7b-instruct-q4_0",
        "deepseek-coder:6.7b-instruct-q4_0"
    ]

    optimization_results = {}
    for model in slow_models:
        result = await optimize_model_settings(model)
        if result:
            optimization_results[model] = result

    # Summary and recommendations
    print("\n" + "="*50)
    print("📋 OPTIMIZATION SUMMARY")
    print("="*50)

    if system_resources["warnings"]:
        print("\n⚠️  System Resource Issues:")
        for warning in system_resources["warnings"]:
            print(f"  - {warning}")
        print("\n💡 Recommendations:")
        if system_resources["memory_percent"] > 85:
            print("  - Free up memory by closing unnecessary applications")
        if system_resources["cpu_percent"] > 80:
            print("  - Reduce CPU load or consider using fewer models simultaneously")

    if optimization_results:
        print("\n⚡ Performance Optimizations Found:")
        for model, result in optimization_results.items():
            print(f"  - {model}: Use '{result['name']}' settings for {result['response_time_ms']}ms response")

    print("\n🎯 Next Steps:")
    print("  1. Apply optimized settings to the AI provider")
    print("  2. Consider model loading strategies (lazy loading)")
    print("  3. Implement response caching for common queries")
    print("  4. Monitor system resources during normal usage")

if __name__ == "__main__":
    asyncio.run(main())
