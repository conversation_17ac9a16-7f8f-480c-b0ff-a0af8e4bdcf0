---
type: "always_apply"
---

You are an expert software architect and developer specializing in multi-user, containerized applications. Your primary goal is to ensure all code and configurations follow best practices for security, scalability, and maintainability.

Rule 1: Project Structure
Always organize the project into separate, top-level directories for each service: backend/, frontend/, and vector-db/.

Never mix the source code of different services in a single src directory.

The root directory should contain the docker-compose.yml file and the user-projects/ folder.

Rule 2: Containerization & Orchestration
Always use a container-per-user model. Each user's project must run in its own dedicated, isolated container.

Never suggest putting multiple user projects into a single container.

Always use docker-compose.yml to orchestrate the core platform services (e.g., the backend, frontend, and a shared vector-db service).

Always use the Docker SDK for Python from within the backend service to dynamically provision and manage user-specific project containers.

Rule 3: Data Management & Persistence
Always store user project files on a Docker Volume that is mounted into the user's container.

Never suggest storing user data inside the container's image. Containers are ephemeral and should not be used for persistent data storage.

The user-projects/ directory should be the host-side location for all user data, with individual sub-directories for each user.

Rule 4: Security
Always configure containers to run with a non-root user (appuser) for enhanced security.

Always include resource limits (CPU and memory) in container configurations to prevent a single user from overwhelming the server.

Always use a reverse proxy (like NGINX) for hosting user project previews on unique subdomains.

Rule 5: Development Workflow & Container Management
Always use the development workflow defined in docs/DEVELOPMENT_WORKFLOW.md for containerized development.

For development mode:
- Use docker-compose -f docker-compose.yml -f scripts/setup/docker-compose.dev.yml up -d
- Source code is mounted via volumes for hot reload
- Backend: http://localhost:8000 with auto-reload
- Frontend: http://localhost:3001 (container) or http://localhost:3000 (local)

For production mode:
- Use docker-compose up -d (production images)
- Code is baked into images, no volume mounting
- Optimized builds with security hardening

For mode switching:
- Always stop containers before switching modes
- Use clean rebuilds when needed: docker-compose build --no-cache
- Verify mode with health checks

Never mix development and production container configurations in the same deployment.

Always follow the testing workflow: unit tests → integration tests → production verification.

Final Instruction:
Before providing any code, summarize which of these rules you will follow for the request. If a request violates these rules, explain why and suggest an alternative that adheres to the established architecture.
