/**
 * Preferences Context - Global State Management for User Preferences
 * Provides centralized preferences management with localStorage persistence
 */

import React, { createContext, useContext, useEffect, useReducer, ReactNode } from 'react';
import { UserPreferences, defaultPreferences } from '../types/preferences';
import { validateForm } from '../utils/validation';
import { preferencesValidation } from '../types/preferences';

// Action types
type PreferencesAction =
  | { type: 'SET_PREFERENCES'; payload: UserPreferences }
  | { type: 'UPDATE_SECTION'; payload: { section: keyof UserPreferences; data: any } }
  | { type: 'RESET_PREFERENCES' }
  | { type: 'IMPORT_PREFERENCES'; payload: UserPreferences }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// State interface
interface PreferencesState {
  preferences: UserPreferences;
  isLoading: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
}

// Context interface
interface PreferencesContextType extends PreferencesState {
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  updateSection: <K extends keyof UserPreferences>(
    section: K,
    data: Partial<UserPreferences[K]>
  ) => void;
  resetPreferences: () => void;
  savePreferences: () => Promise<void>;
  exportPreferences: () => string;
  importPreferences: (data: string) => Promise<boolean>;
  validatePreferences: (preferences: UserPreferences) => boolean;
}

// Initial state
const initialState: PreferencesState = {
  preferences: defaultPreferences as UserPreferences,
  isLoading: false,
  error: null,
  hasUnsavedChanges: false,
};

// Reducer
const preferencesReducer = (
  state: PreferencesState,
  action: PreferencesAction
): PreferencesState => {
  switch (action.type) {
    case 'SET_PREFERENCES':
      return {
        ...state,
        preferences: action.payload,
        hasUnsavedChanges: false,
        error: null,
      };

    case 'UPDATE_SECTION':
      return {
        ...state,
        preferences: {
          ...state.preferences,
          [action.payload.section]: {
            ...(state.preferences[action.payload.section] as Record<string, any>),
            ...action.payload.data,
          },
        },
        hasUnsavedChanges: true,
        error: null,
      };

    case 'RESET_PREFERENCES':
      return {
        ...state,
        preferences: defaultPreferences as UserPreferences,
        hasUnsavedChanges: true,
        error: null,
      };

    case 'IMPORT_PREFERENCES':
      return {
        ...state,
        preferences: action.payload,
        hasUnsavedChanges: true,
        error: null,
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    default:
      return state;
  }
};

// Create context
const PreferencesContext = createContext<PreferencesContextType | undefined>(undefined);

// Storage keys
const PREFERENCES_STORAGE_KEY = 'user_preferences';
const PREFERENCES_VERSION = '1.0.0';

// Provider component
interface PreferencesProviderProps {
  children: ReactNode;
}

export const PreferencesProvider: React.FC<PreferencesProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(preferencesReducer, initialState);

  // Load preferences from localStorage on mount
  useEffect(() => {
    const loadPreferences = () => {
      try {
        const stored = localStorage.getItem(PREFERENCES_STORAGE_KEY);
        if (stored) {
          const parsed = JSON.parse(stored);
          // Merge with defaults to ensure all properties exist
          const mergedPreferences = {
            ...defaultPreferences,
            ...parsed,
            // Ensure nested objects are properly merged
            notifications: { ...defaultPreferences.notifications, ...parsed.notifications },
            accessibility: { ...defaultPreferences.accessibility, ...parsed.accessibility },
            display: { ...defaultPreferences.display, ...parsed.display },
            privacy: { ...defaultPreferences.privacy, ...parsed.privacy },
            developer: { ...defaultPreferences.developer, ...parsed.developer },
          } as UserPreferences;

          dispatch({ type: 'SET_PREFERENCES', payload: mergedPreferences });
        }
      } catch (error) {
        console.error('Failed to load preferences from storage:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load preferences' });
      }
    };

    loadPreferences();
  }, []);

  // Apply accessibility preferences to document
  useEffect(() => {
    const { accessibility, display } = state.preferences;

    // Apply reduced motion
    if (accessibility.reduceMotion) {
      document.documentElement.style.setProperty('--motion-reduce', '1');
    } else {
      document.documentElement.style.removeProperty('--motion-reduce');
    }

    // Apply font size
    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px',
      'extra-large': '20px',
    };
    document.documentElement.style.setProperty('--base-font-size', fontSizeMap[accessibility.fontSize]);

    // Apply high contrast
    if (accessibility.highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }

    // Apply density
    document.documentElement.classList.remove('density-compact', 'density-comfortable', 'density-spacious');
    document.documentElement.classList.add(`density-${display.density}`);
  }, [state.preferences]);

  // Validate preferences
  const validatePreferences = (preferences: UserPreferences): boolean => {
    try {
      // Validate each section
      const sections = ['notifications', 'accessibility', 'display', 'privacy', 'developer'] as const;

      for (const section of sections) {
        const sectionData = preferences[section];
        const sectionValidation = preferencesValidation[section];

        const result = validateForm(sectionData, sectionValidation as any);
        if (!result.isValid) {
          console.error(`Validation failed for ${section}:`, result.errors);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Preferences validation error:', error);
      return false;
    }
  };

  // Update entire preferences
  const updatePreferences = (newPreferences: Partial<UserPreferences>) => {
    const updatedPreferences = { ...state.preferences, ...newPreferences };

    if (validatePreferences(updatedPreferences)) {
      dispatch({ type: 'SET_PREFERENCES', payload: updatedPreferences });
    } else {
      dispatch({ type: 'SET_ERROR', payload: 'Invalid preferences data' });
    }
  };

  // Update specific section
  const updateSection = <K extends keyof UserPreferences>(
    section: K,
    data: Partial<UserPreferences[K]>
  ) => {
    dispatch({ type: 'UPDATE_SECTION', payload: { section, data } });
  };

  // Reset to defaults
  const resetPreferences = () => {
    dispatch({ type: 'RESET_PREFERENCES' });
  };

  // Save to localStorage and optionally to server
  const savePreferences = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      // Save to localStorage
      localStorage.setItem(PREFERENCES_STORAGE_KEY, JSON.stringify(state.preferences));

      // TODO: Save to server when backend is ready
      // await api.savePreferences(state.preferences);

      dispatch({ type: 'SET_PREFERENCES', payload: state.preferences });
    } catch (error) {
      console.error('Failed to save preferences:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to save preferences' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Export preferences as JSON
  const exportPreferences = (): string => {
    const exportData = {
      version: PREFERENCES_VERSION,
      exportDate: new Date().toISOString(),
      preferences: {
        notifications: state.preferences.notifications,
        accessibility: state.preferences.accessibility,
        display: state.preferences.display,
        privacy: state.preferences.privacy,
        developer: state.preferences.developer,
      },
    };

    return JSON.stringify(exportData, null, 2);
  };

  // Import preferences from JSON
  const importPreferences = async (data: string): Promise<boolean> => {
    try {
      const parsed = JSON.parse(data);

      // Validate import structure
      if (!parsed.preferences || !parsed.version) {
        throw new Error('Invalid import format');
      }

      // Merge with current preferences
      const mergedPreferences = {
        ...state.preferences,
        ...parsed.preferences,
      };

      if (validatePreferences(mergedPreferences)) {
        dispatch({ type: 'IMPORT_PREFERENCES', payload: mergedPreferences });
        return true;
      } else {
        throw new Error('Invalid preferences data');
      }
    } catch (error) {
      console.error('Failed to import preferences:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to import preferences' });
      return false;
    }
  };

  const contextValue: PreferencesContextType = {
    ...state,
    updatePreferences,
    updateSection,
    resetPreferences,
    savePreferences,
    exportPreferences,
    importPreferences,
    validatePreferences,
  };

  return (
    <PreferencesContext.Provider value={contextValue}>
      {children}
    </PreferencesContext.Provider>
  );
};

// Custom hook
export const usePreferences = (): PreferencesContextType => {
  const context = useContext(PreferencesContext);
  if (context === undefined) {
    throw new Error('usePreferences must be used within a PreferencesProvider');
  }
  return context;
};
