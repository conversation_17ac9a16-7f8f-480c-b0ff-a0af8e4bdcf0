"""
Comprehensive Tests for Enhanced Dependency System
Tests for high and medium priority dependency system improvements.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

# Test the enhanced dependency models
def test_conditional_dependency_models():
    """Test conditional dependency models."""
    from src.ai_coding_agent.models import (
        ConditionalDependency, ConditionType, DependencyType,
        ConditionEvaluationContext, ConditionEvaluationResult
    )
    
    # Test ConditionalDependency
    conditional_dep = ConditionalDependency(
        dependency_id="dep-123",
        dependency_type=DependencyType.TASK,
        condition="platform == 'web'",
        condition_type=ConditionType.PLATFORM,
        description="Only applies to web platform"
    )
    
    assert conditional_dep.dependency_id == "dep-123"
    assert conditional_dep.condition == "platform == 'web'"
    assert conditional_dep.condition_type == ConditionType.PLATFORM
    assert conditional_dep.is_active is True
    
    # Test ConditionEvaluationContext
    context = ConditionEvaluationContext(
        platform="web",
        environment="production",
        feature_flags={"new_ui": True},
        user_choices={"theme": "dark"}
    )
    
    assert context.platform == "web"
    assert context.feature_flags["new_ui"] is True
    
    # Test ConditionEvaluationResult
    result = ConditionEvaluationResult(
        condition_id="cond-123",
        dependency_id="dep-123",
        condition_met=True,
        evaluation_details="Condition evaluated successfully",
        context_used=context
    )
    
    assert result.condition_met is True
    assert result.dependency_id == "dep-123"
    
    print("✅ Conditional dependency models test passed")


def test_external_dependency_models():
    """Test external dependency models."""
    from src.ai_coding_agent.models import (
        ExternalDependency, ExternalDependencyStatus, DependencyType,
        ApprovalDependency, TimeDependency
    )
    
    # Test ExternalDependency
    external_dep = ExternalDependency(
        dependency_id="ext-123",
        name="User Service API",
        dependency_type=DependencyType.EXTERNAL_API,
        endpoint_url="https://api.example.com/users",
        check_method="GET",
        status=ExternalDependencyStatus.AVAILABLE
    )
    
    assert external_dep.name == "User Service API"
    assert external_dep.dependency_type == DependencyType.EXTERNAL_API
    assert external_dep.status == ExternalDependencyStatus.AVAILABLE
    
    # Test ApprovalDependency
    approval_dep = ApprovalDependency(
        dependency_id="approval-123",
        name="Security Review",
        approvers=["security-team", "lead-dev"],
        approval_type="any",
        description="Security review required"
    )
    
    assert approval_dep.name == "Security Review"
    assert len(approval_dep.approvers) == 2
    assert approval_dep.approval_type == "any"
    
    # Test TimeDependency
    time_dep = TimeDependency(
        dependency_id="time-123",
        name="Scheduled Maintenance Window",
        time_type="absolute",
        target_time=datetime.utcnow() + timedelta(hours=24)
    )
    
    assert time_dep.name == "Scheduled Maintenance Window"
    assert time_dep.time_type == "absolute"
    
    print("✅ External dependency models test passed")


def test_batch_operations_models():
    """Test batch operations models."""
    from src.ai_coding_agent.models import (
        BatchDependencyCheckRequest, BatchDependencyCheckResponse,
        DependencyType, ConditionEvaluationContext
    )
    
    # Test BatchDependencyCheckRequest
    context = ConditionEvaluationContext(platform="web")
    
    batch_request = BatchDependencyCheckRequest(
        entity_ids=["task-1", "task-2", "task-3"],
        entity_type=DependencyType.TASK,
        include_soft_dependencies=True,
        include_conditional_dependencies=True,
        evaluation_context=context,
        user_id="user-123"
    )
    
    assert len(batch_request.entity_ids) == 3
    assert batch_request.entity_type == DependencyType.TASK
    assert batch_request.include_soft_dependencies is True
    
    # Test BatchDependencyCheckResponse
    batch_response = BatchDependencyCheckResponse(
        request_id="req-123",
        results={},
        processing_time=1.5
    )
    
    assert batch_response.request_id == "req-123"
    assert batch_response.processing_time == 1.5
    
    print("✅ Batch operations models test passed")


def test_analytics_models():
    """Test analytics models."""
    from src.ai_coding_agent.models import (
        DependencyBottleneck, DependencyMetrics, DelayPrediction,
        DependencyAnalyticsReport, DependencyType
    )
    
    # Test DependencyBottleneck
    bottleneck = DependencyBottleneck(
        entity_id="task-123",
        entity_type=DependencyType.TASK,
        entity_name="API Integration",
        bottleneck_type="external",
        severity="high",
        affected_entities=["task-124", "task-125"],
        estimated_delay=8,
        resolution_suggestions=["Check API status", "Contact vendor"]
    )
    
    assert bottleneck.entity_name == "API Integration"
    assert bottleneck.severity == "high"
    assert len(bottleneck.affected_entities) == 2
    
    # Test DependencyMetrics
    metrics = DependencyMetrics(
        roadmap_id="roadmap-123",
        timeframe_start=datetime.utcnow() - timedelta(days=7),
        timeframe_end=datetime.utcnow(),
        total_dependencies_checked=100,
        dependencies_resolved=85,
        dependencies_blocked=15,
        average_resolution_time=4.5
    )
    
    assert metrics.roadmap_id == "roadmap-123"
    assert metrics.total_dependencies_checked == 100
    assert metrics.average_resolution_time == 4.5
    
    # Test DelayPrediction
    delay_prediction = DelayPrediction(
        entity_id="phase-123",
        entity_type=DependencyType.PHASE,
        entity_name="Backend Development",
        predicted_delay=16,
        confidence=0.8,
        delay_causes=["API dependency", "Resource constraint"],
        mitigation_strategies=["Parallel development", "Additional resources"],
        impact_on_project="Minor delay to overall timeline"
    )
    
    assert delay_prediction.predicted_delay == 16
    assert delay_prediction.confidence == 0.8
    assert len(delay_prediction.delay_causes) == 2
    
    print("✅ Analytics models test passed")


def test_visualization_models():
    """Test visualization models."""
    from src.ai_coding_agent.models import (
        VisualizationNode, VisualizationEdge, DependencyGraphVisualization,
        GanttChartTask, GanttChart, DashboardWidget, DependencyDashboard,
        DependencyType
    )
    
    # Test VisualizationNode
    node = VisualizationNode(
        id="node-123",
        label="Task 1",
        type=DependencyType.TASK,
        status="in_progress",
        x=100.0,
        y=200.0,
        color="#6bcf7f",
        size=15
    )
    
    assert node.label == "Task 1"
    assert node.type == DependencyType.TASK
    assert node.x == 100.0
    
    # Test VisualizationEdge
    edge = VisualizationEdge(
        source="node-123",
        target="node-124",
        type="dependency",
        color="#666666",
        style="solid"
    )
    
    assert edge.source == "node-123"
    assert edge.type == "dependency"
    
    # Test GanttChartTask
    gantt_task = GanttChartTask(
        id="task-123",
        name="Implement API",
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(hours=8),
        duration=8,
        progress=0.5,
        dependencies=["task-122"],
        assigned_to="backend_agent",
        priority="high",
        status="in_progress"
    )
    
    assert gantt_task.name == "Implement API"
    assert gantt_task.duration == 8
    assert gantt_task.progress == 0.5
    
    print("✅ Visualization models test passed")


def test_external_tool_integration_models():
    """Test external tool integration models."""
    from src.ai_coding_agent.models import (
        ExternalToolType, ExternalToolConfig, ExternalToolSyncStatus,
        ExternalToolSyncResult, JiraIssueMapping, GitHubIssueMapping,
        CICDPipelineMapping, ExternalToolIntegration
    )
    
    # Test ExternalToolConfig
    tool_config = ExternalToolConfig(
        tool_type=ExternalToolType.JIRA,
        name="JIRA Integration",
        base_url="https://company.atlassian.net",
        api_key="secret-key",
        project_id="PROJ",
        sync_enabled=True,
        field_mappings={"status": "Status", "assignee": "Assignee"}
    )
    
    assert tool_config.tool_type == ExternalToolType.JIRA
    assert tool_config.name == "JIRA Integration"
    assert tool_config.sync_enabled is True
    
    # Test ExternalToolSyncResult
    sync_result = ExternalToolSyncResult(
        tool_config_id="config-123",
        sync_type="bidirectional",
        status=ExternalToolSyncStatus.SUCCESS,
        items_processed=25,
        items_created=5,
        items_updated=15,
        sync_duration=30.5
    )
    
    assert sync_result.status == ExternalToolSyncStatus.SUCCESS
    assert sync_result.items_processed == 25
    assert sync_result.sync_duration == 30.5
    
    # Test JiraIssueMapping
    jira_mapping = JiraIssueMapping(
        task_id="task-123",
        jira_issue_key="PROJ-456",
        jira_issue_id="10123",
        sync_direction="bidirectional"
    )
    
    assert jira_mapping.task_id == "task-123"
    assert jira_mapping.jira_issue_key == "PROJ-456"
    
    print("✅ External tool integration models test passed")


def test_condition_evaluator():
    """Test condition evaluator functionality."""
    from src.ai_coding_agent.services.dependency_engine import ConditionEvaluator
    from src.ai_coding_agent.models import ConditionEvaluationContext
    
    evaluator = ConditionEvaluator()
    
    # Test simple condition evaluation
    context = ConditionEvaluationContext(
        platform="web",
        environment="production",
        feature_flags={"new_ui": True}
    )
    
    # Test platform condition
    result = evaluator.evaluate_condition("platform == 'web'", context)
    assert result is True
    
    # Test environment condition
    result = evaluator.evaluate_condition("environment == 'staging'", context)
    assert result is False
    
    # Test feature flag condition
    result = evaluator.evaluate_condition("feature_flags['new_ui'] == True", context)
    assert result is True
    
    # Test complex condition
    result = evaluator.evaluate_condition("platform == 'web' and environment == 'production'", context)
    assert result is True
    
    print("✅ Condition evaluator test passed")


def test_dependency_cache():
    """Test dependency caching functionality."""
    from src.ai_coding_agent.services.dependency_engine import DependencyCache
    from src.ai_coding_agent.models import DependencyGraph
    
    cache = DependencyCache()
    
    # Test caching dependency graph
    graph = DependencyGraph(
        entities={"task-1": {"name": "Task 1"}},
        dependencies={"task-1": []},
        reverse_dependencies={"task-1": []}
    )
    
    cache.cache_dependency_graph("roadmap-123", graph)
    cached_graph = cache.get_cached_dependency_graph("roadmap-123")
    
    assert cached_graph is not None
    assert cached_graph.entities["task-1"]["name"] == "Task 1"
    
    # Test cache invalidation
    cache.invalidate_cache("task-1")
    
    # Test cache clearing
    cache.clear_all()
    assert len(cache.dependency_graph_cache) == 0
    
    print("✅ Dependency cache test passed")


def main():
    """Run all enhanced dependency system tests."""
    print("🚀 Starting Enhanced Dependency System Tests\n")
    
    tests = [
        test_conditional_dependency_models,
        test_external_dependency_models,
        test_batch_operations_models,
        test_analytics_models,
        test_visualization_models,
        test_external_tool_integration_models,
        test_condition_evaluator,
        test_dependency_cache,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            print(f"📋 Running {test.__name__}...")
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {str(e)}")
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL ENHANCED DEPENDENCY SYSTEM TESTS PASSED!")
        print("✅ High and medium priority improvements are working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
