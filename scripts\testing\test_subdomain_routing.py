#!/usr/bin/env python3
"""
Test Script for NGINX Subdomain Routing.

Tests the container-per-user subdomain routing system to ensure:
1. User containers are accessible via subdomains
2. Authentication is properly enforced
3. Port resolution works correctly
4. Security isolation is maintained
"""

import asyncio
import aiohttp
import logging
import sys
from typing import Dict, Any, Optional
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SubdomainRoutingTester:
    """Test suite for subdomain routing functionality."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.nginx_url = "http://localhost"
        self.auth_token: Optional[str] = None
        self.test_user_id = "test-user-123"
        
    async def authenticate(self, session: aiohttp.ClientSession) -> bool:
        """Authenticate and get access token."""
        try:
            # Test authentication endpoint
            auth_data = {
                "username": "<EMAIL>",
                "password": "testpassword"
            }
            
            async with session.post(
                f"{self.base_url}/api/v1/auth/login",
                json=auth_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    logger.info("✅ Authentication successful")
                    return True
                else:
                    logger.warning(f"⚠️ Authentication failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False
    
    async def test_container_provisioning(self, session: aiohttp.ClientSession) -> bool:
        """Test container provisioning for subdomain routing."""
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            provision_data = {
                "project_type": "react",
                "project_name": "test-project"
            }
            
            async with session.post(
                f"{self.base_url}/api/v1/containers/provision",
                json=provision_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Container provisioned: {data.get('container_name')}")
                    logger.info(f"   Preview URL: {data.get('preview_url')}")
                    return True
                else:
                    logger.error(f"❌ Container provisioning failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Container provisioning error: {e}")
            return False
    
    async def test_auth_check_endpoint(self, session: aiohttp.ClientSession) -> bool:
        """Test NGINX auth check endpoint."""
        try:
            headers = {
                "X-User-ID": self.test_user_id,
                "X-Original-URI": "/",
                "X-Real-IP": "127.0.0.1"
            }
            
            async with session.get(
                f"{self.base_url}/api/v1/containers/auth-check",
                headers=headers
            ) as response:
                if response.status in [200, 403]:  # Both are valid responses
                    logger.info(f"✅ Auth check endpoint working: {response.status}")
                    return True
                else:
                    logger.error(f"❌ Auth check endpoint failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Auth check endpoint error: {e}")
            return False
    
    async def test_port_resolution_endpoint(self, session: aiohttp.ClientSession) -> bool:
        """Test NGINX port resolution endpoint."""
        try:
            headers = {
                "X-User-ID": self.test_user_id
            }
            
            async with session.get(
                f"{self.base_url}/api/v1/containers/get-port",
                headers=headers
            ) as response:
                if response.status in [200, 404]:  # Both are valid responses
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ Port resolution working: port {data.get('port')}")
                    else:
                        logger.info("✅ Port resolution working: no active container")
                    return True
                else:
                    logger.error(f"❌ Port resolution failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Port resolution error: {e}")
            return False
    
    async def test_subdomain_access(self, session: aiohttp.ClientSession) -> bool:
        """Test actual subdomain access (requires local DNS setup)."""
        try:
            # Test subdomain URL
            subdomain_url = f"http://preview-{self.test_user_id}.localhost"
            
            async with session.get(subdomain_url, timeout=5) as response:
                if response.status in [200, 502, 503]:  # 502/503 expected if no container
                    logger.info(f"✅ Subdomain routing accessible: {response.status}")
                    return True
                else:
                    logger.warning(f"⚠️ Subdomain routing: {response.status}")
                    return False
                    
        except asyncio.TimeoutError:
            logger.warning("⚠️ Subdomain access timeout (expected if no local DNS)")
            return True  # This is expected in most test environments
        except Exception as e:
            logger.warning(f"⚠️ Subdomain access error: {e} (expected if no local DNS)")
            return True  # This is expected in most test environments
    
    async def test_container_health(self, session: aiohttp.ClientSession) -> bool:
        """Test container service health."""
        try:
            async with session.get(f"{self.base_url}/api/v1/containers/health") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Container service healthy: {data.get('status')}")
                    logger.info(f"   Docker connected: {data.get('docker_connected')}")
                    return True
                else:
                    logger.error(f"❌ Container service unhealthy: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Container health check error: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all subdomain routing tests."""
        results = {}
        
        async with aiohttp.ClientSession() as session:
            logger.info("🚀 Starting Subdomain Routing Tests")
            logger.info("=" * 50)
            
            # Test 1: Container Health
            results["container_health"] = await self.test_container_health(session)
            
            # Test 2: Authentication (optional)
            results["authentication"] = await self.authenticate(session)
            
            # Test 3: Auth Check Endpoint
            results["auth_check_endpoint"] = await self.test_auth_check_endpoint(session)
            
            # Test 4: Port Resolution Endpoint
            results["port_resolution_endpoint"] = await self.test_port_resolution_endpoint(session)
            
            # Test 5: Container Provisioning (if authenticated)
            if results["authentication"]:
                results["container_provisioning"] = await self.test_container_provisioning(session)
            
            # Test 6: Subdomain Access
            results["subdomain_access"] = await self.test_subdomain_access(session)
            
            logger.info("=" * 50)
            logger.info("📊 Test Results Summary:")
            
            passed = 0
            total = len(results)
            
            for test_name, passed_test in results.items():
                status = "✅ PASS" if passed_test else "❌ FAIL"
                logger.info(f"   {test_name}: {status}")
                if passed_test:
                    passed += 1
            
            logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
            
            if passed == total:
                logger.info("🎉 All tests passed! Subdomain routing is ready!")
            elif passed >= total * 0.8:
                logger.info("⚠️ Most tests passed. Minor issues to address.")
            else:
                logger.error("❌ Multiple test failures. System needs attention.")
        
        return results


async def main():
    """Main test runner."""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    tester = SubdomainRoutingTester(base_url)
    results = await tester.run_all_tests()
    
    # Exit with appropriate code
    passed = sum(results.values())
    total = len(results)
    
    if passed == total:
        sys.exit(0)  # All tests passed
    elif passed >= total * 0.8:
        sys.exit(1)  # Minor issues
    else:
        sys.exit(2)  # Major issues


if __name__ == "__main__":
    asyncio.run(main())
