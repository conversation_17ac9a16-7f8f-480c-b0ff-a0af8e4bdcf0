# AI Coding Agent Environment Configuration Template
# Copy this file to .env and fill in your actual values
# CRITICAL: All values marked as REQUIRED must be set for containers to work

# =============================================================================
# SECURITY CONFIGURATION (REQUIRED FOR DOCKER)
# =============================================================================

# Application secret key (32+ characters, generate with: openssl rand -hex 32)
# REQUIRED: Container will fail without this
SECRET_KEY=your_super_secret_key_that_must_be_at_least_32_characters_long

# Configuration encryption key (32+ characters, generate with: openssl rand -hex 32)
# REQUIRED: Container will fail without this
CONFIG_ENCRYPTION_KEY=your_config_encryption_key_32_chars_min

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

APP_NAME=AI Coding Agent
APP_VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# JWT Configuration
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# DATABASE CONFIGURATION (REQUIRED FOR DOCKER)
# =============================================================================

# PostgreSQL Database (Docker service names for containers)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=ai_coding_agent
DB_USER=agent
# REQUIRED: Set a secure password for database
DB_PASSWORD=your_secure_database_password_here

# Database URL (constructed from above values)
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# =============================================================================
# AI SERVICE CONFIGURATION
# =============================================================================

# Ollama Configuration (Docker service name for containers)
OLLAMA_HOST=http://host.docker.internal:11434
OLLAMA_TIMEOUT=300
DEFAULT_AI_MODEL=llama3.2:3b

# Specialized Agent Models (5 core agents)
ARCHITECT_AGENT_MODEL=llama3.2:3b
FRONTEND_AGENT_MODEL=starcoder2:3b
BACKEND_AGENT_MODEL=deepseek-coder:6.7b-instruct-q4_0
SHELL_AGENT_MODEL=qwen2.5:3b
ISSUE_FIX_AGENT_MODEL=yi-coder:1.5b

# Code-specific task models
CODE_COMPLETION_MODEL=starcoder2:3b
CODE_GENERATION_MODEL=deepseek-coder:6.7b-instruct
CODE_REVIEW_MODEL=deepseek-coder:6.7b-instruct

# Chat and specialized models
CHAT_MODEL=llama3.2:3b
DOCUMENTATION_MODEL=llama3.2:3b

# AI Performance settings
AI_MAX_TOKENS=4096
AI_TEMPERATURE=0.7

# =============================================================================
# REDIS CACHE CONFIGURATION
# =============================================================================

# Redis Cache Service (replaces SQLite3 local cache)
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# =============================================================================
# VECTOR DATABASE CONFIGURATION (pgvector via Supabase)
# =============================================================================

# Vector embeddings now stored in Supabase with pgvector extension
# No separate vector database service needed

# Embedding Models (same as before)
LTKB_EMBEDDING_MODEL=nomic-embed-text:v1.5
STPM_EMBEDDING_MODEL=mxbai-embed-large

# =============================================================================
# SUPABASE CONFIGURATION (OPTIONAL)
# =============================================================================

# Supabase Cloud Database (optional - for cloud features)
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# =============================================================================
# DOCKER & DEVELOPMENT CONFIGURATION
# =============================================================================

# Docker Compose Project Name
COMPOSE_PROJECT_NAME=ai-coding-agent

# Development Frontend URL
REACT_APP_API_URL=http://localhost:8000

# Hot Reload (development only)
RELOAD=true

# Logging Configuration
LOG_LEVEL=INFO

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================

# Rate Limiting
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW_MINUTES=1
ADMIN_RATE_LIMIT_REQUESTS=20
ADMIN_RATE_LIMIT_WINDOW_MINUTES=5
AUTH_RATE_LIMIT_REQUESTS=5
AUTH_RATE_LIMIT_WINDOW_MINUTES=15

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/Disable Features
ENABLE_LTKB=true
ENABLE_VECTOR_SEARCH=true
ENABLE_AGENT_COLLABORATION=true
ENABLE_PROJECT_TEMPLATES=true
ENABLE_AUDIT_LOGGING=true

# =============================================================================
# DOCKER VOLUME PERMISSIONS (IMPORTANT FOR LINUX/MAC)
# =============================================================================

# User ID for volume mounts (set to your user ID: `id -u`)
# This prevents permission issues with mounted volumes
# DOCKER_USER_ID=1000
# DOCKER_GROUP_ID=1000
