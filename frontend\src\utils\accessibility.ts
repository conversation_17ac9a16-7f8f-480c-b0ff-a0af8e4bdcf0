import React from 'react';

/**
 * Accessibility utilities and hooks for WCAG compliance
 * Provides keyboard navigation, focus management, and screen reader support
 */

export interface AccessibilityConfig {
  enableFocusVisible: boolean;
  enableAnnouncements: boolean;
  enableKeyboardShortcuts: boolean;
  reducedMotion: boolean;
}

/**
 * Hook to manage focus trap for modals and overlays
 */
export const useFocusTrap = (isActive: boolean) => {
  const containerRef = React.useRef<HTMLElement>(null);
  const firstFocusableRef = React.useRef<HTMLElement | null>(null);
  const lastFocusableRef = React.useRef<HTMLElement | null>(null);

  React.useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstFocusable = focusableElements[0] as HTMLElement;
    const lastFocusable = focusableElements[focusableElements.length - 1] as HTMLElement;

    firstFocusableRef.current = firstFocusable;
    lastFocusableRef.current = lastFocusable;

    // Focus the first element
    firstFocusable?.focus();

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstFocusable) {
          e.preventDefault();
          lastFocusable?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastFocusable) {
          e.preventDefault();
          firstFocusable?.focus();
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);

    return () => {
      document.removeEventListener('keydown', handleTabKey);
    };
  }, [isActive]);

  return containerRef;
};

/**
 * Hook for managing focus restoration
 */
export const useFocusRestore = () => {
  const previousFocusRef = React.useRef<HTMLElement | null>(null);

  const saveFocus = React.useCallback(() => {
    previousFocusRef.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = React.useCallback(() => {
    if (previousFocusRef.current) {
      previousFocusRef.current.focus();
      previousFocusRef.current = null;
    }
  }, []);

  return { saveFocus, restoreFocus };
};

/**
 * Hook for keyboard navigation
 */
export const useKeyboardNavigation = (
  items: HTMLElement[],
  options: {
    loop?: boolean;
    orientation?: 'horizontal' | 'vertical' | 'both';
    onSelect?: (index: number) => void;
  } = {}
) => {
  const { loop = true, orientation = 'vertical', onSelect } = options;
  const [currentIndex, setCurrentIndex] = React.useState(-1);

  const handleKeyDown = React.useCallback((e: KeyboardEvent) => {
    const { key } = e;
    const isHorizontal = orientation === 'horizontal' || orientation === 'both';
    const isVertical = orientation === 'vertical' || orientation === 'both';

    let newIndex = currentIndex;

    switch (key) {
      case 'ArrowDown':
        if (isVertical) {
          e.preventDefault();
          newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : loop ? 0 : currentIndex;
        }
        break;
      case 'ArrowUp':
        if (isVertical) {
          e.preventDefault();
          newIndex = currentIndex > 0 ? currentIndex - 1 : loop ? items.length - 1 : currentIndex;
        }
        break;
      case 'ArrowRight':
        if (isHorizontal) {
          e.preventDefault();
          newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : loop ? 0 : currentIndex;
        }
        break;
      case 'ArrowLeft':
        if (isHorizontal) {
          e.preventDefault();
          newIndex = currentIndex > 0 ? currentIndex - 1 : loop ? items.length - 1 : currentIndex;
        }
        break;
      case 'Home':
        e.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        newIndex = items.length - 1;
        break;
      case 'Enter':
      case ' ':
        if (currentIndex >= 0 && onSelect) {
          e.preventDefault();
          onSelect(currentIndex);
        }
        break;
    }

    if (newIndex !== currentIndex && newIndex >= 0 && newIndex < items.length) {
      setCurrentIndex(newIndex);
      items[newIndex]?.focus();
    }
  }, [currentIndex, items, loop, orientation, onSelect]);

  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { currentIndex, setCurrentIndex };
};

/**
 * Hook for screen reader announcements
 */
export const useAnnouncer = () => {
  const announcerRef = React.useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    // Create live region for announcements
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.id = 'accessibility-announcer';
    document.body.appendChild(announcer);
    announcerRef.current = announcer;

    return () => {
      if (announcerRef.current && document.body.contains(announcerRef.current)) {
        document.body.removeChild(announcerRef.current);
      }
    };
  }, []);

  const announce = React.useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announcerRef.current) {
      announcerRef.current.setAttribute('aria-live', priority);
      announcerRef.current.textContent = message;

      // Clear after announcement
      setTimeout(() => {
        if (announcerRef.current) {
          announcerRef.current.textContent = '';
        }
      }, 1000);
    }
  }, []);

  return { announce };
};

/**
 * Hook to detect reduced motion preference
 */
export const useReducedMotion = (): boolean => {
  const [reducedMotion, setReducedMotion] = React.useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setReducedMotion(mediaQuery.matches);

    const handleChange = () => setReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return reducedMotion;
};

/**
 * Hook for managing skip links
 */
export const useSkipLinks = (links: Array<{ href: string; label: string }>) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const skipLinksRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab' && !e.shiftKey && document.activeElement === document.body) {
        setIsVisible(true);
      }
    };

    const handleFocusOut = () => {
      if (!skipLinksRef.current?.contains(document.activeElement)) {
        setIsVisible(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('focusout', handleFocusOut);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('focusout', handleFocusOut);
    };
  }, []);

  return { isVisible, skipLinksRef };
};

/**
 * ARIA utilities
 */
export const ariaUtils = {
  // Generate unique IDs for ARIA relationships
  generateId: (prefix: string = 'aria'): string => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  },

  // Common ARIA attributes
  button: {
    role: 'button',
    tabIndex: 0,
  },

  dialog: {
    role: 'dialog',
    'aria-modal': true,
  },

  menu: {
    role: 'menu',
  },

  menuitem: {
    role: 'menuitem',
  },

  navigation: {
    role: 'navigation',
  },

  main: {
    role: 'main',
  },

  complementary: {
    role: 'complementary',
  },

  banner: {
    role: 'banner',
  },

  contentinfo: {
    role: 'contentinfo',
  },
};

/**
 * Focus management utilities
 */
export const focusUtils = {
  // Set focus with optional delay
  setFocus: (element: HTMLElement | null, delay: number = 0) => {
    if (element) {
      setTimeout(() => element.focus(), delay);
    }
  },

  // Focus first focusable element in container
  focusFirst: (container: HTMLElement) => {
    const focusable = container.querySelector(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement;
    focusable?.focus();
  },

  // Focus last focusable element in container
  focusLast: (container: HTMLElement) => {
    const focusable = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const lastFocusable = focusable[focusable.length - 1] as HTMLElement;
    lastFocusable?.focus();
  },
};

/**
 * Keyboard shortcut utilities
 */
export const keyboardUtils = {
  // Check if key combination matches
  matchesShortcut: (
    event: KeyboardEvent,
    shortcut: {
      key: string;
      ctrl?: boolean;
      alt?: boolean;
      shift?: boolean;
      meta?: boolean;
    }
  ): boolean => {
    return (
      event.key.toLowerCase() === shortcut.key.toLowerCase() &&
      !!event.ctrlKey === !!shortcut.ctrl &&
      !!event.altKey === !!shortcut.alt &&
      !!event.shiftKey === !!shortcut.shift &&
      !!event.metaKey === !!shortcut.meta
    );
  },

  // Common shortcuts
  shortcuts: {
    escape: { key: 'Escape' },
    enter: { key: 'Enter' },
    space: { key: ' ' },
    tab: { key: 'Tab' },
    home: { key: 'Home' },
    end: { key: 'End' },
    save: { key: 's', ctrl: true },
    copy: { key: 'c', ctrl: true },
    paste: { key: 'v', ctrl: true },
    undo: { key: 'z', ctrl: true },
    redo: { key: 'y', ctrl: true },
  },
};
