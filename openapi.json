{"openapi": "3.1.0", "info": {"title": "AI Coding Agent", "description": "Intelligent no-code platform with AI agent orchestration", "version": "0.1.0"}, "paths": {"/": {"get": {"summary": "Root", "description": "Root endpoint with basic application info.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/health": {"get": {"tags": ["health"], "summary": "Health Check", "description": "Health check endpoint.\n\nReturns the current status of the application and its services.\n\nReturns:\n    HealthResponse: Application health status", "operationId": "health_check_api_v1_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}, "/api/v1/health/ready": {"get": {"tags": ["health"], "summary": "Readiness Check", "description": "Readiness probe for Kubernetes/container orchestration.\n\nReturns:\n    Dict[str, str]: Readiness status", "operationId": "readiness_check_api_v1_health_ready_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Readiness Check Api V1 Health Ready Get"}}}}}}}, "/api/v1/health/live": {"get": {"tags": ["health"], "summary": "Liveness Check", "description": "Liveness probe for Kubernetes/container orchestration.\n\nReturns:\n    Dict[str, str]: Liveness status", "operationId": "liveness_check_api_v1_health_live_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Liveness Check Api V1 Health Live Get"}}}}}}}, "/api/v1/auth/register": {"post": {"tags": ["authentication"], "summary": "Register User", "description": "Register a new user account using Supabase Auth.\n\nCreates a new user with Supabase authentication and syncs to local database.\n\nArgs:\n    user_data: User registration data\n    supabase_auth: Supabase authentication service\n\nReturns:\n    Dict containing user info, tokens, and confirmation message\n\nRaises:\n    HTTPException: If registration fails", "operationId": "register_user_api_v1_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Register User Api V1 Auth Register Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["authentication"], "summary": "Login User", "description": "Authenticate user using Supabase Auth.\n\nValidates credentials against Supabase and returns authentication tokens.\n\nArgs:\n    login_data: User login credentials\n    supabase_auth: Supabase authentication service\n\nReturns:\n    Dict containing user info and authentication tokens\n\nRaises:\n    HTTPException: If authentication fails", "operationId": "login_user_api_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Login User Api V1 Auth Login Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["authentication"], "summary": "Logout User", "description": "Logout user and invalidate session.\n\nArgs:\n    request: FastAPI request object\n    supabase_auth: Supabase authentication service\n\nReturns:\n    Dict with logout confirmation", "operationId": "logout_user_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Logout User Api V1 Auth Logout Post"}}}}}}}, "/api/v1/auth/refresh": {"post": {"tags": ["authentication"], "summary": "Ref<PERSON>", "description": "Refresh authentication tokens.\n\nArgs:\n    refresh_token_data: Dict containing refresh_token\n    supabase_auth: Supabase authentication service\n\nReturns:\n    Dict with new authentication tokens", "operationId": "refresh_tokens_api_v1_auth_refresh_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Refresh Token Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Refresh Tokens Api V1 Auth Refresh Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/reset-password": {"post": {"tags": ["authentication"], "summary": "Reset Password", "description": "Send password reset email.\n\nArgs:\n    email_data: Dict containing email address\n    supabase_auth: Supabase authentication service\n\nReturns:\n    Dict with confirmation message", "operationId": "reset_password_api_v1_auth_reset_password_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Email Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Reset Password Api V1 Auth Reset Password Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/me": {"get": {"tags": ["authentication"], "summary": "Get Current User", "description": "Get current authenticated user information.\n\nArgs:\n    request: FastAPI request object\n    supabase_auth: Supabase authentication service\n    db: Database session\n\nReturns:\n    Dict with current user information", "operationId": "get_current_user_api_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Current User Api V1 Auth Me Get"}}}}}}, "put": {"tags": ["authentication"], "summary": "Update Current User", "description": "Update current user's profile information.\n\nArgs:\n    user_update_data: User data to update\n    request: FastAPI request object\n    supabase_auth: Supabase authentication service\n\nReturns:\n    Dict with updated user information", "operationId": "update_current_user_api_v1_auth_me_put", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "User Update Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Update Current User Api V1 Auth Me Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/legacy/register": {"post": {"tags": ["authentication"], "summary": "Legacy Register User", "description": "Legacy user registration endpoint (local database only).\n\nThis endpoint is kept for backward compatibility and testing.\nNew applications should use the /register endpoint with Supabase.", "operationId": "legacy_register_user_api_v1_auth_legacy_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/legacy/login": {"post": {"tags": ["authentication"], "summary": "Legacy Login User", "description": "Legacy user login endpoint (local database only).\n\nThis endpoint is kept for backward compatibility and testing.\nNew applications should use the /login endpoint with Supabase.", "operationId": "legacy_login_user_api_v1_auth_legacy_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Legacy Login User Api V1 Auth Legacy Login Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/legacy-auth/register": {"post": {"tags": ["legacy-auth"], "summary": "Register User", "description": "Register a new user account.\n\nCreates a new user with email verification and secure password hashing.\n\nArgs:\n    user_data: User registration data\n    db: Database session\n\nReturns:\n    UserResponse: The created user data (excluding password)\n\nRaises:\n    HTTPException: If username or email already exists", "operationId": "register_user_api_v1_legacy_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/legacy-auth/login": {"post": {"tags": ["legacy-auth"], "summary": "Login User", "description": "Authenticate user and return access tokens.\n\nValidates user credentials and returns JWT access and refresh tokens.\n\nArgs:\n    user_credentials: Username/email and password\n    db: Database session\n\nReturns:\n    dict: Access token, refresh token, and token type\n\nRaises:\n    HTTPException: If credentials are invalid", "operationId": "login_user_api_v1_legacy_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Login User Api V1 Legacy Auth Login Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/legacy-auth/refresh": {"post": {"tags": ["legacy-auth"], "summary": "Refresh <PERSON>", "description": "Refresh an access token using a refresh token.\n\nArgs:\n    refresh_token: Valid refresh token\n    db: Database session\n\nReturns:\n    dict: New access token and expiry\n\nRaises:\n    HTTPException: If refresh token is invalid", "operationId": "refresh_token_api_v1_legacy_auth_refresh_post", "parameters": [{"name": "refresh_token", "in": "query", "required": true, "schema": {"type": "string", "title": "Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Refresh Token Api V1 Legacy Auth Refresh Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/legacy-auth/me": {"get": {"tags": ["legacy-auth"], "summary": "Get Current User Info", "description": "Get current authenticated user information.\n\nArgs:\n    current_user: Current authenticated user\n\nReturns:\n    UserResponse: Current user data", "operationId": "get_current_user_info_api_v1_legacy_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/legacy-auth/logout": {"post": {"tags": ["legacy-auth"], "summary": "Logout User", "description": "Logout current user.\n\nNote: Since we're using stateless JWT tokens, this endpoint\nprimarily serves as a confirmation. In a production environment,\nyou might want to implement token blacklisting.\n\nArgs:\n    current_user: Current authenticated user\n\nReturns:\n    dict: Logout confirmation message", "operationId": "logout_user_api_v1_legacy_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Logout User Api V1 Legacy Auth Logout Post"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/ai/chat": {"post": {"tags": ["ai", "AI"], "summary": "Chat Completion", "description": "Generate a chat completion using the specified or auto-selected agent.", "operationId": "chat_completion_api_v1_ai_chat_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequestModel"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/chat/stream": {"post": {"tags": ["ai", "AI"], "summary": "Stream Chat Completion", "description": "Generate a streaming chat completion using the specified or auto-selected agent.", "operationId": "stream_chat_completion_api_v1_ai_chat_stream_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequestModel"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/tasks": {"post": {"tags": ["ai", "AI"], "summary": "Execute Task", "description": "Execute a task using intelligent agent selection and routing.", "operationId": "execute_task_api_v1_ai_tasks_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequestModel"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/execute-enhanced-task": {"post": {"tags": ["ai", "AI"], "summary": "Execute Enhanced Task", "description": "Execute a task with enhanced features including verification and auto-fix.\n\nFeatures:\n- Automatic agent selection based on task description\n- Output verification by Architect Agent\n- Auto-fixing of issues when detected\n- Dependency detection and installation", "operationId": "execute_enhanced_task_api_v1_ai_execute_enhanced_task_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancedTaskRequestModel"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/collaborate": {"post": {"tags": ["ai", "AI"], "summary": "Collaborate Agents", "description": "Execute a task using multiple collaborating agents.", "operationId": "collaborate_agents_api_v1_ai_collaborate_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollaborationRequestModel"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/TaskResult"}, "type": "array", "title": "Response Collaborate Agents Api V1 Ai Collaborate Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/collaborate-enhanced": {"post": {"tags": ["ai", "AI"], "summary": "Collaborate Agents Enhanced", "description": "Execute a task using multiple collaborating agents with enhanced features.\n\nThis endpoint orchestrates multiple agents with:\n- Cross-agent verification and quality checks\n- Automatic dependency management\n- Context sharing between agents\n- Sequential, parallel, or hierarchical coordination", "operationId": "collaborate_agents_enhanced_api_v1_ai_collaborate_enhanced_post", "parameters": [{"name": "verification_enabled", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Verification Enabled"}}, {"name": "auto_dependency_management", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Auto Dependency Management"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollaborationRequestModel"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/verify-code": {"post": {"tags": ["ai", "AI"], "summary": "Verify Code", "description": "Verify code quality using the Architect Agent.\n\nThis endpoint provides detailed code review and suggestions for improvement.", "operationId": "verify_code_api_v1_ai_verify_code_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationRequestModel"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/install-dependencies": {"post": {"tags": ["ai", "AI"], "summary": "Install Dependencies", "description": "Install Python packages using the Shell Agent.\n\nThis endpoint safely installs packages in the current environment\nwith proper error handling and feedback.", "operationId": "install_dependencies_api_v1_ai_install_dependencies_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DependencyInstallRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai/agents": {"get": {"tags": ["ai", "AI"], "summary": "Get Agents", "description": "Get information about all available agents.", "operationId": "get_agents_api_v1_ai_agents_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Response Get Agents Api V1 Ai Agents Get"}}}}}}}, "/api/v1/ai/agents/health": {"get": {"tags": ["ai", "AI"], "summary": "Get Agent Health", "description": "Get health status for all agents and their models.", "operationId": "get_agent_health_api_v1_ai_agents_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Agent Health Api V1 Ai Agents Health Get"}}}}}}}, "/api/v1/ai/models": {"get": {"tags": ["ai", "AI"], "summary": "Get Models", "description": "Get information about available AI models.", "operationId": "get_models_api_v1_ai_models_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Response Get Models Api V1 Ai Models Get"}}}}}}}, "/api/v1/ai/stats": {"get": {"tags": ["ai", "AI"], "summary": "Get Orchestrator Stats", "description": "Get orchestrator statistics and performance metrics.", "operationId": "get_orchestrator_stats_api_v1_ai_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Orchestrator Stats Api V1 Ai Stats Get"}}}}}}}, "/api/v1/ai/health": {"get": {"tags": ["ai", "AI"], "summary": "Health Check", "description": "Check the health of the AI service.", "operationId": "health_check_api_v1_ai_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ltkb/documents": {"post": {"tags": ["ltkb", "LTKB"], "summary": "Upload Document", "description": "Upload a new document to LTKB.", "operationId": "upload_document_api_v1_ltkb_documents_post", "parameters": [{"name": "title", "in": "query", "required": true, "schema": {"type": "string", "description": "Document title", "title": "Title"}, "description": "Document title"}, {"name": "description", "in": "query", "required": true, "schema": {"type": "string", "description": "Document description", "title": "Description"}, "description": "Document description"}, {"name": "document_type", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/DocumentType", "description": "Document type"}, "description": "Document type"}, {"name": "author", "in": "query", "required": true, "schema": {"type": "string", "description": "Document author", "title": "Author"}, "description": "Document author"}, {"name": "tags", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Comma-separated tags", "title": "Tags"}, "description": "Comma-separated tags"}, {"name": "technology_stack", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Comma-separated technologies", "title": "Technology Stack"}, "description": "Comma-separated technologies"}, {"name": "complexity_level", "in": "query", "required": false, "schema": {"type": "string", "description": "Complexity level", "default": "intermediate", "title": "Complexity Level"}, "description": "Complexity level"}, {"name": "collection", "in": "query", "required": false, "schema": {"type": "string", "description": "Collection to store in", "default": "systems", "title": "Collection"}, "description": "Collection to store in"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_document_api_v1_ltkb_documents_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentMetadata"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["ltkb", "LTKB"], "summary": "List Documents", "description": "List documents with optional filtering.", "operationId": "list_documents_api_v1_ltkb_documents_get", "parameters": [{"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DocumentType"}, {"type": "null"}], "description": "Filter by document type", "title": "Document Type"}, "description": "Filter by document type"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DocumentStatus"}, {"type": "null"}], "description": "Filter by status", "title": "Status"}, "description": "Filter by status"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Maximum documents to return", "default": 50, "title": "Limit"}, "description": "Maximum documents to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of documents to skip", "default": 0, "title": "Offset"}, "description": "Number of documents to skip"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentMetadata"}, "title": "Response List Documents Api V1 Ltkb Documents Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ltkb/documents/{document_id}": {"get": {"tags": ["ltkb", "LTKB"], "summary": "Get Document", "description": "Retrieve a document by ID.", "operationId": "get_document_api_v1_ltkb_documents__document_id__get", "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "include_content", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Include document content", "default": false, "title": "Include Content"}, "description": "Include document content"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["ltkb", "LTKB"], "summary": "Update Document Metadata", "description": "Update document metadata.", "operationId": "update_document_metadata_api_v1_ltkb_documents__document_id__put", "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Updates"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentMetadata"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["ltkb", "LTKB"], "summary": "Delete Document", "description": "Delete a document from LTKB.", "operationId": "delete_document_api_v1_ltkb_documents__document_id__delete", "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ltkb/search": {"post": {"tags": ["ltkb", "LTKB"], "summary": "Search Documents", "description": "Search documents in LTKB.", "operationId": "search_documents_api_v1_ltkb_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentSearchQuery"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DocumentResponse"}, "type": "array", "title": "Response Search Documents Api V1 Ltkb Search Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ltkb/stats": {"get": {"tags": ["ltkb", "LTKB"], "summary": "Get Ltkb Stats", "description": "Get LTKB statistics.", "operationId": "get_ltkb_stats_api_v1_ltkb_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LTKBStats"}}}}}}}, "/api/v1/ltkb/backup": {"post": {"tags": ["ltkb", "LTKB"], "summary": "Create Backup", "description": "Create a backup of the vector database and LTKB data.", "operationId": "create_backup_api_v1_ltkb_backup_post", "parameters": [{"name": "backup_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Custom backup name", "title": "Backup Name"}, "description": "Custom backup name"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ltkb/backups": {"get": {"tags": ["ltkb", "LTKB"], "summary": "List Backups", "description": "List available backups.", "operationId": "list_backups_api_v1_ltkb_backups_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/ltkb/restore": {"post": {"tags": ["ltkb", "LTKB"], "summary": "Restore Backup", "description": "Restore from a backup file.", "operationId": "restore_backup_api_v1_ltkb_restore_post", "parameters": [{"name": "backup_path", "in": "query", "required": true, "schema": {"type": "string", "description": "Path to backup file", "title": "Backup Path"}, "description": "Path to backup file"}, {"name": "confirm", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Confirm restore operation", "default": false, "title": "Confirm"}, "description": "Confirm restore operation"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Create Project", "description": "Create a new project with optional roadmap.\n\n- **project_data**: Project information including optional roadmap\n- **Returns**: Created project with full details", "operationId": "create_project_api_v1_projects_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/projects/{project_id}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Project", "description": "Get project by ID with full roadmap hierarchy.\n\n- **project_id**: UUID of the project\n- **Returns**: Project with full roadmap data", "operationId": "get_project_api_v1_projects__project_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["roadmap", "roadmap"], "summary": "Update Project", "description": "Update project information.\n\n- **project_id**: UUID of the project\n- **update_data**: Fields to update\n- **Returns**: Updated project", "operationId": "update_project_api_v1_projects__project_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["roadmap", "roadmap"], "summary": "Delete Project", "description": "Delete project and all associated data.\n\n- **project_id**: UUID of the project", "operationId": "delete_project_api_v1_projects__project_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects/{project_id}/roadmap": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Create Roadmap", "description": "Create a roadmap for an existing project.\n\n- **project_id**: UUID of the project\n- **roadmap_data**: Roadmap with phases, steps, and tasks\n- **Returns**: Created roadmap with full hierarchy", "operationId": "create_roadmap_api_v1_projects__project_id__roadmap_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["roadmap", "roadmap"], "summary": "Get Project Roadmap", "description": "Get roadmap for a specific project.\n\n- **project_id**: UUID of the project\n- **Returns**: Project's roadmap with full hierarchy", "operationId": "get_project_roadmap_api_v1_projects__project_id__roadmap_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/roadmaps/{roadmap_id}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Roadmap", "description": "Get roadmap by ID with full hierarchy.\n\n- **roadmap_id**: UUID of the roadmap\n- **Returns**: Roadmap with all phases, steps, and tasks", "operationId": "get_roadmap_api_v1_roadmaps__roadmap_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "roadmap_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Roadmap Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["roadmap", "roadmap"], "summary": "Update Roadmap", "description": "Update roadmap information.\n\n- **roadmap_id**: UUID of the roadmap\n- **update_data**: Fields to update\n- **Returns**: Updated roadmap", "operationId": "update_roadmap_api_v1_roadmaps__roadmap_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "roadmap_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Roadmap Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/roadmap": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Create Standalone Roadmap", "description": "Create a standalone roadmap (auto-generates a project).\n\n- **roadmap_data**: Roadmap with phases, steps, and tasks\n- **Returns**: Created roadmap with full hierarchy and auto-generated project", "operationId": "create_standalone_roadmap_api_v1_roadmap_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/roadmap/{roadmap_id}": {"delete": {"tags": ["roadmap", "roadmap"], "summary": "Delete Roadmap", "description": "Delete roadmap and optionally its auto-generated project.\n\n- **roadmap_id**: UUID of the roadmap\n- **Note**: If the roadmap's project was auto-generated, it will also be deleted", "operationId": "delete_roadmap_api_v1_roadmap__roadmap_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "roadmap_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Roadmap Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}/status": {"patch": {"tags": ["roadmap", "roadmap"], "summary": "Update Task Status", "description": "Update task status and propagate changes up the hierarchy with audit logging.\n\n- **task_id**: UUID of the task\n- **status**: New task status\n- **artifacts**: Optional list of artifacts produced\n- **error_message**: Optional error message if task failed\n- **reason**: Optional reason for the status change (for audit trail)\n- **Returns**: Updated task with propagated status changes", "operationId": "update_task_status_api_v1_tasks__task_id__status_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}, {"name": "status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/TaskStatus"}}, {"name": "error_message", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}}, {"name": "reason", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "array", "items": {"type": "object", "additionalProperties": true}}, {"type": "null"}], "title": "Artifacts"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["roadmap", "roadmap"], "summary": "Get Task Status", "description": "Get detailed task status information with hierarchy context.\n\n- **task_id**: UUID of the task\n- **Returns**: Task status with step, phase, and roadmap context", "operationId": "get_task_status_api_v1_tasks__task_id__status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects/{project_id}/progress": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Project Progress", "description": "Get comprehensive project progress metrics.\n\n- **project_id**: UUID of the project\n- **Returns**: Detailed progress metrics and statistics", "operationId": "get_project_progress_api_v1_projects__project_id__progress_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects/{project_id}/tasks/by-agent/{agent_type}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Tasks By Agent", "description": "Get all tasks assigned to a specific agent in a project.\n\n- **project_id**: UUID of the project\n- **agent_type**: Agent type (architect, frontend, backend, shell, issue_fix, test)\n- **status**: Optional status filter\n- **Returns**: List of tasks assigned to the agent", "operationId": "get_tasks_by_agent_api_v1_projects__project_id__tasks_by_agent__agent_type__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "agent_type", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TaskStatus"}, {"type": "null"}], "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects/{project_id}/phases/{phase_id}/next-tasks": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Next Available Tasks", "description": "Get next available tasks in a phase (tasks with dependencies satisfied).\n\n- **project_id**: UUID of the project\n- **phase_id**: UUID of the phase\n- **Returns**: List of tasks ready to be executed", "operationId": "get_next_available_tasks_api_v1_projects__project_id__phases__phase_id__next_tasks_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "phase_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Phase Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/audit/logs": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Audit Logs", "description": "Get audit logs with filtering options.\n\n- **entity_type**: Filter by entity type (project, roadmap, phase, step, task)\n- **entity_id**: Filter by specific entity ID\n- **user_id**: Filter by user who made changes\n- **limit**: Maximum number of results (max 1000)\n- **offset**: Number of results to skip\n- **Returns**: List of audit log entries", "operationId": "get_audit_logs_api_v1_audit_logs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AuditEntityType"}, {"type": "null"}], "title": "Entity Type"}}, {"name": "entity_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entity Id"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}, "title": "Response Get Audit Logs Api V1 Audit Logs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/audit/{entity_type}/{entity_id}/trail": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Entity Audit Trail", "description": "Get complete audit trail for a specific entity.\n\n- **entity_type**: Type of entity (project, roadmap, phase, step, task)\n- **entity_id**: UUID of the entity\n- **limit**: Maximum number of results\n- **Returns**: Complete audit trail for the entity", "operationId": "get_entity_audit_trail_api_v1_audit__entity_type___entity_id__trail_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AuditEntityType"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}, "title": "Response Get Entity Audit Trail Api V1 Audit  Entity Type   Entity Id  Trail Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/audit/{entity_type}/{entity_id}/status-history": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Status History", "description": "Get status change history for an entity.\n\n- **entity_type**: Type of entity (project, roadmap, phase, step, task)\n- **entity_id**: UUID of the entity\n- **limit**: Maximum number of results\n- **Returns**: Status change history with duration tracking", "operationId": "get_status_history_api_v1_audit__entity_type___entity_id__status_history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AuditEntityType"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StatusHistoryResponse"}, "title": "Response Get Status History Api V1 Audit  Entity Type   Entity Id  Status History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/concurrency/{entity_type}/{entity_id}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Concurrency Control", "description": "Get concurrency control information for an entity.\n\n- **entity_type**: Type of entity (project, roadmap, phase, step, task)\n- **entity_id**: UUID of the entity\n- **Returns**: Concurrency control information including version and lock status", "operationId": "get_concurrency_control_api_v1_concurrency__entity_type___entity_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AuditEntityType"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConcurrencyControlResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/concurrency/{entity_type}/{entity_id}/lock": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Acquire Lock", "description": "Acquire a lock on an entity for editing.\n\n- **entity_type**: Type of entity (project, roadmap, phase, step, task)\n- **entity_id**: UUID of the entity\n- **lock_duration_minutes**: How long to hold the lock (default 30 minutes)\n- **Returns**: Success status and lock information", "operationId": "acquire_lock_api_v1_concurrency__entity_type___entity_id__lock_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AuditEntityType"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}, {"name": "lock_duration_minutes", "in": "query", "required": false, "schema": {"type": "integer", "default": 30, "title": "Lock Duration Minutes"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["roadmap", "roadmap"], "summary": "Release Lock", "description": "Release a lock on an entity.\n\n- **entity_type**: Type of entity (project, roadmap, phase, step, task)\n- **entity_id**: UUID of the entity\n- **Returns**: Updated concurrency control information", "operationId": "release_lock_api_v1_concurrency__entity_type___entity_id__lock_delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AuditEntityType"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/audit/{entity_type}/{entity_id}/analytics": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Status Analytics", "description": "Get comprehensive status analytics for an entity.\n\n- **entity_type**: Type of entity (project, roadmap, phase, step, task)\n- **entity_id**: UUID of the entity\n- **Returns**: Detailed analytics including status distribution, timing, and trends", "operationId": "get_status_analytics_api_v1_audit__entity_type___entity_id__analytics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AuditEntityType"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/roadmaps/{roadmap_id}/versions": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Create Roadmap Version", "description": "Create a new version of a roadmap.\n\n- **roadmap_id**: UUID of the roadmap\n- **version_data**: Version creation data including type and change details\n- **Returns**: Created version with snapshot and metadata", "operationId": "create_roadmap_version_api_v1_roadmaps__roadmap_id__versions_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "roadmap_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Roadmap Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapVersionCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapVersionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["roadmap", "roadmap"], "summary": "Get Roadmap Versions", "description": "Get all versions for a roadmap.\n\n- **roadmap_id**: UUID of the roadmap\n- **limit**: Maximum number of versions to return\n- **Returns**: List of roadmap versions ordered by creation date", "operationId": "get_roadmap_versions_api_v1_roadmaps__roadmap_id__versions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "roadmap_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Roadmap Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoadmapVersionResponse"}, "title": "Response Get Roadmap Versions Api V1 Roadmaps  Roadmap Id  Versions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/versions/{version_id}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Roadmap Version", "description": "Get a specific roadmap version by ID.\n\n- **version_id**: UUID of the version\n- **Returns**: Version details with complete snapshot", "operationId": "get_roadmap_version_api_v1_versions__version_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "version_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Version Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadmapVersionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/versions/{version_id}/release": {"patch": {"tags": ["roadmap", "roadmap"], "summary": "Release Roadmap Version", "description": "Mark a roadmap version as released.\n\n- **version_id**: UUID of the version\n- **release_notes**: Optional release notes\n- **Returns**: Updated version with release information", "operationId": "release_roadmap_version_api_v1_versions__version_id__release_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "version_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Version Id"}}, {"name": "release_notes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Release Notes"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/versions/{from_version_id}/compare/{to_version_id}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Compare Roadmap Versions", "description": "Compare two roadmap versions and return differences.\n\n- **from_version_id**: UUID of the source version\n- **to_version_id**: UUID of the target version\n- **Returns**: Detailed comparison showing changes, additions, and removals", "operationId": "compare_roadmap_versions_api_v1_versions__from_version_id__compare__to_version_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "from_version_id", "in": "path", "required": true, "schema": {"type": "string", "title": "From Version Id"}}, {"name": "to_version_id", "in": "path", "required": true, "schema": {"type": "string", "title": "To Version Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionComparison"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/projects/{project_id}/analytics": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Project Analytics", "description": "Get comprehensive analytics for a project including all entities.\n\n- **project_id**: UUID of the project\n- **Returns**: Complete project analytics with status, timing, and progress data", "operationId": "get_project_analytics_api_v1_projects__project_id__analytics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/bulk-operations/status-update": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Bulk Status Update", "description": "Update status for multiple tasks in a single operation.\n\n- **updates**: List of updates with task_id and new status\n- **reason**: Optional reason for the bulk update\n- **Returns**: Results of all updates with success/failure status", "operationId": "bulk_status_update_api_v1_bulk_operations_status_update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "reason", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": true}, "title": "Updates"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/validate/roadmap": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Validate Roadmap Schema", "description": "Validate roadmap data against the roadmap.json schema.\n\n- **roadmap_data**: Complete roadmap data to validate\n- **Returns**: Validation results with any errors found", "operationId": "validate_roadmap_schema_api_v1_validate_roadmap_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Roadmap Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/schema/roadmap/info": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Roadmap Schema Info", "description": "Get information about the roadmap JSON schema.\n\n- **Returns**: Schema metadata and structure information", "operationId": "get_roadmap_schema_info_api_v1_schema_roadmap_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/schema/list": {"get": {"tags": ["roadmap", "roadmap"], "summary": "List Available Schemas", "description": "List all available JSON schemas.\n\n- **Returns**: List of available schema names", "operationId": "list_available_schemas_api_v1_schema_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/tasks/{task_id}/dependencies": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Check Task Dependencies", "description": "Check task dependencies and determine if task can start.\n\n- **task_id**: UUID of the task\n- **Returns**: Dependency check result with blocking dependencies and warnings", "operationId": "check_task_dependencies_api_v1_tasks__task_id__dependencies_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/steps/{step_id}/dependencies": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Check Step Dependencies", "description": "Check step dependencies and determine if step can start.\n\n- **step_id**: UUID of the step\n- **Returns**: Dependency check result with blocking dependencies and warnings", "operationId": "check_step_dependencies_api_v1_steps__step_id__dependencies_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "step_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Step Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/phases/{phase_id}/dependencies": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Check Phase Dependencies", "description": "Check phase dependencies and determine if phase can start.\n\n- **phase_id**: UUID of the phase\n- **Returns**: Dependency check result with blocking dependencies and warnings", "operationId": "check_phase_dependencies_api_v1_phases__phase_id__dependencies_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "phase_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Phase Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/phases/{phase_id}/progression": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Check Phase Progression", "description": "Check phase progression status and completion.\n\n- **phase_id**: UUID of the phase\n- **Returns**: Phase progression result with completion statistics", "operationId": "check_phase_progression_api_v1_phases__phase_id__progression_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "phase_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Phase Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}/start": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Start Task With Validation", "description": "Start a task with dependency validation.\n\n- **task_id**: UUID of the task\n- **force_override**: Whether to force start despite dependencies\n- **override_reason**: Reason for override if force_override is True\n- **Returns**: Updated task with validation results", "operationId": "start_task_with_validation_api_v1_tasks__task_id__start_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}, {"name": "force_override", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force Override"}}, {"name": "override_reason", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Override Reason"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_id}/complete": {"post": {"tags": ["roadmap", "roadmap"], "summary": "Complete Task With Validation", "description": "Complete a task with dependency validation.\n\n- **task_id**: UUID of the task\n- **artifacts**: Optional list of artifacts produced\n- **force_override**: Whether to force completion despite dependencies\n- **override_reason**: Reason for override if force_override is True\n- **Returns**: Updated task with validation results", "operationId": "complete_task_with_validation_api_v1_tasks__task_id__complete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}, {"name": "force_override", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force Override"}}, {"name": "override_reason", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Override Reason"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "array", "items": {"type": "object", "additionalProperties": true}}, {"type": "null"}], "title": "Artifacts"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/validate-execution-order/{entity_type}/{entity_id}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Validate Execution Order", "description": "Validate execution order for an entity (task, step, or phase).\n\n- **entity_type**: Type of entity (task, step, phase)\n- **entity_id**: UUID of the entity\n- **Returns**: Validation result with warnings", "operationId": "validate_execution_order_api_v1_validate_execution_order__entity_type___entity_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Type"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/roadmaps/{roadmap_id}/real-time-status": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Real Time Status", "description": "Get real-time status summary for a roadmap.\n\n- **roadmap_id**: UUID of the roadmap\n- **Returns**: Real-time status summary with progress and task details", "operationId": "get_real_time_status_api_v1_roadmaps__roadmap_id__real_time_status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "roadmap_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Roadmap Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/status-impact/{entity_type}/{entity_id}": {"get": {"tags": ["roadmap", "roadmap"], "summary": "Get Status Change Impact", "description": "Analyze the potential impact of a status change.\n\n- **entity_type**: Type of entity (task, step, phase)\n- **entity_id**: UUID of the entity\n- **new_status**: The new status to analyze\n- **Returns**: Impact analysis with affected entities and automatic progressions", "operationId": "get_status_change_impact_api_v1_status_impact__entity_type___entity_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Type"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Entity Id"}}, {"name": "new_status", "in": "query", "required": true, "schema": {"type": "string", "title": "New Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/admin/auth/check": {"get": {"tags": ["admin", "admin"], "summary": "<PERSON> <PERSON><PERSON>", "description": "Check if current user has admin privileges.\n\nThis endpoint is used by the frontend to verify admin access.", "operationId": "check_admin_auth_api_v1_admin_auth_check_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/models/available": {"get": {"tags": ["admin", "admin"], "summary": "Get Available Models", "description": "Get all available models from local and cloud providers.", "operationId": "get_available_models_api_v1_admin_models_available_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"$ref": "#/components/schemas/ModelProvider"}, "type": "object", "title": "Response Get Available Models Api V1 Admin Models Available Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/models/current-config": {"get": {"tags": ["admin", "admin"], "summary": "Get Current Model Config", "description": "Get current model configuration for all agents.", "operationId": "get_current_model_config_api_v1_admin_models_current_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/models/update-config": {"post": {"tags": ["admin", "admin"], "summary": "Update Model Config", "description": "Update model configuration for agents.", "operationId": "update_model_config_api_v1_admin_models_update_config_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModelConfigUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/providers/cloud/configure": {"post": {"tags": ["admin", "admin"], "summary": "Configure Cloud Provider", "description": "Configure a cloud provider with API key (securely encrypted).", "operationId": "configure_cloud_provider_api_v1_admin_providers_cloud_configure_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CloudProviderConfig"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/system/health": {"get": {"tags": ["admin", "admin"], "summary": "Get System Health", "description": "Get overall system health including model availability.", "operationId": "get_system_health_api_v1_admin_system_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/models/test-connection": {"post": {"tags": ["admin", "admin"], "summary": "Test Model Connection", "description": "Test connection to a specific model.", "operationId": "test_model_connection_api_v1_admin_models_test_connection_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "model_name", "in": "query", "required": true, "schema": {"type": "string", "title": "Model Name"}}, {"name": "provider", "in": "query", "required": false, "schema": {"type": "string", "default": "ollama", "title": "Provider"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/admin/security/rule-violations": {"get": {"tags": ["admin", "admin"], "summary": "Get Rule Violations", "description": "Get summary of rule violations for security monitoring.", "operationId": "get_rule_violations_api_v1_admin_security_rule_violations_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/models/cloud/available": {"get": {"tags": ["admin", "admin"], "summary": "Get Available Cloud Models", "description": "Get available models from all providers (admin only).", "operationId": "get_available_cloud_models_api_v1_admin_models_cloud_available_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/models/cloud/routing/update": {"post": {"tags": ["admin", "admin"], "summary": "Update Cloud Model Routing", "description": "Update model routing to use cloud models (admin only).", "operationId": "update_cloud_model_routing_api_v1_admin_models_cloud_routing_update_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Routing Config"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/containers/provision": {"post": {"tags": ["containers", "containers"], "summary": "Provision User Container", "description": "Provision an isolated container environment for the current user.\n\nCreates a new container with the specified project type and proper\nsecurity isolation, resource limits, and networking.", "operationId": "provision_user_container_api_v1_containers_provision_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContainerProvisionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContainerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}, {"HTTPBearer": []}]}}, "/api/v1/containers/status": {"get": {"tags": ["containers", "containers"], "summary": "Get Container Status", "description": "Get the current status of the user's container.\n\nReturns container information if it exists, or None if no container\nis currently provisioned for the user.", "operationId": "get_container_status_api_v1_containers_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/ContainerResponse"}, {"type": "null"}], "title": "Response Get Container Status Api V1 Containers Status Get"}}}}}, "security": [{"HTTPBearer": []}, {"HTTPBearer": []}]}}, "/api/v1/containers/execute": {"post": {"tags": ["containers", "containers"], "summary": "Execute Command", "description": "Execute a command in the user's container.\n\nRuns the specified command in the user's isolated container environment\nwith proper security restrictions and returns the execution results.", "operationId": "execute_command_api_v1_containers_execute_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContainerExecuteRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecutionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}, {"HTTPBearer": []}]}}, "/api/v1/containers/cleanup": {"delete": {"tags": ["containers", "containers"], "summary": "Cleanup User Container", "description": "Clean up the user's container and associated resources.\n\nStops and removes the user's container while preserving data volumes\nfor future use. This is useful for freeing up resources when the\ncontainer is no longer needed.", "operationId": "cleanup_user_container_api_v1_containers_cleanup_delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}, {"HTTPBearer": []}]}}, "/api/v1/containers/admin/list": {"get": {"tags": ["containers", "containers"], "summary": "List All Containers", "description": "List all active user containers (admin only).\n\nReturns information about all currently active user containers\nfor monitoring and management purposes.", "operationId": "list_all_containers_api_v1_containers_admin_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContainerListResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/containers/admin/cleanup-inactive": {"post": {"tags": ["containers", "containers"], "summary": "Cleanup Inactive Containers", "description": "Clean up inactive containers (admin only).\n\nRemoves containers that have been inactive for longer than the\nspecified time period to free up system resources.", "operationId": "cleanup_inactive_containers_api_v1_containers_admin_cleanup_inactive_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "max_idle_hours", "in": "query", "required": false, "schema": {"type": "integer", "default": 24, "title": "Max Idle Hours"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/containers/health": {"get": {"tags": ["containers", "containers"], "summary": "Container Service Health", "description": "Check the health of the container management service.\n\nVerifies that the Docker daemon is accessible and the container\nmanagement service is functioning properly.", "operationId": "container_service_health_api_v1_containers_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AgentCapability": {"type": "string", "enum": ["project_planning", "roadmap_creation", "agent_orchestration", "requirements_analysis", "ui_component_generation", "responsive_design", "react_development", "css_styling", "api_development", "database_design", "authentication", "business_logic", "command_execution", "system_administration", "file_operations", "deployment", "error_detection", "bug_fixing", "code_optimization", "performance_analysis", "unit_testing", "integration_testing", "test_strategy", "test_automation"], "title": "AgentCapability", "description": "Enumeration of agent capabilities."}, "AgentModelConfig": {"properties": {"agent_name": {"type": "string", "title": "Agent Name"}, "primary_model": {"type": "string", "title": "Primary Model"}, "secondary_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Secondary Model"}, "fallback_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fallback Model"}, "provider": {"type": "string", "title": "Provider"}}, "type": "object", "required": ["agent_name", "primary_model", "provider"], "title": "AgentModelConfig", "description": "Agent model assignment configuration."}, "AgentRole": {"type": "string", "enum": ["architect", "frontend", "backend", "shell", "debug", "test"], "title": "AgentRole", "description": "Enumeration of available AI agent roles."}, "AgentType": {"type": "string", "enum": ["architect", "frontend", "backend", "shell", "issue_fix", "test"], "title": "AgentType", "description": "Available agent types for task assignment."}, "AuditAction": {"type": "string", "enum": ["create", "update", "delete", "status_change", "assign_agent", "add_artifact", "dependency_change", "version_change"], "title": "AuditAction", "description": "Types of audit actions."}, "AuditEntityType": {"type": "string", "enum": ["project", "roadmap", "phase", "step", "task"], "title": "AuditEntityType", "description": "Types of entities that can be audited."}, "AuditLogResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "entity_type": {"$ref": "#/components/schemas/AuditEntityType"}, "entity_id": {"type": "string", "title": "Entity Id"}, "action": {"$ref": "#/components/schemas/AuditAction"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "user_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Email"}, "old_values": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Old Values"}, "new_values": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "New Values"}, "changed_fields": {"items": {"type": "string"}, "type": "array", "title": "Changed Fields"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "ip_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ip Address"}, "user_agent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Agent"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "audit_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON>t Metada<PERSON>"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "entity_type", "entity_id", "action", "created_at"], "title": "AuditLogResponse", "description": "Schema for audit log responses."}, "Body_upload_document_api_v1_ltkb_documents_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_document_api_v1_ltkb_documents_post"}, "ChatRequestModel": {"properties": {"message": {"type": "string", "title": "Message"}, "agent_role": {"anyOf": [{"$ref": "#/components/schemas/AgentRole"}, {"type": "null"}]}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conversation Id"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "stream": {"type": "boolean", "title": "Stream", "default": false}, "max_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature"}}, "type": "object", "required": ["message"], "title": "ChatRequestModel", "description": "Request model for chat completion."}, "ChatResponseModel": {"properties": {"response": {"type": "string", "title": "Response"}, "agent_role": {"anyOf": [{"$ref": "#/components/schemas/AgentRole"}, {"type": "null"}]}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conversation Id"}, "model_used": {"type": "string", "title": "Model Used"}, "tokens_used": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tokens Used"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["response", "model_used"], "title": "ChatResponseModel", "description": "Response model for chat completion."}, "CloudProviderConfig": {"properties": {"provider_name": {"type": "string", "pattern": "^(openai|anthropic|google)$", "title": "Provider Name"}, "api_key": {"type": "string", "maxLength": 200, "minLength": 10, "title": "Api Key"}, "base_url": {"anyOf": [{"type": "string", "pattern": "^https?://.*"}, {"type": "null"}], "title": "Base Url"}, "enabled": {"type": "boolean", "title": "Enabled", "default": true}}, "type": "object", "required": ["provider_name", "api_key"], "title": "CloudProviderConfig", "description": "Cloud provider configuration with validation."}, "CollaborationRequestModel": {"properties": {"task_description": {"type": "string", "title": "Task Description"}, "primary_agent": {"$ref": "#/components/schemas/AgentRole"}, "supporting_agents": {"items": {"$ref": "#/components/schemas/AgentRole"}, "type": "array", "title": "Supporting Agents"}, "coordination_strategy": {"type": "string", "title": "Coordination Strategy", "default": "sequential"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}}, "type": "object", "required": ["task_description", "primary_agent"], "title": "CollaborationRequestModel", "description": "Request model for multi-agent collaboration."}, "ConcurrencyControlResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "entity_type": {"$ref": "#/components/schemas/AuditEntityType"}, "entity_id": {"type": "string", "title": "Entity Id"}, "version": {"type": "integer", "title": "Version"}, "last_modified_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Modified By"}, "last_modified_at": {"type": "string", "format": "date-time", "title": "Last Modified At"}, "is_locked": {"type": "boolean", "title": "<PERSON>"}, "locked_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Locked By"}, "locked_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Locked At"}, "lock_expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Lock Expires At"}, "has_conflicts": {"type": "boolean", "title": "Has Conflicts"}, "conflict_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Conflict Data"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "entity_type", "entity_id", "version", "last_modified_at", "is_locked", "has_conflicts", "created_at", "updated_at"], "title": "ConcurrencyControlResponse", "description": "Schema for concurrency control responses."}, "ContainerExecuteRequest": {"properties": {"command": {"type": "string", "title": "Command", "description": "Command to execute"}, "working_dir": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Working Dir", "description": "Working directory"}}, "type": "object", "required": ["command"], "title": "ContainerExecuteRequest", "description": "Request model for command execution."}, "ContainerListResponse": {"properties": {"containers": {"items": {"$ref": "#/components/schemas/ContainerResponse"}, "type": "array", "title": "Containers"}, "total_count": {"type": "integer", "title": "Total Count"}}, "type": "object", "required": ["containers", "total_count"], "title": "ContainerListResponse", "description": "Response model for container listing."}, "ContainerProvisionRequest": {"properties": {"project_type": {"$ref": "#/components/schemas/ProjectType"}, "project_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Project Name", "description": "Optional project name"}}, "type": "object", "required": ["project_type"], "title": "ContainerProvisionRequest", "description": "Request model for container provisioning."}, "ContainerResponse": {"properties": {"user_id": {"type": "string", "title": "User Id"}, "container_id": {"type": "string", "title": "Container Id"}, "container_name": {"type": "string", "title": "Container Name"}, "project_type": {"$ref": "#/components/schemas/ProjectType"}, "status": {"$ref": "#/components/schemas/ContainerStatus"}, "port": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Port"}, "preview_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preview Url"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "last_accessed": {"type": "string", "format": "date-time", "title": "Last Accessed"}, "resource_limits": {"additionalProperties": true, "type": "object", "title": "Resource Limits"}}, "type": "object", "required": ["user_id", "container_id", "container_name", "project_type", "status", "port", "preview_url", "created_at", "last_accessed", "resource_limits"], "title": "ContainerResponse", "description": "Response model for container information."}, "ContainerStatus": {"type": "string", "enum": ["creating", "running", "stopped", "error", "removing"], "title": "ContainerStatus", "description": "Container status states."}, "DependencyInstallRequest": {"properties": {"packages": {"items": {"type": "string"}, "type": "array", "title": "Packages"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}}, "type": "object", "required": ["packages"], "title": "DependencyInstallRequest", "description": "Request model for dependency installation."}, "DocumentMetadata": {"properties": {"id": {"type": "string", "title": "Id"}, "title": {"type": "string", "title": "Title", "description": "Document title"}, "description": {"type": "string", "title": "Description", "description": "Document description"}, "document_type": {"$ref": "#/components/schemas/DocumentType", "description": "Type of document"}, "status": {"$ref": "#/components/schemas/DocumentStatus", "default": "active"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Document tags"}, "technology_stack": {"items": {"type": "string"}, "type": "array", "title": "Technology Stack", "description": "Related technologies"}, "use_cases": {"items": {"type": "string"}, "type": "array", "title": "Use Cases", "description": "Applicable use cases"}, "complexity_level": {"type": "string", "title": "Complexity Level", "description": "beginner|intermediate|advanced|expert", "default": "intermediate"}, "file_path": {"type": "string", "title": "File Path", "description": "Relative path to the document file"}, "file_size": {"type": "integer", "title": "File Size", "description": "File size in bytes"}, "file_format": {"type": "string", "title": "File Format", "description": "File format/extension"}, "version": {"type": "string", "title": "Version", "description": "Document version", "default": "1.0.0"}, "author": {"type": "string", "title": "Author", "description": "Document author"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "usage_count": {"type": "integer", "title": "Usage Count", "description": "Number of times used", "default": 0}, "last_used": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used", "description": "Last usage timestamp"}, "dependencies": {"items": {"type": "string"}, "type": "array", "title": "Dependencies", "description": "Document IDs this depends on"}, "related_documents": {"items": {"type": "string"}, "type": "array", "title": "Related Documents", "description": "Related document IDs"}}, "type": "object", "required": ["title", "description", "document_type", "file_path", "file_size", "file_format", "author"], "title": "DocumentMetadata", "description": "Metadata for LTKB documents."}, "DocumentResponse": {"properties": {"metadata": {"$ref": "#/components/schemas/DocumentMetadata"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "Document content if requested"}, "relevance_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Relevance Score", "description": "Search relevance score"}}, "type": "object", "required": ["metadata"], "title": "DocumentResponse", "description": "Response model for document operations."}, "DocumentSearchQuery": {"properties": {"query": {"type": "string", "title": "Query", "description": "Search query text"}, "document_types": {"anyOf": [{"items": {"$ref": "#/components/schemas/DocumentType"}, "type": "array"}, {"type": "null"}], "title": "Document Types", "description": "Filter by document types"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "Filter by tags"}, "technology_stack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Technology Stack", "description": "Filter by technologies"}, "complexity_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Complexity Level", "description": "Filter by complexity"}, "limit": {"type": "integer", "maximum": 100.0, "minimum": 1.0, "title": "Limit", "description": "Maximum results to return", "default": 20}, "include_content": {"type": "boolean", "title": "Include Content", "description": "Include document content in results", "default": false}}, "type": "object", "required": ["query"], "title": "DocumentSearchQuery", "description": "Search query for LTKB documents."}, "DocumentStatus": {"type": "string", "enum": ["active", "archived", "deprecated", "draft"], "title": "DocumentStatus", "description": "Status of documents in LTKB."}, "DocumentType": {"type": "string", "enum": ["template", "pattern", "component", "workflow", "documentation", "example", "configuration"], "title": "DocumentType", "description": "Types of documents in LTKB."}, "EnhancedTaskRequestModel": {"properties": {"task_description": {"type": "string", "title": "Task Description"}, "preferred_agent": {"anyOf": [{"$ref": "#/components/schemas/AgentRole"}, {"type": "null"}]}, "required_capabilities": {"items": {"$ref": "#/components/schemas/AgentCapability"}, "type": "array", "title": "Required Capabilities"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conversation Id"}, "verify_output": {"type": "boolean", "title": "Verify Output", "default": true}, "auto_fix": {"type": "boolean", "title": "Auto Fix", "default": true}}, "type": "object", "required": ["task_description"], "title": "EnhancedTaskRequestModel", "description": "Enhanced request model for task execution with verification and auto-fix."}, "ExecutionResponse": {"properties": {"exit_code": {"type": "integer", "title": "Exit Code"}, "output": {"type": "string", "title": "Output"}, "success": {"type": "boolean", "title": "Success"}, "executed_at": {"type": "string", "format": "date-time", "title": "Executed At"}}, "type": "object", "required": ["exit_code", "output", "success", "executed_at"], "title": "ExecutionResponse", "description": "Response model for command execution."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthResponse": {"properties": {"status": {"type": "string", "title": "Status"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "version": {"type": "string", "title": "Version"}, "environment": {"type": "string", "title": "Environment"}, "uptime": {"type": "number", "title": "Uptime"}, "services": {"additionalProperties": true, "type": "object", "title": "Services"}}, "type": "object", "required": ["status", "timestamp", "version", "environment", "uptime", "services"], "title": "HealthResponse", "description": "Health check response model."}, "LTKBStats": {"properties": {"total_documents": {"type": "integer", "title": "Total Documents"}, "documents_by_type": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Documents By Type"}, "total_size": {"type": "integer", "title": "Total Size"}, "most_used_documents": {"items": {"$ref": "#/components/schemas/DocumentMetadata"}, "type": "array", "title": "Most Used Documents"}, "recent_additions": {"items": {"$ref": "#/components/schemas/DocumentMetadata"}, "type": "array", "title": "Recent Additions"}, "technology_distribution": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Technology Distribution"}}, "type": "object", "required": ["total_documents", "documents_by_type", "total_size", "most_used_documents", "recent_additions", "technology_distribution"], "title": "LTKBStats", "description": "Statistics about the LTKB."}, "ModelConfigUpdate": {"properties": {"agent_configs": {"items": {"$ref": "#/components/schemas/AgentModelConfig"}, "type": "array", "title": "Agent Configs"}, "provider_configs": {"additionalProperties": {"additionalProperties": true, "type": "object"}, "type": "object", "title": "Provider Configs"}}, "type": "object", "required": ["agent_configs", "provider_configs"], "title": "ModelConfigUpdate", "description": "Model configuration update request."}, "ModelProvider": {"properties": {"name": {"type": "string", "title": "Name"}, "type": {"type": "string", "title": "Type"}, "host": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Host"}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "models": {"items": {"type": "string"}, "type": "array", "title": "Models", "default": []}, "status": {"type": "string", "title": "Status", "default": "unknown"}}, "type": "object", "required": ["name", "type"], "title": "Model<PERSON>rovider", "description": "Model provider configuration."}, "PhaseCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Phase name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "dependencies": {"items": {"type": "string"}, "type": "array", "title": "Dependencies"}, "estimated_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Duration"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "order_index": {"type": "integer", "minimum": 0.0, "title": "Order Index", "description": "Phase order within roadmap"}, "steps": {"items": {"$ref": "#/components/schemas/StepCreate"}, "type": "array", "title": "Steps"}}, "type": "object", "required": ["name", "order_index"], "title": "PhaseCreate", "description": "Schema for creating phases."}, "PhaseResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "Phase name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "dependencies": {"items": {"type": "string"}, "type": "array", "title": "Dependencies"}, "estimated_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Duration"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "id": {"type": "string", "title": "Id"}, "order_index": {"type": "integer", "title": "Order Index"}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "steps": {"items": {"$ref": "#/components/schemas/StepResponse"}, "type": "array", "title": "Steps"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "id", "order_index", "status", "created_at", "updated_at"], "title": "PhaseResponse", "description": "Schema for phase responses."}, "ProjectCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Project name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "tech_stack": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Tech Stack"}, "project_rules": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Rules"}, "roadmap": {"anyOf": [{"$ref": "#/components/schemas/RoadmapCreate"}, {"type": "null"}]}}, "type": "object", "required": ["name"], "title": "ProjectCreate", "description": "<PERSON><PERSON><PERSON> for creating projects."}, "ProjectResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "Project name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "tech_stack": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Tech Stack"}, "project_rules": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Rules"}, "id": {"type": "string", "title": "Id"}, "roadmap": {"anyOf": [{"$ref": "#/components/schemas/RoadmapResponse"}, {"type": "null"}]}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "id", "created_at", "updated_at"], "title": "ProjectResponse", "description": "Schema for project responses."}, "ProjectType": {"type": "string", "enum": ["react", "nextjs", "python", "static", "nodejs"], "title": "ProjectType", "description": "Supported project types for user containers."}, "ProjectUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "tech_stack": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Tech Stack"}, "project_rules": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Rules"}}, "type": "object", "title": "ProjectUpdate", "description": "Schema for updating projects."}, "RoadmapCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Roadmap name", "default": "Project Roadmap"}, "version": {"type": "string", "title": "Version", "description": "Roadmap version", "default": "1.0.0"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "phases": {"items": {"$ref": "#/components/schemas/PhaseCreate"}, "type": "array", "title": "Phases"}}, "type": "object", "title": "RoadmapCreate", "description": "Schema for creating roadmaps."}, "RoadmapResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "Roadmap name", "default": "Project Roadmap"}, "version": {"type": "string", "title": "Version", "description": "Roadmap version", "default": "1.0.0"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "id": {"type": "string", "title": "Id"}, "project_id": {"type": "string", "title": "Project Id"}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "phases": {"items": {"$ref": "#/components/schemas/PhaseResponse"}, "type": "array", "title": "Phases"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "project_id", "status", "created_at", "updated_at"], "title": "RoadmapResponse", "description": "Schema for roadmap responses."}, "RoadmapUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version"}, "status": {"anyOf": [{"$ref": "#/components/schemas/TaskStatus"}, {"type": "null"}]}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}}, "type": "object", "title": "RoadmapUpdate", "description": "Schema for updating roadmaps."}, "RoadmapVersionCreate": {"properties": {"version_type": {"type": "string", "enum": ["major", "minor", "patch"], "title": "Version Type", "default": "minor"}, "change_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Change Summary"}, "change_details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Change Details"}, "creation_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creation Reason"}, "auto_increment": {"type": "boolean", "title": "Auto Increment", "default": true}}, "type": "object", "title": "RoadmapVersionCreate", "description": "Schema for creating roadmap versions."}, "RoadmapVersionResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "roadmap_id": {"type": "string", "title": "Roadmap Id"}, "version_number": {"type": "string", "title": "Version Number"}, "major_version": {"type": "integer", "title": "Major Version"}, "minor_version": {"type": "integer", "title": "Minor Version"}, "patch_version": {"type": "integer", "title": "Patch Version"}, "version_type": {"type": "string", "title": "Version Type"}, "change_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Change Summary"}, "change_details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Change Details"}, "roadmap_snapshot": {"additionalProperties": true, "type": "object", "title": "Roadmap Snapshot"}, "created_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Created By"}, "created_by_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Created By Email"}, "creation_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creation Reason"}, "is_released": {"type": "boolean", "title": "Is Released"}, "released_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Released At"}, "released_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Released By"}, "release_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Release Notes"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "roadmap_id", "version_number", "major_version", "minor_version", "patch_version", "version_type", "roadmap_snapshot", "is_released", "created_at"], "title": "RoadmapVersionResponse", "description": "Schema for roadmap version responses."}, "StatusHistoryResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "entity_type": {"$ref": "#/components/schemas/AuditEntityType"}, "entity_id": {"type": "string", "title": "Entity Id"}, "old_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Old Status"}, "new_status": {"type": "string", "title": "New Status"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "triggered_by": {"type": "string", "title": "Triggered By"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "duration_in_previous_status": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration In Previous Status"}, "status_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Status Metadata"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "entity_type", "entity_id", "new_status", "triggered_by", "created_at"], "title": "StatusHistoryResponse", "description": "Schema for status history responses."}, "StepCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Step name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "dependencies": {"items": {"type": "string"}, "type": "array", "title": "Dependencies"}, "estimated_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Duration"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "order_index": {"type": "integer", "minimum": 0.0, "title": "Order Index", "description": "Step order within phase"}, "tasks": {"items": {"$ref": "#/components/schemas/TaskCreate"}, "type": "array", "title": "Tasks"}}, "type": "object", "required": ["name", "order_index"], "title": "StepCreate", "description": "Schema for creating steps."}, "StepResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "Step name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "dependencies": {"items": {"type": "string"}, "type": "array", "title": "Dependencies"}, "estimated_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Duration"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "id": {"type": "string", "title": "Id"}, "order_index": {"type": "integer", "title": "Order Index"}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "tasks": {"items": {"$ref": "#/components/schemas/TaskResponse"}, "type": "array", "title": "Tasks"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "id", "order_index", "status", "created_at", "updated_at"], "title": "StepResponse", "description": "Schema for step responses."}, "TaskArtifact": {"properties": {"type": {"type": "string", "enum": ["code", "config", "documentation", "schema", "test"], "title": "Type"}, "filename": {"type": "string", "title": "Filename"}, "content": {"type": "string", "title": "Content"}, "description": {"type": "string", "title": "Description"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}}, "type": "object", "required": ["type", "filename", "content", "description"], "title": "TaskArtifact", "description": "Artifact produced by task execution."}, "TaskCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Task name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "assigned_agent": {"$ref": "#/components/schemas/AgentType"}, "dependencies": {"items": {"type": "string"}, "type": "array", "title": "Dependencies"}, "estimated_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Duration"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "order_index": {"type": "integer", "minimum": 0.0, "title": "Order Index", "description": "Task order within step"}}, "type": "object", "required": ["name", "assigned_agent", "order_index"], "title": "TaskCreate", "description": "<PERSON><PERSON>a for creating tasks."}, "TaskRequestModel": {"properties": {"task_description": {"type": "string", "title": "Task Description"}, "preferred_agent": {"anyOf": [{"$ref": "#/components/schemas/AgentRole"}, {"type": "null"}]}, "required_capabilities": {"items": {"$ref": "#/components/schemas/AgentCapability"}, "type": "array", "title": "Required Capabilities"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conversation Id"}}, "type": "object", "required": ["task_description"], "title": "TaskRequestModel", "description": "Request model for task execution."}, "TaskResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "Task name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "assigned_agent": {"$ref": "#/components/schemas/AgentType"}, "dependencies": {"items": {"type": "string"}, "type": "array", "title": "Dependencies"}, "estimated_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Duration"}, "project_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Project Metadata"}, "id": {"type": "string", "title": "Id"}, "order_index": {"type": "integer", "title": "Order Index"}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "artifacts": {"items": {"$ref": "#/components/schemas/TaskArtifact"}, "type": "array", "title": "Artifacts"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "assigned_agent", "id", "order_index", "status", "created_at", "updated_at"], "title": "TaskResponse", "description": "Schema for task responses."}, "TaskResult": {"properties": {"task_id": {"type": "string", "title": "Task Id"}, "agent_role": {"$ref": "#/components/schemas/AgentRole"}, "success": {"type": "boolean", "title": "Success"}, "result": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "execution_time_ms": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Execution Time Ms"}, "verification_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verification Status"}, "dependencies_installed": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Dependencies Installed"}}, "type": "object", "required": ["task_id", "agent_role", "success"], "title": "TaskResult", "description": "Result of task execution."}, "TaskStatus": {"type": "string", "enum": ["pending", "in_progress", "completed", "failed", "blocked", "skipped"], "title": "TaskStatus", "description": "Task execution status."}, "UserCreate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "username": {"type": "string", "maxLength": 50, "minLength": 3, "pattern": "^[a-zA-Z0-9_-]+$", "title": "Username"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Full Name"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "password": {"type": "string", "maxLength": 100, "minLength": 8, "title": "Password"}}, "type": "object", "required": ["email", "username", "password"], "title": "UserCreate", "description": "Schema for user creation with password validation.", "example": {"email": "<EMAIL>", "full_name": "<PERSON>", "password": "secure_password123", "username": "johndoe"}}, "UserLogin": {"properties": {"username": {"type": "string", "title": "Username", "description": "Username or email"}, "password": {"type": "string", "title": "Password", "description": "User password"}}, "type": "object", "required": ["username", "password"], "title": "UserLogin", "description": "Schema for user login credentials.", "example": {"password": "secure_password123", "username": "johndoe"}}, "UserResponse": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "username": {"type": "string", "maxLength": 50, "minLength": 3, "pattern": "^[a-zA-Z0-9_-]+$", "title": "Username"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Full Name"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "id": {"type": "integer", "title": "Id"}, "is_superuser": {"type": "boolean", "title": "Is Superuser"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["email", "username", "id", "is_superuser", "created_at", "updated_at"], "title": "UserResponse", "description": "Schema for user responses (excludes sensitive data)."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VerificationRequestModel": {"properties": {"code": {"type": "string", "title": "Code"}, "agent_type": {"$ref": "#/components/schemas/AgentRole"}, "context": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Context"}}, "type": "object", "required": ["code", "agent_type"], "title": "VerificationRequestModel", "description": "Request model for code verification."}, "VersionComparison": {"properties": {"from_version": {"type": "string", "title": "From Version"}, "to_version": {"type": "string", "title": "To Version"}, "changes": {"additionalProperties": true, "type": "object", "title": "Changes"}, "added_items": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Added Items"}, "removed_items": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Removed Items"}, "modified_items": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Modified Items"}, "summary": {"type": "string", "title": "Summary"}}, "type": "object", "required": ["from_version", "to_version", "changes", "added_items", "removed_items", "modified_items", "summary"], "title": "VersionComparison", "description": "Schema for version comparison results."}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}